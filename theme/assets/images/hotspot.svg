<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_18235_57391)">
<circle cx="16" cy="16" r="16" fill="#14130E" fill-opacity="0.24"/>
<circle cx="16" cy="16" r="15.1375" stroke="white" stroke-opacity="0.5" stroke-width="1.725"/>
<g filter="url(#filter0_d_18235_57391)">
<circle cx="16" cy="16" r="4" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_18235_57391" x="8.55" y="9.4125" width="14.9" height="14.9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.8625"/>
<feGaussianBlur stdDeviation="1.725"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_18235_57391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_18235_57391" result="shape"/>
</filter>
<clipPath id="clip0_18235_57391">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
