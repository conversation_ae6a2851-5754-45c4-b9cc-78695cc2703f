<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: currentColor;
        stroke-width: .85px;
      }

      .cls-2 {
        fill: currentColor;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <path class="cls-1" d="M20,0h0c11.05,0,20,8.95,20,20h0c0,11.05-8.95,20-20,20h0C8.95,40,0,31.05,0,20h0C0,8.95,8.95,0,20,0Z" fill="currentColor"/>
  <g>
    <path class="cls-2" d="M15.44,17.14h-3.48v11.14h3.48v-11.14Z" fill="currentColor"/>
    <path class="cls-2" d="M24.84,16.89c-.13-.01-.27-.02-.4-.03-1.95-.08-3.05,1.08-3.43,1.58-.1.13-.15.22-.15.22v-1.49h-3.33v11.14h3.48v-5.24c0-.76-.06-1.56.32-2.25.32-.58.9-.87,1.55-.87,1.92,0,1.97,1.75,1.97,1.91v6.5h3.48v-7.27c0-2.49-1.26-3.96-3.48-4.2h0Z" fill="currentColor"/>
    <path class="cls-2" d="M15.12,15.1c.79-.79.79-2.07,0-2.87-.79-.79-2.06-.79-2.85,0-.79.79-.79,2.07,0,2.87.79.79,2.06.79,2.85,0Z" fill="currentColor"/>
  </g>
</svg>