<?xml version="1.0" encoding="UTF-8"?>
<svg transform="rotate(180deg)" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 53.2 (72643) - https://sketchapp.com -->
    <title>arrow-pdp</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="22" cy="22" r="22"></circle>
        <filter x="-12.5%" y="-14.8%" width="129.5%" height="129.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.111894248 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="arrow-pdp" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Right_Arrow" transform="translate(26.000000, 26.000000) rotate(-180.000000) translate(-26.000000, -26.000000) translate(4.000000, 4.000000)">
            <polyline id="Path-2" stroke="#000" stroke-width="3" points="26.9685462 12.3632812 16.3272456 22.950959 27.5594731 32.3632813"></polyline>
        </g>
    </g>
</svg>
