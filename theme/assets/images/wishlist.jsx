import React from "react";

function WishlistIcon({ isActive = false, ...props }) {
  if (isActive) {
    // Filled black heart
    return (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          d="M10.5175 17.3418C10.2341 17.4418 9.76748 17.4418 9.48415 17.3418C7.06748 16.5168 1.66748 13.0752 1.66748 7.24183C1.66748 4.66683 3.74248 2.5835 6.30081 2.5835C7.81748 2.5835 9.15915 3.31683 10.0008 4.45016C10.8425 3.31683 12.1925 2.5835 13.7008 2.5835C16.2591 2.5835 18.3341 4.66683 18.3341 7.24183C18.3341 13.0752 12.9341 16.5168 10.5175 17.3418Z"
          fill="#1A1A1A"
          stroke="#1A1A1A"
          strokeWidth="1.56156"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }

  // Outline heart (not filled)
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g id="heart">
        <path id="Vector" d="M10.5175 17.3418C10.2341 17.4418 9.76748 17.4418 9.48415 17.3418C7.06748 16.5168 1.66748 13.0752 1.66748 7.24183C1.66748 4.66683 3.74248 2.5835 6.30081 2.5835C7.81748 2.5835 9.15915 3.31683 10.0008 4.45016C10.8425 3.31683 12.1925 2.5835 13.7008 2.5835C16.2591 2.5835 18.3341 4.66683 18.3341 7.24183C18.3341 13.0752 12.9341 16.5168 10.5175 17.3418Z" stroke="#1A1A1A" strokeWidth="1.56156" strokeLinecap="round" strokeLinejoin="round" />
      </g>
    </svg>
  );
}

export default WishlistIcon;
