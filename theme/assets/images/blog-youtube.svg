<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: currentColor;
        stroke-width: 0px;
      }

      .cls-2 {
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <path class="cls-1" d="M20,40C8.97,40,0,31.03,0,20S8.97,0,20,0s20,8.97,20,20-8.97,20-20,20ZM20,.83C9.43.83.83,9.43.83,20s8.6,19.17,19.17,19.17,19.17-8.6,19.17-19.17S30.57.83,20,.83Z"/>
  <g id="Page-1">
    <g id="Dribbble-Light-Preview">
      <g id="icons">
        <path id="youtube-_168_" data-name="youtube-[#168]" class="cls-2" d="M18.2,22.58v-5.6c1.99.94,3.53,1.84,5.35,2.82-1.5.83-3.36,1.77-5.35,2.79M29.29,14.19c-.34-.45-.93-.8-1.55-.92-1.83-.35-13.25-.35-15.08,0-.5.09-.94.32-1.33.67-1.61,1.49-1.11,9.5-.72,10.8.16.56.37.97.64,1.23.34.35.81.59,1.35.7,1.51.31,9.27.49,15.1.05.54-.09,1.01-.34,1.39-.71,1.49-1.49,1.39-9.95.19-11.82"/>
      </g>
    </g>
  </g>
</svg>