<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_4316_14908)">
<path d="M16.1 22.55L16 22.65L15.89 22.55C11.14 18.24 8 15.39 8 12.5C8 10.5 9.5 9 11.5 9C13.04 9 14.54 10 15.07 11.36H16.93C17.46 10 18.96 9 20.5 9C22.5 9 24 10.5 24 12.5C24 15.39 20.86 18.24 16.1 22.55Z" fill="currentcolor" fill-opacity="1"/>
<path d="M16.1 22.55L16 22.65L15.89 22.55C11.14 18.24 8 15.39 8 12.5C8 10.5 9.5 9 11.5 9C13.04 9 14.54 10 15.07 11.36H16.93C17.46 10 18.96 9 20.5 9C22.5 9 24 10.5 24 12.5C24 15.39 20.86 18.24 16.1 22.55ZM20.5 7C18.76 7 17.09 7.81 16 9.08C14.91 7.81 13.24 7 11.5 7C8.42 7 6 9.41 6 12.5C6 16.27 9.4 19.36 14.55 24.03L16 25.35L17.45 24.03C22.6 19.36 26 16.27 26 12.5C26 9.41 23.58 7 20.5 7Z" fill="inherit"/>
</g>
<defs>
<filter id="filter0_d_4316_14908" x="-2" y="0" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4316_14908"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4316_14908" result="shape"/>
</filter>
</defs>
</svg>
