import { useState, useCallback } from 'react';

const useSearchModal = () => {
    const [showMobileModal, setShowMobileModal] = useState(false);


    const openSearchModal = useCallback(() => {
        setShowMobileModal(true);
    }, []);

    const closeSearchModal = useCallback(() => {
        setShowMobileModal(false);
    }, []);

    return {
        showMobileModal,
        openSearchModal,
        closeSearchModal,
    };
};

export default useSearchModal; 