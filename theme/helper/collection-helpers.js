import {
  COLLECTIONS,
  COLLECTION_WITH_ITEMS,
} from "../queries/collectionsQuery";

/**
 * Fetches all products for a specific collection
 * @param {Object} fpi - FPI instance
 * @param {string} slug - Collection slug (e.g., "tops")
 * @param {Object} options - Additional options (pagination, sorting, filtering)
 * @returns {Promise} - Promise resolving to collection products
 */
export const fetchCollectionProducts = async (fpi, slug, options = {}) => {
  try {
    // Check if fpi is properly initialized
    if (!fpi || typeof fpi.executeGQL !== "function") {
      console.error("FPI instance is invalid or executeGQL is not available");
      return {
        collection: null,
        products: [],
        pagination: {},
        filters: [],
        sortOptions: [],
      };
    }

    const {
      pageSize = 100, // Fetch a large number to get "all" products
      pageNo = 1,
      sortOn = null,
      filters = null,
    } = options;

    const payload = {
      slug,
      first: pageSize,
      pageNo,
      pageType: "number",
    };

    if (sortOn) payload.sortOn = sortOn;
    if (filters) payload.query = filters;

    const response = await fpi.executeGQL(COLLECTION_WITH_ITEMS, payload, {
      skipStoreUpdate: true,
    });

    if (response.errors) {
      throw response.errors[0];
    }

    return {
      collection: response.data.collection,
      products: response.data.collectionItems.items || [],
      pagination: response.data.collectionItems.page || {},
      filters: response.data.collectionItems.filters || [],
      sortOptions: response.data.collectionItems.sort_on || [],
    };
  } catch (error) {
    console.error("Error fetching collection products:", error);
    throw error;
  }
};

/**
 * Fetches all collections
 * @param {Object} fpi - FPI instance
 * @param {Object} options - Additional options (pagination)
 * @returns {Promise} - Promise resolving to all collections
 */
export const fetchAllCollections = async (fpi, options = {}) => {
  try {
    // Check if fpi is properly initialized
    if (!fpi || typeof fpi.executeGQL !== "function") {
      console.error("FPI instance is invalid or executeGQL is not available");
      return {
        collections: [],
        pagination: {},
      };
    }

    const {
      pageSize = 100, // Fetch a large number to get "all" collections
      pageNo = 1,
    } = options;

    const payload = {
      pageNo,
      pageSize,
    };

    const response = await fpi.executeGQL(COLLECTIONS, payload);

    if (response.errors) {
      throw response.errors[0];
    }

    return {
      collections: response.data.collections.items || [],
      pagination: response.data.collections.page || {},
    };
  } catch (error) {
    console.error("Error fetching all collections:", error);
    throw error;
  }
};

// fetchCollectionProducts(top);
