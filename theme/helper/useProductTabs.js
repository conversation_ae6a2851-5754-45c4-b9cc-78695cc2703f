// hooks/useProductTabs.js

import { useMemo } from "react";
import { useLocation } from "react-router-dom";
import useHeader from "../components/header/useHeader";

const useProductTabs = ({ fpi }) => {
  const { HeaderNavigation } = useHeader(fpi);
  const location = useLocation();

  const searchParam = useMemo(() => {
    // const param = new URLSearchParams(location.search).get("q");
    // if (param) return param.toLowerCase();

    const pathSegments = location.pathname.split("/").filter(Boolean);
    return pathSegments[pathSegments.length - 1]?.toLowerCase() || "";
  }, [location.pathname]);

  const { tabs, activeTab } = useMemo(() => {
    if (!searchParam || HeaderNavigation?.length === 0) {
      return { tabs: [], activeTab: "" };
    }

    const mapTabs = (prefix, arr) =>
      arr.map((s) => ({
        display: s.display,
        // path: `${prefix}/${s.display.toLowerCase().replace(/\s+/g, "-")}`,
        path: s.display.toLowerCase().replace(/\s+/g, "-"),
      }));

    for (const l1 of HeaderNavigation) {
      if (l1.display.toLowerCase() === searchParam) {
        return {
          activeTab: l1.display.toLowerCase(),
          tabs: l1.sub_navigation
            ? mapTabs(l1.display.toLowerCase(), l1.sub_navigation)
            : [],
        };
      }
    }

    for (const l1 of HeaderNavigation) {
      for (const l2 of l1.sub_navigation || []) {
        if (l2.display.toLowerCase() === searchParam) {
          return {
            activeTab: l2.display.toLowerCase(),
            tabs: l2.sub_navigation
              ? mapTabs(
                  `${l1.display.toLowerCase()}/${l2.display.toLowerCase()}`,
                  l2.sub_navigation
                )
              : [],
          };
        }
      }
    }

    for (const l1 of HeaderNavigation) {
      for (const l2 of l1.sub_navigation || []) {
        for (const l3 of l2.sub_navigation || []) {
          if (l3.display.toLowerCase() === searchParam) {
            return { activeTab: l3.display.toLowerCase(), tabs: [] };
          }
        }
      }
    }

    return { tabs: [], activeTab: "" };
  }, [HeaderNavigation, searchParam]);

  return { tabs, activeTab };
};

export default useProductTabs;
