import React, { useEffect, useMemo, useRef, useState, Suspense } from "react";
import { FDKLink } from "fdk-core/components";
import { useGlobalStore, useFPI } from "fdk-core/utils";
import Slider from "react-slick";
import ProductCard from "../components/product-card/product-card";
import styles from "../styles/sections/restock.less";
import FyImage from "../components/core/fy-image/fy-image";
import "fdk-react-templates/components/core/fy-image/fy-image.css";
import SliderRightIcon from "../assets/images/glide-arrow-right.svg";
import SliderLeftIcon from "../assets/images/glide-arrow-left.svg";
import ArrowRightIcon from "../assets/images/arrow-right.svg";
import { useMobile } from "../helper/hooks/useMobile";
import {
  FEATURED_COLLECTION,
  COLLECTION_ITEMS,
} from "../queries/collectionsQuery";
import Vector from "../assets/images/vector.svg";
import "../components/product-card/product-card.less";
import placeholderProduct from "../assets/images/placeholder/featured-collection-product.png";
import useAddToCartModal from "../page-layouts/plp/useAddToCartModal";
import {
  useViewport,
  useAccounts,
  useWishlist,
  useThemeFeature,
  useWindowWidth,
} from "../helper/hooks";
import { getProductImgAspectRatio } from "../helper/utils";

const Modal = React.lazy(
  () => import("fdk-react-templates/components/core/modal/modal")
);
const AddToCart = React.lazy(
  () =>
    import(
      "fdk-react-templates/page-layouts/plp/Components/add-to-cart/add-to-cart"
    )
);
const SizeGuide = React.lazy(
  () =>
    import(
      "fdk-react-templates/page-layouts/plp/Components/size-guide/size-guide"
    )
);

export function Component({ props, blocks, globalConfig }) {
  const fpi = useFPI();
  const customValues = useGlobalStore(fpi?.getters?.CUSTOM_VALUE);
  const CONFIGURATION = useGlobalStore(fpi.getters.CONFIGURATION);
  const listingPrice =
    CONFIGURATION?.app_features?.common?.listing_price?.value || "range";

  const isMobile = useViewport(0, 480);
  const windowWidth = useWindowWidth();
  const locationDetails = useGlobalStore(fpi?.getters?.LOCATION_DETAILS);
  const i18nDetails = useGlobalStore(fpi.getters.i18N_DETAILS);

  const { title, button_text, image, image_mobile } = props;

  const [isLoading, setIsLoading] = useState(true);
  const [collectionsData, setCollectionsData] = useState({});
  const [activeCollectionIndex, setActiveCollectionIndex] = useState(0);
  const [collectionDetails, setCollectionDetails] = useState([]);
  const [heights, setHeights] = useState({});
  const contentRefs = useRef({});


  const addToCartConfigs = {
    mandatory_pincode: false,
    hide_single_size: false,
    preselect_size: false,
  };

  const { isInternational } = useThemeFeature({ fpi });
  const addToCartModalProps = useAddToCartModal({
    fpi,
    pageConfig: addToCartConfigs,
  });
  const { isLoggedIn, openLogin } = useAccounts({ fpi });
  const { toggleWishlist, followedIdList } = useWishlist({ fpi });

  const {
    handleAddToCart,
    isOpen: isAddToCartOpen,
    showSizeGuide,
    handleCloseSizeGuide,
    ...restAddToModalProps
  } = addToCartModalProps;

  const showAddToCart = !isInternational && !globalConfig?.disable_cart;

  // Fetch collection data for each block
  useEffect(() => {
    const fetchCollectionData = async () => {
      if (!blocks || blocks.length === 0) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      // console.log("Fetching data for blocks:", blocks);

      try {
        const fetchPromises = blocks.map(async (block, index) => {
          if (block.props.collection?.value) {
            const payload = {
              slug: block.props.collection.value,
              first: 12,
              pageNo: 1,
            };

            // console.log(`Fetching collection: ${block.props.collection.value}`);

            try {
              // First get the collection details with total count
              const collectionRes = await fpi.executeGQL(COLLECTION_ITEMS, {
                slug: block.props.collection.value,
                first: 1,
                pageNo: 1,
              });

              // Then get the products for display
              const res = await fpi.executeGQL(FEATURED_COLLECTION, payload);
              // console.log("res", res);

              const totalCount =
                collectionRes.data?.collectionItems?.page?.item_total || 0;

              return {
                blockId: block.id || `block_${index}`,
                collectionSlug: block.props.collection.value,
                collectionName:
                  res.data?.collection?.name || block.props.collection.value,
                productCount:
                  res.data?.collection?.products?.items?.length || 0,
                totalProductCount: totalCount,
                products: res.data?.collection?.products?.items || [],
              };
            } catch (err) {
              console.error(
                `Error fetching collection ${block.props.collection.value}:`,
                err
              );
              return {
                blockId: block.id || `block_${index}`,
                collectionSlug: block.props.collection.value,
                collectionName: block.props.collection.value,
                productCount: 0,
                totalProductCount: 0,
                products: [],
              };
            }
          }
          return null;
        });

        const results = await Promise.all(fetchPromises);
        // console.log("All collection results:", results);

        const newCollectionsData = {};
        const newCollectionDetails = [];

        results.forEach((result) => {
          if (result && result.collectionSlug) {
            // Use collection slug as key for easier access
            newCollectionsData[result.collectionSlug] = result.products;
            newCollectionDetails.push({
              blockId: result.blockId,
              name: result.collectionName,
              slug: result.collectionSlug,
              productCount: result.productCount,
              totalProductCount: result.totalProductCount,
            });
          }
        });

        // console.log("Final collections data:", newCollectionsData);
        // console.log("Final collection details:", newCollectionDetails);

        setCollectionsData(newCollectionsData);
        setCollectionDetails(newCollectionDetails);

        // Reset to first collection when data changes
        setActiveCollectionIndex(0);
      } catch (error) {
        console.error("Error in fetchCollectionData:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCollectionData();
  }, [blocks, locationDetails?.pincode, i18nDetails?.currency?.code]);

  // Get products for the active collection only
  const activeProducts = useMemo(() => {
    if (
      collectionDetails.length === 0 ||
      Object.keys(collectionsData).length === 0
    ) {
      // console.log("No collection details or data available");
      return [];
    }

    const activeCollection = collectionDetails[activeCollectionIndex];
    if (!activeCollection) {
      // console.log(
      //   "No active collection found at index:",
      //   activeCollectionIndex
      // );
      return [];
    }

    const products = collectionsData[activeCollection.slug] || [];

    // console.log("Active Collection:", activeCollection.name);
    // console.log("Active Collection Slug:", activeCollection.slug);
    // console.log("Active Collection Products:", products.length);
    // console.log("Products:", products);

    return products;
  }, [collectionsData, activeCollectionIndex, collectionDetails]);

  useEffect(() => {
    const newHeights = {};
    collectionDetails.forEach((_, index) => {
      const contentEl = contentRefs.current[index];
      if (contentEl) {
        // newHeights[index] = contentEl.scrollHeight;
        newHeights[index] = 297;
      }
    });
    setHeights(newHeights);
  }, [collectionDetails,activeCollectionIndex]);

  // Get active collection slug for the "View All" button
  const activeCollectionSlug = useMemo(() => {
    if (collectionDetails.length === 0) return "";
    const activeCollection = collectionDetails[activeCollectionIndex];
    return activeCollection ? activeCollection.slug : "";
  }, [collectionDetails, activeCollectionIndex]);

  // Handle collection tab click
  const handleCollectionChange = (index) => {
    // console.log("Changing to collection index:", index);
    // console.log("Collection details:", collectionDetails[index]);
    setActiveCollectionIndex(index);
  };

  // Slider configuration
  const sliderConfig = useMemo(() => {
    const itemLength = activeProducts?.length || 0;
    return {
      dots: false,
      arrows: false,
      infinite: false,
      speed: 300,
      slidesToShow: 4,
      slidesToScroll: 1,
      swipeToSlide: true,
      autoplay: false,
      cssEase: "linear",
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      responsive: [
        {
          breakpoint: 780,
          settings: {
            arrows: false,
            slidesToShow: 3,
            slidesToScroll: 1,
            dots: false,
            swipe: true,
            swipeToSlide: true,
            touchThreshold: 80,
            draggable: false,
            touchMove: true,
          },
        },
        {
          breakpoint: 480,
          settings: {
            arrows: false,
            slidesToShow: 2,
            slidesToScroll: 1,
            dots: false,
            swipe: true,
            swipeToSlide: true,
            draggable: false,
            touchMove: true,
          },
        },
      ],
    };
  }, [activeProducts?.length]);

  const handleWishlistToggle = (data) => {
    if (!isLoggedIn) {
      openLogin();
      return;
    }
    toggleWishlist(data);
  };

  const imgSrcSet = useMemo(() => {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      { breakpoint: { min: 481 }, width: 300 },
      { breakpoint: { max: 480 }, width: 500 },
    ];
  }, [globalConfig?.img_hd]);

  const getImgSrcSet = () => {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      { breakpoint: { min: 1728 }, width: 1691 },
      { breakpoint: { min: 1512 }, width: 1487 },
      { breakpoint: { min: 1296 }, width: 1282 },
      { breakpoint: { min: 1080 }, width: 1069 },
      { breakpoint: { min: 900 }, width: 897 },
      { breakpoint: { min: 720 }, width: 1530 },
      { breakpoint: { min: 540 }, width: 1170 },
      { breakpoint: { min: 360 }, width: 810 },
      { breakpoint: { min: 180 }, width: 450 },
    ];
  };

  // Debug logging
  // console.log("Current state:", {
  //   isLoading,
  //   activeCollectionIndex,
  //   collectionDetails,
  //   activeProducts: activeProducts.length,
  //   collectionsDataKeys: Object.keys(collectionsData),
  // });\

  const bannerRef = useRef(null);

  useEffect(() => {
    console.log("isMobile", isMobile);
  }, [isMobile]);

  return !isMobile ? (
    <div style={{ backgroundImage: `url(${image?.value})` }}>
      <section className={styles.sectionWrapper}>
        {/* Header Section */}
        <div className={`fx-title-block ${styles.titleBlock}`}>
          {title?.value && (
            <h2 className={`fx-title ${styles.sectionHeading} fontHeader`}>
              {title.value}
            </h2>
          )}
        </div>

        {/*  active tab karta less then to  >>> index+1 */}
        {/* {index } =99999 */}
        {/* index >activeIndex =>>> 2index -activeIndex */}

        {/* Collection Tabs */}
        {collectionDetails.length > 0 && (
          <div className={styles.collectionTabs}>
            <div className={styles.tabsContainer}>
              {collectionDetails.map((collection, index) => {
                const isActive = activeCollectionIndex === index;
                const isLeftOfActive = index < activeCollectionIndex;
                const isRightOfActive = index > activeCollectionIndex;

                const classNames = [
                  styles.tabButton,
                  isActive ? styles.activeTab : "",
                  isLeftOfActive ? styles.leftOfActive : "",
                  isRightOfActive ? styles.rightOfActive : "",
                ]
                  .filter(Boolean)
                  .join(" ");

                return (
                  <button
                    key={`${collection.slug}_${index}`}
                    className={classNames}
                    style={{
                      zIndex: isActive
                        ? 20
                        : isLeftOfActive
                          ? index + 1
                          : Math.max(1, collectionDetails?.length - index),
                      boxShadow: `-10px 0 15px rgba(0, 0, 0, 0.3), 
                      10px 0 15px rgba(0, 0, 0, 0.3)`,
                    }}
                    onClick={() => handleCollectionChange(index)}
                  >
                    {collection.name}
                    {collection.totalProductCount > 0 && (
                      <span className={styles.productCount}>
                        ({collection.totalProductCount})
                      </span>
                    )}
                  </button>
                );
              })}
            </div>
            {button_text?.value && activeCollectionSlug && (
              <FDKLink to={`/collection/${activeCollectionSlug}`}>
                <button className={styles.viewAllTab}>
                  {button_text.value}
                </button>
              </FDKLink>
            )}
          </div>
        )}
        {/* Products Horizontal Scroll - Only Active Collection */}
        {!isLoading && activeProducts.length > 0 && (
          <div className={`${styles.productSlider} ${styles.cleanCarousel}`}>
            <Slider
              ref={bannerRef}
              {...sliderConfig}
              key={`slider_${activeCollectionIndex}_${activeProducts.length}`}
            >
              {activeProducts.map((product, index) => (
                <ProductCardItem
                  key={`${activeCollectionIndex}_${product.uid || product.id}_${index}`}
                  className={styles.sliderItem}
                  product={product}
                  imgSrcSet={imgSrcSet}
                  listingPrice={listingPrice}
                  globalConfig={globalConfig}
                  showAddToCart={showAddToCart}
                  followedIdList={followedIdList}
                  isSlider={true}
                  handleWishlistToggle={handleWishlistToggle}
                  handleAddToCart={handleAddToCart}
                />
              ))}
            </Slider>

            {/* Updated Navigation Arrows */}
            <div className={styles.carouselNavigation}>
              <button
                className={`${styles.carouselNavBtn} ${styles.prevBtn}`}
                onClick={() => bannerRef.current.slickPrev()}
              >
                <ArrowRightIcon className={styles.prevIcon} />
              </button>
              <button
                className={`${styles.carouselNavBtn} ${styles.nextBtn}`}
                onClick={() => bannerRef.current.slickNext()}
              >
                <ArrowRightIcon />
              </button>
            </div>
          </div>
        )}
        {/* No Products Message */}
        {!isLoading &&
          activeProducts.length === 0 &&
          collectionDetails.length > 0 && (
            <div className={styles.noProductsMessage}>
              <p>No products available in this collection.</p>
            </div>
          )}
        {/* Loading State */}
        {isLoading && (
          <div className={styles.loadingContainer}>
            <div className={styles.slideWrap}>
              <div
                className={styles.placeholderScroll}
                style={{ "--per_row": 4, "--per_row_mobile": 2 }}
              >
                {Array.from({ length: 4 }, (_, index) => index).map((index) => (
                  <div key={index} style={{ padding: "0 12px" }}>
                    <FyImage
                      customClass={styles.imageGallery}
                      src={placeholderProduct}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </section>
    </div>
  ) : (
    <div style={{ backgroundImage: `url(${image_mobile?.value})` }}>
      <section className={styles.sectionWrapper}>
        {/* Mobile Header Section */}
        <div className={styles.mobiletitleBlock}>
          <h2 className={`fx-title ${styles.mobilesectionHeading} fontHeader`}>
            {title.value}
          </h2>
          <span className={styles.mobileTitleSpan}> 再入荷 </span>
        </div>

        {/* Mobile Collection Cards */}
        {collectionDetails.length > 0 && (
          <div className={styles.mobilecollectionContainer}>
            {collectionDetails.map((collection, index) => {
              const isActive = activeCollectionIndex === index;
              const isLeftOfActive = index < activeCollectionIndex;
              const isRightOfActive = index > activeCollectionIndex;
              return (
                <div
                  key={`${collection.slug}_${index}`}
                  className={styles.mobilecollectionCard}
                  style={{
                    zIndex: isActive
                      ? 20
                      : isLeftOfActive
                        ? index + 1
                        : Math.max(1, collectionDetails?.length - index),
                    margin: isActive ? "0px -10px -25px" : "0 0 -25px",
                  }}
                >
                  {/* Collection Header */}
                  <div
                    className={`${styles.mobilecollectionHeader} ${isActive ? styles.mobileactiveHeader : ""}`}
                    onClick={() => handleCollectionChange(index)}
                  >
                    <div className={styles.mobilecollectionInfo}>
                      <h3 className={styles.mobilecollectionName}>
                        {collection.name}
                      </h3>
                      {collection.totalProductCount > 0 && (
                        <span className={styles.mobileproductCount}>
                          ({collection.totalProductCount})
                        </span>
                      )}
                    </div>
                    {button_text?.value && isActive && (
                      <FDKLink to={`/collection/${activeCollectionSlug}`}>
                        <button className={styles.mobileviewAllBtn}>
                          {button_text.value}
                        </button>
                      </FDKLink>
                    )}
                  </div>

                  {/* Products Section - Only show for active collection */}

                  <div
                    className={styles.mobileproductsContainer}
                    style={{
                      height: isActive ? `${heights[index]}px` || "auto" : "0px",
                      // opacity: isActive ? 1 : 0,
                    }}
                  >
                    <div
                      ref={(el) => (contentRefs.current[index] = el)}
                      className={styles.mobileproductsContent}
                    >
                      {/* Products Section - Always render but control visibility with container */}
                      {!isLoading && activeProducts.length > 0 && isActive && (
                        <div
                          className={`${styles.mobileproductSlider} ${styles.mobilecleanCarousel}`}
                        >
                          <Slider
                            ref={bannerRef}
                            {...sliderConfig}
                            key={`slider_${activeCollectionIndex}_${activeProducts.length}`}
                          >
                            {activeProducts.map((product, productIndex) => (
                              <ProductCardItem
                                key={`${activeCollectionIndex}_${product.uid || product.id}_${productIndex}`}
                                className={styles.mobilesliderItem}
                                product={product}
                                imgSrcSet={imgSrcSet}
                                listingPrice={listingPrice}
                                globalConfig={globalConfig}
                                showAddToCart={showAddToCart}
                                followedIdList={followedIdList}
                                isSlider={true}
                                handleWishlistToggle={handleWishlistToggle}
                                handleAddToCart={handleAddToCart}
                              />
                            ))}
                          </Slider>
                        </div>
                      )}

                      {/* No Products Message */}
                      {!isLoading &&
                        activeProducts.length === 0 &&
                        isActive && (
                          <div className={styles.mobilenoProductsMessage}>
                            <p>No products available in this collection.</p>
                          </div>
                        )}

                      {/* Loading State */}
                      {isLoading && isActive && (
                        <div className={styles.mobileloadingContainer}>
                          <div className={styles.mobileslideWrap}>
                            <div
                              className={styles.mobileplaceholderScroll}
                              style={{ "--per_row": 4, "--per_row_mobile": 2 }}
                            >
                              {Array.from(
                                { length: 4 },
                                (_, index) => index
                              ).map((index) => (
                                <div key={index} style={{ padding: "0 12px" }}>
                                  <FyImage
                                    customClass={styles.mobileimageGallery}
                                    src={placeholderProduct}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </section>
    </div>
  );
}

{
  /* Add to Cart Modal
      {showAddToCart && (
        <>
          {isAddToCartOpen && (
            <Suspense fallback={<div />}>
              <Modal
                isOpen={isAddToCartOpen}
                hideHeader={!isTablet}
                bodyClassName={styles.addToCartBody}
                title={
                  isTablet
                    ? restAddToModalProps?.productData?.product?.name
                    : ""
                }
                closeDialog={restAddToModalProps?.handleClose}
                containerClassName={styles.addToCartContainer}
              >
                <AddToCart
                  {...restAddToModalProps}
                  globalConfig={globalConfig}
                />
              </Modal>
            </Suspense>
          )}
          {showSizeGuide && (
            <Suspense fallback={<div />}>
              <SizeGuide
                isOpen={showSizeGuide}
                onCloseDialog={handleCloseSizeGuide}
                productMeta={restAddToModalProps?.productData?.product?.sizes}
              />
            </Suspense>
          )}
        </>
      )} */
}

const ProductCardItem = ({
  className = "",
  product,
  imgSrcSet,
  listingPrice,
  isSlider = false,
  globalConfig,
  showAddToCart = false,
  followedIdList,
  handleWishlistToggle,
  handleAddToCart,
}) => {
  const columnCount = {
    desktop: 4,
    tablet: 3,
    mobile: 2,
  };

  return (
    <div className={className}>
      <FDKLink to={`/product/${product.slug}`}>
        <ProductCard
          product={product}
          listingPrice={listingPrice}
          isSaleBadgeDisplayed={false}
          showProductTitle={true}
          showBadge={true}
          columnCount={columnCount}
          isWishlistDisplayed={true}
          onWishlistClick={handleWishlistToggle}
          followedIdList={followedIdList}
          isWishlistIcon={true}
          isImageFill={true}
          isPrice={true}
          aspectRatio={getProductImgAspectRatio(globalConfig)}
          imagePlaceholder={placeholderProduct}
          showAddToCart={showAddToCart}
          handleAddToCart={handleAddToCart}
          imgSrcSet={imgSrcSet}
          isSlider={isSlider}
        />
      </FDKLink>
    </div>
  );
};

export const settings = {
  label: "Restock",
  props: [
    {
      type: "text",
      id: "title",
      default: "Restock",
      label: "Heading",
    },
    {
      type: "text",
      id: "button_text",
      default: "view all",
      label: "Button Text",
    },
    {
      type: "image_picker",
      id: "image",
      label: "Image",
      info: "Select an image to display",
      options: {
        aspect_ratio: "120:71",
        aspect_ratio_strict_check: true,
      },
    },
    {
      type: "image_picker",
      id: "image_mobile",
      label: "Image Mobile",
      info: "Select an image to display",
      options: {
        aspect_ratio: "375:814",
        aspect_ratio_strict_check: true,
      },
    },
  ],
  blocks: [
    {
      name: "Collection Item",
      type: "collection",
      props: [
        {
          type: "collection",
          id: "collection",
          label: "Collection",
          info: "Select a collection to display its products",
        },
      ],
    },
  ],
};

Component.serverFetch = async ({ fpi, props, blocks, id }) => {
  try {
    if (blocks && blocks.length > 0) {
      const fetchPromises = blocks.map(async (block) => {
        if (block.props.collection?.value) {
          const payload = {
            slug: block.props.collection.value,
            first: 12,
            pageNo: 1,
          };
          return await fpi
            .executeGQL(FEATURED_COLLECTION, payload)
            .then((res) => {
              return fpi.custom.setValue(
                `featuredCollectionData-${block.props.collection.value}`,
                res
              );
            });
        }
      });
      await Promise.all(fetchPromises);
    }
  } catch (err) {
    console.log(err);
  }
};

export default Component;
