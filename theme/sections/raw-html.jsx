import React from "react";

export function Component({ props }) {
  const { code, padding_top, padding_bottom } = props;

  const dynamicStyles = {
    paddingTop: `${padding_top?.value ?? 16}px`,
    paddingBottom: `${padding_bottom?.value ?? 16}px`,
  };

  return !code?.value ? null : (
    <section
      className="basePageContainer margin0auto"
      dangerouslySetInnerHTML={{ __html: code.value }}
      style={dynamicStyles}
    />
  );
}

export const settings = {
  label: "Custom HTML",
  props: [
    {
      id: "code",
      label: "Your Code Here",
      type: "code",
      default: "",
      info: "Add Your custom HTML Code below. You can also use the full screen icon to open a code editor and add your code",
    },
    {
      type: "range",
      id: "padding_top",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Top padding",
      default: 16,
      info: "Top padding for section",
    },
    {
      type: "range",
      id: "padding_bottom",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Bottom padding",
      default: 16,
      info: "Bottom padding for section",
    },
  ],
  blocks: [],
};
export default Component;
