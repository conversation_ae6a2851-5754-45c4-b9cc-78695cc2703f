import React, { useEffect, useRef, useState } from "react";
import { useSnapCarousel } from "react-snap-carousel";
import { useViewport } from "../helper/hooks";
import styles from "../styles/sections/superdry-origin.less";
import { FDKLink } from "fdk-core/components";
import Vector from "../assets/images/vector.svg";

export function Component({ props, blocks }) {
  const {
    title,
    description,
    button_text,
    text_alignment,
    speed,
    banner_link,
    img_container_bg,
    text_color,
  } = props;
  const { scrollRef, pages, activePageIndex, next, goTo } = useSnapCarousel();

  const [progressMap, setProgressMap] = useState({});
  const isMobileView = useViewport(0, 480);

  const handleProgressUpdate = (index, progress) => {
    setProgressMap((prev) => ({ ...prev, [index]: progress }));
  };

  const mapAlignment = {
    top_left: {
      justifyContent: "unset",
      alignItems: "flex-start",
      textAlign: "left",
    },
    top_center: {
      justifyContent: "unset",
      alignItems: "center",
      textAlign: "center",
    },
    top_right: {
      justifyContent: "unset",
      alignItems: "flex-end",
      textAlign: "right",
    },
    center_center: {
      justifyContent: "center",
      alignItems: "center",
      textAlign: "center",
    },
    center_left: {
      justifyContent: "center",
      alignItems: "flex-start",
      textAlign: "left",
    },
    center_right: {
      justifyContent: "center",
      alignItems: "flex-end",
      textAlign: "right",
    },
    bottom_left: {
      justifyContent: "flex-end",
      alignItems: "flex-start",
      textAlign: "left",
    },
    bottom_right: {
      justifyContent: "flex-end",
      alignItems: "flex-end",
      textAlign: "right",
    },
    bottom_center: {
      justifyContent: "flex-end",
      alignItems: "center",
      textAlign: "center",
    },
  };

  return (
    <section className={`${styles.media_text}`} style={{ backgroundColor: img_container_bg }}>
      <div className={styles.carouselContainer}>
        <div className={styles.carouselItems} ref={scrollRef}>
          {blocks?.map((block, index) => (
            <div key={index} className={styles.carouselItem}>
              <ImageCarousel
                blocks={block}
                isMobileView={isMobileView}
                isActive={activePageIndex === index}
                onProgressUpdate={(progress) =>
                  handleProgressUpdate(index, progress)
                }
                speed={speed?.value}
                next={next}
                goTo={goTo}
                activePageIndex={activePageIndex}
                totalPages={pages.length}
              />
              {/* Progress bar moved inside the carousel item */}
            </div>
          ))}
        </div>
        <div className={styles.carouselProgressBarWrapper}>
          {blocks.map((_, i) => (
            <div
              key={i}
              className={styles.progressTrack}
              onClick={() => {
                if (activePageIndex !== i) goTo(i);
              }}
            >
              <div
                className={styles.progressFill}
                style={{
                  width:
                    i < activePageIndex
                      ? "100%"
                      : i === activePageIndex
                        ? `${progressMap[i] || 0}%`
                        : "0%",
                }}
              />
            </div>
          ))}
        </div>
      </div>
      <div
        className={styles.media_text__info}
        style={mapAlignment[text_alignment?.value]}
      >
        {title?.value && (
          <div className={styles.mobile_title}>
          <h2 className={`fx-title ${styles.media_text__heading} fontHeader`}>
            {title?.value}
          </h2>
          </div>
        )}
        {description?.value && (
          <p className={`fx-description ${styles.media_text__description}`}>
            {description?.value}
          </p>
        )}
        {button_text?.value && (
          <div className={styles.mobile_button}>
          <FDKLink
            className={`fx-button ${styles.media_text__cta} btnSecondary`}
            to={banner_link?.value}
          >
            {button_text?.value}
            <Vector />
          </FDKLink>
          </div>
        )}
      </div>
    </section>
  );
}

const ImageCarousel = ({
  blocks,
  isActive,
  isMobileView,
  onProgressUpdate,
  speed,
  next,
  goTo,
  activePageIndex,
  totalPages,
}) => {
  const imageFile = isMobileView
    ? blocks.props.image_mobile
    : blocks.props.image_desktop;
  const progressRef = useRef(0);
  const intervalRef = useRef(null);

  useEffect(() => {
    if (!isActive) {
      progressRef.current = 0;
      onProgressUpdate(0);
      return;
    }

    const duration = (speed || 3) * 1000;
    const interval = 50;
    const step = (interval / duration) * 100;

    progressRef.current = 0;
    onProgressUpdate(0);

    intervalRef.current = setInterval(() => {
      progressRef.current += step;
      const clampedProgress = Math.min(progressRef.current, 100);
      onProgressUpdate(clampedProgress);

      if (clampedProgress >= 100) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;

        if (activePageIndex === totalPages - 1) {
          goTo(0); // Wrap around to first slide
        } else {
          next(); // Go to next slide
          // console.log("NEXTTTTTTTTTTTTT", "interval");
        }
      }
    }, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isActive, activePageIndex, totalPages]);

  if (!isActive) {
    return <></>;
  }

  return (
    // <div className={styles.carouselItemContent}>
    <>
      {imageFile?.value && (
        <img
          src={imageFile?.value}
          style={{ borderRadius: "30px" }}
          alt="Carousel slide"
        />
      )}
    </>
    // </div>
  );
};

export const settings = {
  label: "superdry-origin",
  props: [
    {
      id: "text_alignment",
      type: "select",
      options: [
        {
          value: "top_left",
          text: "Top Left",
        },
        {
          value: "top_center",
          text: "Top Center",
        },
        {
          value: "top_right",
          text: "Top Right",
        },
        {
          value: "center_center",
          text: "Center Center",
        },
        {
          value: "center_left",
          text: "Center Left",
        },
        {
          value: "center_right",
          text: "Center Right",
        },
        {
          value: "bottom_left",
          text: "Bottom Left",
        },
        {
          value: "bottom_right",
          text: "Bottom Right",
        },
        {
          value: "bottom_center",
          text: "Bottom Center",
        },
      ],
      default: "center_left",
      label: "Text Alignment (Desktop)",
      info: "Set text alignment for desktop",
    },
    {
      type: "text",
      id: "title",
      default: "",
      label: "Heading",
    },
    {
      type: "textarea",
      id: "description",
      default: "",
      label: "Description",
    },
    {
      type: "url",
      id: "banner_link",
      default: "",
      label: "Redirect Link",
    },
    {
      type: "text",
      id: "button_text",
      default: "",
      label: "Button Text",
    },
    {
      type: "range",
      id: "speed",
      min: 1,
      max: 6,
      step: 1,
      default: 1,
      label: "Speed of Autoplay",
      info: "Speed is only image",
    },
    {
      type: "color",
      id: "img_container_bg",
      category: "Image Container",
      default: "#00000000",
      label: "Container Background Color",
      info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
    },
    {
      type: "color",
      id: "text_color",
      category: "Text",
      default: "#00000000",
      label: "Text Color",
      info: "This color will be used as the text color of the section",
    }
  ],

  blocks: [
    {
      name: "SuperdryOrigin",
      type: "Superdry-Origin",
      props: [
        {
          type: "image_picker",
          id: "image_desktop",
          label: "Desktop Image",
          default: "",
          options: {
            aspect_ratio: "117:143",
            aspect_ratio_strict_check: true,
          },
        },
        {
          type: "image_picker",
          id: "image_mobile",
          label: "mobile Image",
          default: "",
          options: {
            aspect_ratio: "113:138",
            aspect_ratio_strict_check: true,
          },
        },
      ],
    },
  ],
};

export default Component;
