import React, { useEffect, useMemo } from "react";
import Slider from "react-slick";
import styles from "../styles/sections/collections-listing.less";
import SliderRightIcon from "../assets/images/glide-arrow-right.svg";
import SliderLeftIcon from "../assets/images/glide-arrow-left.svg";
import { COLLECTION } from "../queries/collectionsQuery";
import { useGlobalStore, useFPI } from "fdk-core/utils";
import placeholderImage from "../assets/images/placeholder/collections-listing.png";
import CollectionCard from "../components/collection-card/collection-card";

export function Component({ props, blocks, globalConfig, id: sectionId }) {
  const fpi = useFPI();
  const {
    heading,
    description,
    layout_mobile,
    layout_desktop,
    per_row,
    img_container_bg,
    padding_top,
    padding_bottom,
  } = props;

  const itemsPerRow = Number(per_row?.value ?? 7);

  const customValue = useGlobalStore(fpi?.getters?.CUSTOM_VALUE) ?? {};
  const collectionIds = useMemo(() => {
    return (
      blocks?.reduce(
        (acc, b) =>
          b?.props?.collection?.value
            ? [...acc, b?.props?.collection?.value]
            : acc,
        []
      ) || []
    );
  }, [blocks]);
  const customSectionId = collectionIds?.join("__");
  const collections = customValue[`collectionData-${customSectionId}`] || [];
  // console.log("collection", collection.image,collection.name)
  useEffect(() => {
    const fetchCollections = async () => {
      try {
        const promisesArr = collectionIds?.map((slug) =>
          fpi.executeGQL(COLLECTION, {
            slug: slug.split(" ").join("-"),
          })
        );
        const responses = await Promise.all(promisesArr);
        fpi.custom.setValue(`collectionData-${customSectionId}`, responses);
      } catch (err) {
        // console.log(err);
      }
    };
    if (!collections?.length && collectionIds?.length) {
      fetchCollections();
    }
  }, [collectionIds]);

  const isDemoBlock = () => {
    if (
      collectionsForScrollView?.length > 0 ||
      collectionsForStackedView?.length > 0
    ) {
      return false;
    }
    const collections =
      blocks?.reduce(
        (acc, b) =>
          b?.props?.collection?.value
            ? [...acc, b?.props?.collection?.value]
            : acc,
        []
      ) || [];
    return collections?.length === 0;
  };

  const getImgSrcSet = () => {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      {
        breakpoint: { min: 1728 },
        width: Math.round(3564 / itemsPerRow),
      },
      { breakpoint: { min: 1512 }, width: Math.round(3132 / itemsPerRow) },
      { breakpoint: { min: 1296 }, width: Math.round(2700 / itemsPerRow) },
      { breakpoint: { min: 1080 }, width: Math.round(2250 / itemsPerRow) },
      { breakpoint: { min: 900 }, width: Math.round(1890 / itemsPerRow) },
      { breakpoint: { min: 720 }, width: Math.round(1530 / 3) },
      { breakpoint: { min: 540 }, width: Math.round(1170 / 3) },
      { breakpoint: { min: 360 }, width: Math.round(810) },
      { breakpoint: { min: 180 }, width: Math.round(450) },
    ];
  };

  const collectionsForStackedView = useMemo(() => {
    return collections.slice(0, itemsPerRow * 2);
  }, [collections, itemsPerRow]);

  const collectionsForScrollView = useMemo(() => {
    return collections.slice(0, itemsPerRow * 4);
  }, [collections, itemsPerRow]);

  const isStackView =
    layout_mobile?.value === "stacked" || layout_desktop?.value === "grid";
  const isHorizontalView =
    layout_mobile?.value === "horizontal" ||
    layout_desktop?.value === "horizontal";

  const stackViewClassName = `${
    layout_mobile?.value === "horizontal" ? styles.hideOnTablet : ""
  } ${layout_desktop?.value === "horizontal" ? styles.hideOnDesktop : ""}`;

  const horizontalViewClassName = `${
    collectionsForScrollView?.length === 1 ? styles.singleItem : ""
  } ${layout_mobile?.value === "stacked" ? styles.hideOnTablet : ""} ${
    layout_desktop?.value === "grid" ? styles.hideOnDesktop : ""
  }`;

  const config = useMemo(
    () => ({
      arrows: collectionsForScrollView?.length > itemsPerRow,
      dots: collectionsForScrollView?.length > itemsPerRow,
      speed: collectionsForScrollView?.length / itemsPerRow > 2 ? 700 : 400,
      slidesToShow: itemsPerRow,
      slidesToScroll: itemsPerRow,
      swipeToSlide: true,
      autoplay: false,
      autoplaySpeed: 3000,
      infinite: collectionsForScrollView?.length > itemsPerRow,
      cssEase: "linear",
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      responsive: [
        {
          breakpoint: 1024,
          settings: {
            speed: 400,
            arrows: false,
            slidesToShow: 4,
            slidesToScroll: 1,
          },
        },
      ],
    }),
    [collectionsForScrollView?.length, itemsPerRow]
  );

  const configMobile = useMemo(
    () => ({
      dots: false,
      speed: 400,
      slidesToShow: 4,
      slidesToScroll: 1,
      swipeToSlide: true,
      autoplay: false,
      autoplaySpeed: 3000,
      infinite: false,
      arrows: false,
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      centerMode: false,
      centerPadding: "0px",
      cssEase: "linear",
    }),
    [collectionsForScrollView?.length]
  );

  const dynamicStyles = {
    paddingTop: `${padding_top?.value ?? 16}px`,
    paddingBottom: `${padding_bottom?.value ?? 16}px`,
    "--bg-color": `${img_container_bg?.value || "#00000000"}`,
    maxWidth: "100vw",
  };

  return (
    <section className={styles.collections__template}>
      <div className={`fx-title-block ${styles["section-title-block"]}`}>
        <h2 className={`fx-title ${styles["section-title"]} fontHeader`}>
          {heading?.value}
        </h2>
        <p className={`fx-description ${styles["section-description"]}`}>
          {description.value}
        </p>
      </div>
      <div className={styles.scrollContainer}>
        {collectionsForScrollView?.map((card, index) => (
          <CollectionItem
            key={`${card?.data?.collection?.name}_${index}`}
            collection={card?.data?.collection}
            props={props}
            srcset={getImgSrcSet()}
            defer={index >= itemsPerRow}
            className={styles.collectionItem}
          />
        ))}
      </div>
      {isStackView && (
        <div
          className={`${styles.collectionGrid} ${stackViewClassName}`}
          style={{ "--grid-columns": itemsPerRow }}
        >
          {collectionsForStackedView?.map((card, index) => (
            <CollectionItem
              key={`${card?.data?.collection?.name}_${index}`}
              collection={card?.data?.collection}
              // block={blocks[index]}
              props={props}
              srcset={getImgSrcSet()}
              defer={index >= itemsPerRow}
            />
          ))}
        </div>
      )}
      {/* {isHorizontalView && (
        <div
          className={`${styles.collectionSlider} ${horizontalViewClassName}`}
          style={{
            "--slick-dots": `${Math.ceil(collectionsForScrollView?.length / itemsPerRow) * 22 + 10}px`,
          }}
        >
          <Slider {...config} className={`${styles.hideOnMobile} `}>
            {collectionsForScrollView?.map((card, index) => (
              <CollectionItem
                className={styles.sliderItem}
                key={`${card?.data?.collection?.name}_${index}`}
                collection={card?.data?.collection}
                // block={blocks[index]}
                props={props}
                srcset={getImgSrcSet()}
                defer={index >= itemsPerRow}
              />
            ))}
          </Slider>
          <Slider {...configMobile} className={`${styles.showOnMobile} `}>
            {collectionsForScrollView?.map((card, index) => (
              <CollectionItem
                className={styles.sliderItem}
                key={`${card?.data?.collection?.name}_${index}`}
                collection={card?.data?.collection}
                // block={blocks[index]}
                props={props}
                srcset={getImgSrcSet()}
                defer={index >= 1}
              />
            ))}
          </Slider>
        </div>
      )} */}
      {isDemoBlock() && (
        <div className={`${styles.collectionGrid} ${styles.defaultGrid}`}>
          {["Featured Products", "New Arrivals", "Best Sellers"].map(
            (item, index) => (
              <CollectionItem
                key={`default_${index}`}
                collection={{ name: item }}
                // block={null}
                props={props}
                srcset={getImgSrcSet()}
                defer={false}
              />
            )
          )}
        </div>
      )}
    </section>
  );
}

const CollectionItem = ({
  className = "",
  props,
  collection,
  // block,
  srcset,
  defer = false,
}) => {
  const { img_fill, img_container_bg, button_text, name_placement } = props;

  // Get the custom image from block props, fallback to collection banner or placeholder
  // const getCollectionImage = () => {
  //   if (block?.props?.collectionimage?.value) {
  //     return block?.props?.collectionimage?.value;
  //   }
  //   return collection?.banners?.portrait?.url || placeholderImage;
  // };

  return (
    <div className={`fx-collection-card ${className}`}>
      <CollectionCard
        collectionName={collection?.name}
        collectionImage={collection?.banners?.portrait?.url || placeholderImage}
        collectionAction={collection?.action}
        buttonText={button_text?.value}
        isNameOverImage={name_placement?.value === "inside"}
        imageProps={{
          backgroundColor: img_container_bg?.value,
          isImageFill: img_fill?.value,
          aspectRatio: 1,
          sources: srcset,
          defer,
        }}
      />
    </div>
  );
};

export const settings = {
  label: "Collections Highlights",
  props: [
    {
      type: "text",
      id: "heading",
      default: "Explore Our Collections",
      label: "Heading",
      info: "Heading text of the section",
    },
    {
      type: "textarea",
      id: "description",
      default:
        "Organize your products into these collections to help customers easily find what they're looking for. Each category can showcase a different aspect of your store's offerings.",
      label: "Description",
      info: "Description text of the section",
    },
    {
      id: "layout_mobile",
      type: "select",
      options: [
        {
          value: "stacked",
          text: "Stack",
        },
        {
          value: "horizontal",
          text: "Horizontal",
        },
      ],
      default: "horizontal",
      label: "Layout(Mobile)",
      info: "Alignment of content",
    },
    {
      id: "layout_desktop",
      type: "select",
      options: [
        {
          value: "grid",
          text: "Stack",
        },
        {
          value: "horizontal",
          text: "Horizontal",
        },
      ],
      default: "horizontal",
      label: "Layout(Desktop)",
      info: "Alignment of content",
    },
    {
      type: "select",
      id: "name_placement",
      label: "Collection Title & Button Placement",
      default: "inside",
      info: "Place collection title and button inside or outside the image",
      options: [
        {
          value: "inside",
          text: "Inside the image",
        },
        {
          value: "outside",
          text: "Outside the image",
        },
      ],
    },
    {
      type: "text",
      id: "button_text",
      default: "Shop Now",
      label: "Button Text",
    },
    {
      type: "range",
      id: "per_row",
      label: "Display collections per row (desktop)",
      min: "3",
      max: "12",
      step: "1",
      info: "It'll not work for mobile layout",
      default: "7",
    },
    {
      type: "color",
      id: "img_container_bg",
      category: "Image Container",
      default: "#00000000",
      label: "Container Background Color",
      info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
    },
    {
      type: "checkbox",
      id: "img_fill",
      category: "Image Container",
      default: true,
      label: "Fit image to the container",
      info: "If the image aspect ratio is different from the container, the image will be clipped to fit the container. The aspect ratio of the image will be maintained",
    },
    {
      type: "range",
      id: "padding_top",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Top padding",
      default: 16,
      info: "Top padding for section",
    },
    {
      type: "range",
      id: "padding_bottom",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Bottom padding",
      default: 16,
      info: "Bottom padding for section",
    },
  ],
  blocks: [
    {
      type: "collection-item",
      name: "Collection Item",
      props: [
        {
          type: "collection",
          id: "collection",
          label: "Select Collection",
        },
      ],
    },
  ],
  preset: {
    blocks: [
      {
        name: "Collection 1",
      },
      {
        name: "Collection 2",
      },
      {
        name: "Collection 3",
      },
    ],
  },
};

Component.serverFetch = async ({ fpi, blocks, id }) => {
  try {
    const ids = [];
    const promisesArr = blocks?.map(async (block) => {
      if (block.props?.collection?.value) {
        const slug = block.props.collection.value;
        ids.push(slug);
        return fpi.executeGQL(COLLECTION, {
          slug: slug.split(" ").join("-"),
        });
      }
    });
    const responses = await Promise.all(promisesArr);
    return fpi.custom.setValue(`collectionData-${ids?.join("__")}`, responses);
  } catch (err) {
    // console.log(err);
  }
};

export default Component;
