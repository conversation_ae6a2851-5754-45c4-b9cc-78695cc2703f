import React, { useEffect, useMemo, useState, useRef } from "react";
import { useGlobalStore, useFPI } from "fdk-core/utils";
import { FDKLink, BlockRenderer } from "fdk-core/components";
import { useParams, useLocation } from "react-router-dom";
import { convertActionToUrl } from "@gofynd/fdk-client-javascript/sdk/common/Utility";
import OutsideClickHandler from "react-outside-click-handler";
import FyButton from "fdk-react-templates/components/core/fy-button/fy-button";
import "fdk-react-templates/components/loader/loader.css";
import FyImage from "../components/core/fy-image/fy-image";
import ShareItem from "../components/share-item/share-item";
import ProductCompareButton from "../page-layouts/compare/product-compare-button";
import useProductDescription from "../page-layouts/pdp/product-description/useProductDescription";
import PdpImageGallery from "../page-layouts/pdp/components/image-gallery/image-gallery";
import ProductVariants from "../page-layouts/pdp/components/product-variants/product-variants";
import SizeGuide from "../page-layouts/pdp/size-guide/size-guide";
import DeliveryInfo from "../page-layouts/pdp/components/delivery-info/delivery-info";
import Offers from "../page-layouts/pdp/components/offers/offers";
import ProdDesc from "../page-layouts/pdp/components/prod-desc/prod-desc";
import BreadCrumb from "../components/breadcrumb/breadcrumb";
import Badges from "../page-layouts/pdp/components/badges/badges";
import StickyAddToCart from "../page-layouts/pdp/components/sticky-addtocart/sticky-addtocart";
import MoreOffers from "../page-layouts/pdp/components/offers/more-offers";
import StoreModal from "../page-layouts/pdp/components/store/store-modal";
import EmptyState from "../components/empty-state/empty-state";
import PlusIcon from "../assets/images/plus.svg";
import {
  isEmptyOrNull,
  isRunningOnClient,
  currencyFormat,
} from "../helper/utils";
import { useSnackbar, useViewport } from "../helper/hooks";
import styles from "../styles/sections/product-description.less";
import { GET_PRODUCT_DETAILS } from "../queries/pdpQuery";
import QuantityController from "fdk-react-templates/components/quantity-control/quantity-control";
import "fdk-react-templates/components/quantity-control/quantity-control.css";
import useCart from "../page-layouts/cart/useCart";
import { createPortal } from "react-dom";
import Shimmer from "../components/shimmer/shimmer";
import ShareDesktopIcon from "../assets/images/share-desktop.svg";
import ArrowDownIcon from "../assets/images/arrow-down.svg";
import CartIcon from "../assets/images/cart.svg";
import BuyNowIcon from "../assets/images/buy-now.svg";
import ScaleIcon from "../assets/images/scale.svg";
import HeartIcon from "../assets/images/heart.svg";
// import WishlistIcon from "../assets/images/wishlist";
import { useWishlist } from "../helper/hooks/useWishlist";
import OccasionToWear from "../page-layouts/pdp/components/occassionTowear/OccassionsToWear";
import ProductServiceBadges from "../page-layouts/pdp/components/ProductServiceBadges/ProductServiceBadges";
import FabricAndFeels from "../page-layouts/pdp/components/FabricAndFeels/FabricAndFeels";
// import ProductDetailsBlocks from "../page-layouts/pdp/components/product-details/productDetailsBlocks";
import OthersHaveWorn from "../page-layouts/pdp/components/othersHaveWorn/othersHaveWorn";
import WishlistComponent from "../components/wishlist-icon/wishlist-icon";
export function Component({ props = {}, globalConfig = {}, blocks = [] }) {
  const fpi = useFPI();
  const {
    icon_color,
    variant_position,
    product,
    enable_buy_now,
    img_resize,
    img_resize_mobile,
    product_name_show_brand,
  } = props;



  const addToCartBtnRef = useRef(null);
  const params = useParams();
  const location = useLocation();

  const isPDP = /^\/product\/[^/]+\/?$/.test(location.pathname);
  const slug = isPDP ? params?.slug : product?.value;

  const [showSizeGuide, setShowSizeGuide] = useState(false);
  const [showStoreModal, setShowStoreModal] = useState(false);
  const [isLoadingCart, setIsLaodingCart] = useState(false);
  const [notifyAccordionOpen, setNotifyAccordionOpen] = useState(false);
  const [notifySize, setNotifySize] = useState("");

  const getBlockConfigValue = (block, id) => block?.props?.[id]?.value ?? "";
  const { showSnackbar } = useSnackbar();

  const imgSources = useMemo(() => {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      { breakpoint: { min: 481 }, width: img_resize?.value ?? 700 },
      { breakpoint: { max: 480 }, width: img_resize_mobile?.value ?? 700 },
    ];
  }, [globalConfig?.img_hd, img_resize?.value, img_resize_mobile?.value]);

  const isSizeWrapperAvailable = useMemo(() => {
    return !!blocks.find((block) => block.type === "size_wrapper");
  }, [blocks]);

  const blockProps = useMemo(() => {
    const currentProps = {
      size_guide: false,
      preselect_size: false,
      hide_single_size: false,
      tax_label: "",
      mrp_label: false,
      show_offers: false,
      show_logo: false,
      show_fit_type: false,
    };

    blocks.forEach((block) => {
      if (block.type === "size_guide") {
        currentProps.size_guide =
          getBlockConfigValue(block, "size_guide") || false;
      }

      if (block.type === "size_wrapper") {
        currentProps.preselect_size =
          getBlockConfigValue(block, "preselect_size") || false;
        currentProps.hide_single_size =
          getBlockConfigValue(block, "hide_single_size") || false;
      }

      if (block.type === "product_tax_label") {
        currentProps.tax_label = getBlockConfigValue(block, "tax_label") || "";
      }

      if (block.type === "product_price") {
        currentProps.mrp_label =
          getBlockConfigValue(block, "mrp_label") || false;
      }

      if (block.type === "offers") {
        currentProps.show_offers =
          getBlockConfigValue(block, "show_offers") || false;
      }

      if (block.type === "fit_type") {
        currentProps.show_fit_type =
          getBlockConfigValue(block, "show_fit_type") || false;
      }

      if (block.type === "pincode") {
        currentProps.show_logo =
          getBlockConfigValue(block, "show_logo") || false;
      }
    });

    return currentProps;
  }, [blocks]);

  const application = useGlobalStore(fpi.getters.APPLICATION);

  const {
    productDetails,
    isLoading,
    isLoadingPriceBySize,
    productPriceBySlug,
    productMeta,
    pincode,
    coupons,
    followed,
    promotions,
    selectPincodeError,
    pincodeErrorMessage,
    setCurrentSize,
    addToWishList,
    removeFromWishlist,
    addProductForCheckout,
    checkPincode,
    setPincodeErrorMessage,
    isPageLoading,
    pincodeInput,
    isValidDeliveryLocation,
    deliveryLocation,
    isServiceabilityPincodeOnly,
    currentSize,
    incrementDecrementUnit,
    maxCartQuantity,
    minCartQuantity,
    allStoresInfo,
    getProductSellers,
    isSellerLoading,
    setCurrentPincode,
    currentPincode,
    buybox,
    city,
    state,
  } = useProductDescription({ fpi, slug, props });


  console.log("productDetails in product description", productDetails);

  const { toggleWishlist, followedIdList } = useWishlist({ fpi });
  const isFollowed = followedIdList.includes(productDetails?.uid);

  const { onUpdateCartItems, isCartUpdating, cartItems } = useCart(fpi, false);

  const singleItemDetails = useMemo(() => {
    let selectedItemDetails = {};

    if (currentSize?.value) {
      const cartItemsKey = Object.keys(cartItems || {});
      const selectedItemKey = `${productDetails?.uid}_${currentSize.value}_${productPriceBySlug?.store?.uid}`;

      cartItemsKey.some((item, index) => {
        if (item === selectedItemKey) {
          selectedItemDetails = { ...cartItems[item], itemIndex: index };
          return true;
        }

        return false;
      });
    }

    return selectedItemDetails;
  }, [currentSize, cartItems, productDetails, productPriceBySlug]);

  const priceDataDefault = productMeta?.price;
  const [selectedSize, setSelectedSize] = useState("");
  const [showSizeDropdown, setShowSizeDropdown] = useState(false);
  const [showMoreOffers, setShowMoreOffers] = useState(false);
  const [sidebarActiveTab, setSidebarActiveTab] = useState("coupons");
  const [errMessage, setErrorMessage] = useState("");
  const [showSocialLinks, setShowSocialLinks] = useState(false);
  const isMobile = useViewport(0, 768);
  const {
    media,
    grouped_attributes,
    brand,
    name,
    short_description,
    variants,
    sizes,
  } = productDetails;





  const { isProductNotFound } = useGlobalStore(fpi?.getters?.CUSTOM_VALUE);
  const isMto = productDetails?.custom_order?.is_custom_order || false;
  const { show_price, disable_cart, show_quantity_control } = globalConfig;

  const priceDataBySize = productPriceBySlug?.price;
  const isSizeSelectionBlock = (block) =>
    getBlockConfigValue(block, "size_selection_style") === "block";
  const isSingleSize = sizes?.sizes?.length === 1;
  const isSizeCollapsed = blockProps?.hide_single_size && isSingleSize;

  function getManufacturingTime() {
    const custom_order = productDetails?.custom_order;
    if (
      custom_order?.manufacturing_time >= 0 &&
      custom_order?.manufacturing_time_unit
    ) {
      return custom_order;
    }
    return false;
  }

  const getProductPrice = (key) => {
    if (selectedSize && !isEmptyOrNull(productPriceBySlug.price)) {
      if (productPriceBySlug?.set) {
        return currencyFormat(productPriceBySlug?.price_per_piece[key]) || "";
      }
      const price = productPriceBySlug?.price || "";
      return currencyFormat(price?.[key], price?.currency_symbol) || "";
    }
    if (selectedSize && priceDataDefault) {
      return (
        currencyFormat(
          priceDataDefault?.[key]?.min,
          priceDataDefault?.[key]?.currency_symbol
        ) || ""
      );
    }
    if (priceDataDefault) {
      return priceDataDefault?.[key]?.min !== priceDataDefault?.[key]?.max
        ? `${priceDataDefault?.[key]?.currency_symbol || ""} ${currencyFormat(priceDataDefault?.[key]?.min) || ""
        } - ${currencyFormat(priceDataDefault?.[key]?.max) || ""}`
        : currencyFormat(
          priceDataDefault?.[key]?.max,
          priceDataDefault?.[key]?.currency_symbol
        ) || "";
    }
  };

  const onSizeSelection = (size) => {
    if (size?.quantity === 0 && !isMto) {
      return;
    }
    setSelectedSize(size?.value);
    setCurrentSize(size);
    setShowSizeDropdown(false);
  };

  useEffect(() => {
    if (
      isSizeCollapsed ||
      (blockProps?.preselect_size && sizes !== undefined)
    ) {
      onSizeSelection(sizes?.sizes?.[0]);
    }
  }, [isSizeCollapsed, blockProps?.preselect_size, sizes?.sizes]);

  // function getReviewRatingInfo() {
  //   const customMeta = productDetails?.custom_meta || [];

  //   return getReviewRatingData(customMeta);
  // }

  const discountLabel = useMemo(() => {
    const productDiscount = productPriceBySlug?.discount;
    const sizeDiscount = sizes?.discount;

    return selectedSize ? productDiscount : productDiscount || sizeDiscount;
  }, [productPriceBySlug, sizes]);

  const isSizeGuideAvailable = useMemo(() => {
    const sizeChartHeader = productMeta?.size_chart?.headers || {};
    return (
      Object.keys(sizeChartHeader).length > 0 || productMeta?.size_chart?.image
    );
  }, [productMeta]);

  const soldBy = useMemo(() => {
    const sellerInfo = productPriceBySlug?.seller || {};
    const storeInfo = productPriceBySlug?.store || {};

    return buybox?.is_seller_buybox_enabled
      ? { ...sellerInfo, count: sellerInfo.count ?? storeInfo.count }
      : storeInfo;
  }, [productPriceBySlug, buybox]);

  const isAllowStoreSelection = useMemo(() => {
    return buybox?.enable_selection && soldBy?.count > 1;
  }, [buybox, soldBy]);

  const sellerStoreName = useMemo(() => {
    const sellerName = productPriceBySlug?.seller?.name;
    const storeName = productPriceBySlug?.store?.name;

    return [sellerName, storeName].filter(Boolean).join(", ") || "";
  }, [productPriceBySlug]);

  const handleShare = async () => {
    if (navigator.share && isMobile) {
      try {
        await navigator.share({
          title: "Amazing Product",
          text: `Check out this amazing product on ${application?.name}`,
          url: window?.location?.href,
        });
      } catch (error) {
        console.error("Sharing failed", error);
      }
    } else setShowSocialLinks(true);
  };

  const cartUpdateHandler = async (
    event,
    itemDetails,
    itemSize,
    quantity,
    itemIndex,
    operation
  ) => {
    let totalQuantity = (itemDetails?.quantity || 0) + quantity;

    if (operation === "edit_item") {
      totalQuantity = quantity;
    }

    if (!isMto) {
      if (totalQuantity > maxCartQuantity) {
        totalQuantity = maxCartQuantity;
        showSnackbar(`Maximum quantity is ${maxCartQuantity}.`, "error");
      }

      if (totalQuantity < minCartQuantity) {
        if (operation === "edit_item") {
          totalQuantity = minCartQuantity;
          showSnackbar(`Minimum quantity is ${minCartQuantity}.`, "error");
        } else if (itemDetails?.quantity > minCartQuantity) {
          totalQuantity = minCartQuantity;
        } else {
          totalQuantity = 0;
        }
      }
    }

    if (itemDetails?.quantity !== totalQuantity) {
      onUpdateCartItems(
        event,
        itemDetails,
        itemSize,
        totalQuantity,
        itemIndex,
        "update_item"
      );
    }
  };

  const toggleStoreModal = () => {
    setShowStoreModal((modal) => {
      const updatedModal = !modal;

      if (typeof document !== "undefined") {
        const classList = document.body?.classList;

        if (updatedModal && classList) {
          classList.add("remove-scroll");
        } else {
          classList.remove("remove-scroll");
        }
      }

      return updatedModal;
    });
  };

  const onSellerClick = () => {
    if (isAllowStoreSelection) {
      toggleStoreModal();
      getProductSellers();
    }
  };

  if (isRunningOnClient() && isPageLoading) {
    return <Shimmer />;
  }

  if (isProductNotFound) {
    return <EmptyState title="No product found" />;
  }

  // productdetails
  // const descriptionProduct = {
  //   Collection: productData.collection,
  //   "Item Code": pageConfig?.item_code ? attributes?.item_code : "",
  //   Gender:
  //     productData?.gender?.length > 0
  //       ? productData?.gender?.map((item) => {
  //         return item;
  //       })
  //       : "",
  //   Colour: productData.color,
  //   Material: productData.material,
  //   Technique: productData.technique,
  //   Embellishment: productData.embellishment,
  //   "Embroidery Type": productData.embroideryType,
  //   Silhouette: productData.silhouette,
  //   "Neck Type": productData.neckType,
  //   "Collar Style": productData.collarStyle,
  //   "Sleeve Type": productData.sleeveType,
  //   "Sleeve Length": productData.sleeveLength,
  //   "Strap Type": productData.strapType,
  //   "Closure Type": productData.closureType,
  //   "Number of Pockets": productData.numberOfPockets,
  //   Measurements: productData.productDimensions,
  //   Highlights:
  //     productData?.highlights?.length > 0
  //       ? productData?.highlights?.map((item, index) =>
  //         index < productData?.highlights?.length - 1
  //           ? `${item} ~ `
  //           : `${item}`
  //       )
  //       : "",
  //   "Care Instructions": productData.careInstructions,
  //   // Season: attributes?.["season"],
  //   // "Package Dimension": attributes?.["pack-size"],
  //   // "Marketer Name": attributes?.["marketer-name"],
  //   // "Marketer Address": attributes?.["marketer-address"],
  //   // "Name of Commodity": attributes?.["name-of-commodity"],
  //   // "Country of Origin": attributes?.["country_of_origin"],
  //   // "Net Qty": net_quantity?.value,
  //   // "MRP (inclusive of all taxes)": getProductPrice("effective"),
  // };
  const productDetailsList = {


    Occassion: productDetails.attributes.occasion,
    Color: productDetails.attributes.color,
    "Product Fit": productDetails.attributes.product_fit,
    "Print & Pattern": productDetails.attributes.pattern,
    Material: productDetails.attributes.material,
    // Type: productDetails.attributes.productType,
    productDetailsFields: [
      "Occassion",
      "Color",
      "Product Fit",
      "Print & Pattern",
      "Material",
      // "Type",
    ],
  };

  const productDescription = {
    details: productDetails.description || "description not found",
    title: "Product Description",
  };

  // Helper function to parse care instructions
  const parseCareInstructions = (careInstructions) => {
    try {
      if (!careInstructions) {
        return {
          name: "Cleaning and Care",
          details: []
        };
      }
      const instructions = careInstructions.split('\n');
      const careMap = {
        'Bleach': { logo: 'do-not-bleach', text: 'Do Not Bleach' },
        'Dry': { logo: 'do-not-tumble', text: 'Do Not Tumble Dry' },
        'Dry Clean': { logo: 'do-not-dryclean', text: 'Do Not Dry Clean' },
        'Iron': { logo: 'do-not-iron', text: 'Iron- Low' },
        'Wash': { logo: 'machine-wash-cold', text: 'Machine Wash- Cold (30°C)' }
      };
      const details = instructions.map(instruction => {
        if (!instruction) return null;
        const [type] = instruction.split(': ');
        return careMap[type] || null;
      }).filter(Boolean);
      return { name: "Cleaning and Care", details };
    } catch (error) {
      console.error('Error parsing care instructions:', error);
      return { name: "Cleaning and Care", details: [] };
    }
  };

  // Use the parsed instructions
  const cleaningAndCareFields = parseCareInstructions(productDetails.attributes?.care_instructions);

  const returnExchangeFields = {
    name: "Return Policy",
    details: [
      {
        label: productDetails?.attributes?.["return-text"] || "not found",
      },
    ],
  };
  const additionalInfoFields = {
    name: "Additional Info",
    details: [
      {
        label: "Importer Name",
        value: productDetails?.attributes?.["marketer-name"] || "not found",
      },
      {
        label: "Importer Address",
        value: productDetails?.attributes?.["marketer-address"] || "not found",
      },
      {
        label: "Import Month Year",
        value: productDetails?.attributes?.["import_month_year"] || "not found",
      },
      {
        label: "Commodity Name",
        value: productDetails?.attributes?.["name-of-commodity"] || "not found",
      },
      {
        label: "Net Quantity",
        value: productDetails?.attributes?.["net-uantity"] || "not found",
      },
      {
        label: "Package Content",
        value: productDetails?.attributes?.["package-content"] || "not found",
      },
      {
        label: "Product Dimensions",
        value: productDetails?.attributes?.["product-dimensions"] || "not found",
      },
      {
        label: "Country of Origin",
        value: productDetails?.attributes?.["country_of_origin"] || "not found",
      },
      {
        label: "MRP",
        value: productDetails?.attributes?.["mrp"] || "not found",
      },
      {
        label: "Return Policy",
        value: productDetails?.attributes?.["return-text"] || "not found",
      },
      {
        label: "Customer Care",
        value: productDetails?.attributes?.["customer-care"] || "not found",
      },

    ]
  };

  const fitTypeData = productDetails?.attributes?.["tags"];
  const fabricAndMaterialData = {

    // split after :
    title: productDetails?.attributes?.["material"],
    description: productDetails?.attributes?.["fabric-description"],


  }
  const howItFeelsData = {
    lightWeight: productDetails?.attributes?.["custom-attribute-14"],
    fit: productDetails?.attributes?.["custom-attribute-15"],
  }
  console.log("howItFeelsData", howItFeelsData);



  // to avoid mutation
  const newBlocks = [...blocks];
  // Create mobile-optimized blocks array that swaps offer and size positions
  if (isMobile) {

    const sizeIndex = blocks.findIndex(b => b.type === 'size_wrapper');
    const offerIndex = blocks.findIndex(b => b.type === 'offers');

    // Return original if either is missing
    if (sizeIndex === -1 || offerIndex === -1) return blocks;

    // Swap logic
    const temp = newBlocks[sizeIndex];
    newBlocks[sizeIndex] = newBlocks[offerIndex];
    newBlocks[offerIndex] = temp;

    // remove the product_variants block and push at its as a index = 0
    const productVariantsIndex = newBlocks.findIndex(block => block.type === 'product_variants');
    if (productVariantsIndex !== -1) {
      const productVariants = newBlocks.splice(productVariantsIndex, 1);
      newBlocks.unshift(productVariants[0]);
    }

  }

  blocks = blocks.filter(block => block.type !== 'product_service_badges');


  return (
    <div className={styles.productDescriptionContainer}>
      <div className={`${styles.mainContainer}`}>
        <div className={styles.productDescContainer}>

          <div className={styles.left}>
            <div className={styles.breadcrumbWrapper}>

              {/* <BreadCrumb
                breadcrumb={productDetails}
                config={props}
                customClass={styles.isDesktop}
              /> */}
            </div>
            {media?.length > 0 && (
              <div className={styles.imgWrap}>
                <PdpImageGallery
                  key={slug}
                  images={media}
                  iconColor={icon_color?.value || ""}
                  globalConfig={globalConfig}
                  followed={followed}
                  imgSources={imgSources}
                  isCustomOrder={isMto}
                  handleShare={() => handleShare()}
                />
              </div>
            )}
          </div>
          <div className={styles.right}>
            <div className={styles.product}>
              {/* <BreadCrumb
                productData={productDetails}
                config={props}
                customClass={styles.isMobile}
              /> */}

              {/* <ProductDetailsBlocks productDetailsBlockTypes={ProductDetailsBlockTypes} productDetails={productDetails} globalConfig={globalConfig}
                getProductPrice={getProductPrice}
                getBlockConfigValue={getBlockConfigValue}
                isLoading={isLoading}
                productMeta={productMeta}
                discountLabel={discountLabel}
                show_price={show_price}
                isMto={isMto}
                toggleWishlist={toggleWishlist}
                followedIdList={followedIdList}
                isFollowed={isFollowed}
                followed={followed}
                imgSources={imgSources}
                isCustomOrder={isMto}
                fitType={fitType}
                blocks={blocks}
                isSizeCollapsed={isSizeCollapsed}
                selectedSize={selectedSize}
                onSizeSelection={onSizeSelection}
                isSizeSelectionBlock={isSizeSelectionBlock}
                disable_cart={disable_cart}
                show_quantity_control={show_quantity_control}
                isCartUpdating={isCartUpdating}
                addProductForCheckout={addProductForCheckout}
                getProductSellers={getProductSellers}
                isSellerLoading={isSellerLoading}
                showStoreModal={showStoreModal}
                singleItemDetails={singleItemDetails}
                addToCartBtnRef={addToCartBtnRef}
                isLoadingCart={isLoadingCart}
                maxCartQuantity={maxCartQuantity}
                minCartQuantity={minCartQuantity}
                incrementDecrementUnit={incrementDecrementUnit}
                notifyAccordionOpen={notifyAccordionOpen}
                notifySize={notifySize}
                enable_buy_now={enable_buy_now}
                isAllowStoreSelection={isAllowStoreSelection}


              /> */}

              {(isMobile ? newBlocks : blocks) &&
                (isMobile ? newBlocks : blocks).map((block, index) => {
                  switch (block.type) {

                    case "product_name":
                      return (
                        <div className={styles.titleBlock}>
                          {getBlockConfigValue(block, "show_brand") && (
                            <h3 className={`${styles.productBrand} fontHeader`}>
                              {brand?.name || ""}
                            </h3>
                          )}
                          <div className={styles.productTitleWrapper}>
                            <div className={styles.productTitleContainer}>
                              <div className={styles.bestSellerContainer}>
                                <span className={styles.bestSellerText}>Best seller</span>
                              </div>
                              <h1
                                className={`${styles.productTitle} ${styles.fontHeader} fontHeader h3`}
                              >
                                {name}
                              </h1>
                            </div>
                            <div
                              className={styles.heartIcon}
                              onClick={() => toggleWishlist({ product: productDetails, isFollowed })}
                            >
                              <WishlistComponent isActive={isFollowed} />
                            </div>
                            <>
                              {/* <span
                                className={styles.shareIcon}
                                onClick={() => handleShare()}
                              >
                                <ShareDesktopIcon />
                              </span> */}
                              {/* {showSocialLinks && (
                                <ShareItem
                                  setShowSocialLinks={setShowSocialLinks}
                                  handleShare={() => handleShare()}
                                  description={`Check out this amazing product on ${application?.name}`}
                                />
                              )} */}
                            </>

                          </div>
                        </div>
                      );

                    case "product_price":
                      return (
                        <>
                          {show_price && (
                            <div className={styles.product__price}>
                              {!isLoading && productMeta?.sellable && (
                                <div className={styles.priceContainer}>
                                  <div className={styles.mrpStrike}>
                                    {getProductPrice("marked") &&
                                      getBlockConfigValue(block, "mrp_label") &&
                                      getProductPrice("effective") !==
                                      getProductPrice("marked") && (
                                        <span
                                          className={`${styles.mrpLabel} ${styles.mrpLabel}`}
                                        >
                                          MRP
                                        </span>
                                      )}
                                    {getProductPrice("effective") !==
                                      getProductPrice("marked") && (
                                        <span
                                          className={
                                            styles["product__price--marked"]
                                          }
                                        >
                                          {getProductPrice("marked") || "2000"}
                                        </span>
                                      )}
                                  </div>

                                  <h4
                                    className={
                                      styles["product__price--effective"]
                                    }
                                  >
                                    {getProductPrice("effective")}
                                  </h4>

                                  {discountLabel && (
                                    <span
                                      className={
                                        styles["product__price--discount"]
                                      }
                                    >
                                      {discountLabel || "40 % off"}
                                    </span>
                                  )}
                                </div>
                              )}
                              <>
                                {

                                  productMeta?.sellable && (
                                    <div
                                      className={`captionNormal ${styles.taxLabel}`}
                                    >
                                      (price incliusive all taxes)
                                    </div>
                                  )
                                }
                              </>
                            </div>
                          )}
                        </>
                      );

                    case "product_service_badges":
                      return (
                        <ProductServiceBadges />
                      )

                    case "product_tax_label":
                      return (
                        <>
                          {getBlockConfigValue(block, "tax_label") &&
                            productMeta?.sellable && (
                              <div
                                className={`captionNormal ${styles.taxLabel}`}
                              >
                                (
                                {getBlockConfigValue(block, "tax_label")}
                                )
                              </div>
                            )}
                        </>
                      );

                    case "fit_type":
                      return fitTypeData?.length > 0 ? (
                        <div className={styles.fitTypeWrapper}>
                          <div className={styles.fitTypeContainer}>
                            {fitTypeData.map((fit, index) => (
                              <div className={styles.fitTypeTextContainer} key={index}>
                                <p className={styles.fitTypeText}>{fit}</p>
                              </div>
                            ))}
                          </div>
                          <div className={styles.line}></div>
                        </div>
                      ) : null;


                    case "short_description":
                      return (
                        <>
                          {short_description?.length > 0 && (
                            <p
                              className={`b2 ${styles.fontBody} ${styles.shortDescription}`}
                            >
                              {short_description}
                            </p>
                          )}
                        </>
                      );

                    case "product_variants":
                      return (
                        <>
                          {variants?.length > 0 && (
                            <div>
                              <ProductVariants
                                product={productDetails}
                                variants={variants}
                                currentSlug={slug}
                                globalConfig={globalConfig}
                              />
                            </div>
                          )}
                        </>
                      );

                    case "seller_details":
                      return (
                        <>
                          {getBlockConfigValue(block, "show_seller") &&
                            selectedSize &&
                            !isEmptyOrNull(productPriceBySlug) &&
                            buybox?.show_name &&
                            sellerStoreName && (
                              <div
                                className={`${styles.sellerInfo} ${styles.fontBody}`}
                              >
                                <div
                                  className={`${styles.storeSeller} captionNormal`}
                                >
                                  <span className={styles.soldByLabel}>
                                    Sold by :
                                  </span>
                                  <div
                                    // v-if="showSellerStoreLabel"
                                    className={`${styles.nameWrapper} ${isAllowStoreSelection ? styles.selectable : ""}`}
                                    onClick={onSellerClick}
                                  >
                                    <p className={styles.storeSellerName}>
                                      {soldBy?.name}
                                    </p>
                                    {isAllowStoreSelection && (
                                      <>
                                        <span
                                          className={`captionSemiBold ${styles.otherSellers}`}
                                        >
                                          &nbsp;&&nbsp;
                                          {`${soldBy?.count - 1} Other${soldBy?.count > 2 ? "s" : ""}`}
                                        </span>
                                        <ArrowDownIcon
                                          className={styles.dropdownArrow}
                                        />
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}

                          <StoreModal
                            isOpen={showStoreModal}
                            buybox={buybox}
                            allStoresInfo={allStoresInfo}
                            onCloseDialog={toggleStoreModal}
                            addItemForCheckout={(e, isBuyNow, item) =>
                              addProductForCheckout(
                                e,
                                selectedSize,
                                isBuyNow,
                                item
                              )
                            }
                            getProductSellers={getProductSellers}
                            isSellerLoading={isSellerLoading}
                          />
                        </>
                      );

                    case "size_wrapper":
                      return (
                        <>
                          <div className={styles.sizeWrapperContainer}>
                            <div className={styles.sizeContainer}>
                              {isSizeSelectionBlock(block) &&
                                productMeta?.sellable &&
                                sizes?.sizes?.length && (
                                  <div
                                    className={`${styles.sizeSelection} ${isSizeCollapsed
                                      ? styles["sizeSelection--collapse"]
                                      : ""
                                      }`}
                                  >
                                    <div className={styles.sizeSelection__container}>
                                      <div className={styles.sizeSelection__header}>

                                        <p
                                          className={`b2 ${styles.sizeSelection__label}`}
                                        >
                                          <span className={styles.sizeLabel}>Size</span>
                                        </p>

                                        <p className={styles.sizeSelection__findSize}
                                          onClick={() => setShowSizeGuide(true)}
                                        >
                                          <span className={styles.sizeSelection__findSizeText}>find your size?</span>
                                        </p>
                                      </div>
                                      <div
                                        className={
                                          styles.sizeSelection__wrapper
                                        }
                                      >
                                        {sizes?.sizes?.map((size) => (
                                          <button
                                            type="button"
                                            key={`${size?.display}`}
                                            className={`b2 ${styles.sizeSelection__block
                                              } ${size.quantity === 0 &&
                                              !isMto &&
                                              styles[
                                              "sizeSelection__block--disable"
                                              ]
                                              } ${(size?.quantity !== 0 || isMto) &&
                                              styles[
                                              "sizeSelection__block--selectable"
                                              ]
                                              } ${selectedSize === size?.value &&
                                              styles[
                                              "sizeSelection__block--selected"
                                              ]
                                              } `}
                                            title={size?.value}
                                            onClick={() =>
                                              onSizeSelection(size)
                                            }
                                          >
                                            <span className={styles.sizeText}>{size?.display}</span>
                                            {size?.quantity === 0 && !isMto && (
                                              <svg>
                                                <line
                                                  x1="0"
                                                  y1="100%"
                                                  x2="100%"
                                                  y2="0"
                                                />
                                              </svg>
                                            )}
                                          </button>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                )}
                            </div>
                            <div className={styles.sizeCartContainer}>
                              {!isSizeSelectionBlock(block) &&
                                productMeta?.sellable && (
                                  <div
                                    className={`${styles.sizeWrapper} ${isSizeCollapsed &&
                                      styles["sizeWrapper--collapse"]
                                      }`}
                                  >
                                    <OutsideClickHandler
                                      onOutsideClick={() => {
                                        setShowSizeDropdown(false);
                                      }}
                                    >
                                      <div
                                        className={` ${styles.sizeButton} ${styles.flexAlignCenter
                                          } ${styles.justifyBetween} ${styles.fontBody} ${sizes?.sizes?.length &&
                                          styles.disabledButton
                                          }`}
                                        onClick={() =>
                                          setShowSizeDropdown(!showSizeDropdown)
                                        }
                                        disabled={!sizes?.sizes?.length}
                                      >
                                        <p
                                          className={`${styles.buttonFont} ${styles.selectedSize}`}
                                          title={
                                            selectedSize
                                              ? `Size : ${selectedSize}`
                                              : "SELECT SIZE"
                                          }
                                        >
                                          {selectedSize
                                            ? `Size : ${selectedSize}`
                                            : "SELECT SIZE"}
                                        </p>
                                        <ArrowDownIcon
                                          className={`${styles.dropdownArrow} ${showSizeDropdown &&
                                            styles.rotateArrow
                                            }`}
                                        />
                                      </div>
                                      <ul
                                        className={styles.sizeDropdown}
                                        style={{
                                          display: showSizeDropdown
                                            ? "block"
                                            : "none",
                                        }}
                                      >
                                        {sizes?.sizes?.map((size) => (
                                          <li
                                            onClick={() =>
                                              onSizeSelection(size)
                                            }
                                            key={size?.value}
                                            className={`${selectedSize === size.display &&
                                              styles.selected_size
                                              } ${size.quantity === 0 && !isMto
                                                ? styles.disabled_size
                                                : styles.selectable_size
                                              }`}
                                          >
                                            {size.display}
                                          </li>
                                        ))}
                                      </ul>
                                    </OutsideClickHandler>
                                  </div>
                                )}

                              <div
                                className={`${styles.cartWrapper} ${isSizeSelectionBlock(block) &&
                                  styles["cartWrapper--half-width"]
                                  }`}
                              >
                                {!disable_cart && productMeta?.sellable && (
                                  <>
                                    {singleItemDetails?.quantity &&
                                      show_quantity_control ? (
                                      <>
                                        <QuantityController
                                          isCartUpdating={isCartUpdating}
                                          count={
                                            singleItemDetails?.quantity || 0
                                          }
                                          onDecrementClick={(e) =>
                                            cartUpdateHandler(
                                              e,
                                              singleItemDetails,
                                              currentSize.value,
                                              -incrementDecrementUnit,
                                              singleItemDetails?.itemIndex,
                                              "update_item"
                                            )
                                          }
                                          onIncrementClick={(e) =>
                                            cartUpdateHandler(
                                              e,
                                              singleItemDetails,
                                              currentSize.value,
                                              incrementDecrementUnit,
                                              singleItemDetails?.itemIndex,
                                              "update_item"
                                            )
                                          }
                                          onQtyChange={(evt, currentNum) =>
                                            cartUpdateHandler(
                                              evt,
                                              singleItemDetails,
                                              currentSize.value,
                                              currentNum,
                                              singleItemDetails?.itemIndex,
                                              "edit_item"
                                            )
                                          }
                                          maxCartQuantity={
                                            singleItemDetails?.article
                                              ?.quantity ?? maxCartQuantity
                                          }
                                          minCartQuantity={minCartQuantity}
                                          containerClassName={
                                            styles.qtyContainer
                                          }
                                          inputClassName={styles.inputContainer}
                                        />
                                      </>
                                    ) : (
                                      <button
                                        type="button"
                                        ref={addToCartBtnRef}
                                        className={`${styles.button} btnSecondary ${styles.flexCenter} ${styles.addToCart} ${styles.fontBody}`}
                                        onClick={(e) => {
                                          addProductForCheckout(
                                            e,
                                            selectedSize,
                                            false
                                            //itemdetails which include articleid and article assignment
                                          );
                                          setIsLaodingCart(true);
                                          setTimeout(() => {
                                            setIsLaodingCart(false);
                                          }, 1000);
                                        }}
                                        disabled={isLoadingCart}
                                      >
                                        {/* <PlusIcon className={styles.cartIcon} /> */}
                                        + Add to cart
                                      </button>
                                    )}

                                  </>
                                )}

                                <div className={styles.sizeSelection__footer}>
                                  {!notifyAccordionOpen ? (
                                    <p
                                      className={styles.sizeSelection__footerText}
                                      onClick={() => setNotifyAccordionOpen(true)}
                                    >
                                      Size not available?{" "}
                                      <span className={styles.sizeSelection__footerTextLink}>Notify me</span>
                                    </p>
                                  ) : (
                                    <div
                                      onClick={(e) => {
                                        const tag = e.target.tagName.toLowerCase();
                                        if (["input", "select", "option", "label", "button", "textarea"].includes(tag)) {
                                          return;
                                        }
                                        setNotifyAccordionOpen(false);
                                      }}
                                    >
                                      <div className={styles.notifyAccordion}>
                                        <div className={styles.notifyTitle}>
                                          Notify me when the item gets back in stock!
                                        </div>
                                        <div className={styles.notifyFieldContainer}>
                                          <div className={styles.notifyField}>
                                            <label className={styles.notifyFieldLabel}>Size</label>
                                            <select
                                              className={styles.notifyDropdown}
                                              value={notifySize}
                                              onChange={(e) => setNotifySize(e.target.value)}
                                            >
                                              <option value="">Select Size</option>
                                              {(sizes?.sizes || [])
                                                .filter(s => s.quantity === 0 && !isMto)
                                                .map(s => (
                                                  <option key={s.value} value={s.value}>
                                                    {s.display}
                                                  </option>
                                                ))}
                                            </select>
                                          </div>

                                          <div className={styles.notifyField}>
                                            <label className={styles.notifyFieldLabel}>Your Email*</label>
                                            <input
                                              type="email"
                                              className={styles.notifyInput}
                                              placeholder="Enter your email id"
                                            />
                                          </div>
                                          <button className={styles.notifyButton}>Notify Me</button>
                                        </div>
                                        <div className={styles.notifyButtonContainer}>

                                          <div className={styles.notifyPrivacy}>
                                            We respect your privacy &amp; do not share your email with anyone.
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>



                                {!productMeta?.sellable && (
                                  <button
                                    type="button"
                                    disabled
                                    className={`${styles.button} btnPrimary ${styles.notAvailable} ${styles.fontBody}`}
                                  >
                                    PRODUCT NOT AVAILABLE
                                  </button>
                                )}
                              </div>

                              {enable_buy_now?.value &&
                                isSizeSelectionBlock(block) &&
                                productMeta?.sellable && (
                                  <div
                                    className={`${styles.actionBuyNow} ${styles["actionBuyNow--ml-12"]
                                      }`}
                                  >
                                    {!disable_cart && productMeta?.sellable && (
                                      <FyButton
                                        type="button"
                                        className={`${styles.button} btnPrimary ${styles.buyNow} ${styles.fontBody}`}
                                        onClick={(e) => {
                                          addProductForCheckout(
                                            e,
                                            selectedSize,
                                            true
                                          );
                                          setIsLaodingCart(true);
                                          setTimeout(() => {
                                            setIsLaodingCart(false);
                                          }, 500);
                                        }}
                                        disabled={isLoadingCart}
                                        startIcon={
                                          <BuyNowIcon
                                            className={styles.buyNow__icon}
                                          />
                                        }
                                      >
                                        BUY NOW
                                      </FyButton>
                                    )}
                                  </div>
                                )}
                            </div>
                            {enable_buy_now?.value &&
                              !isSizeSelectionBlock(block) && (
                                <div className={styles.actionBuyNow}>
                                  {!disable_cart && productMeta?.sellable && (
                                    <FyButton
                                      type="button"
                                      className={`${styles.button} btnPrimary ${styles.buyNow} ${styles.fontBody}`}
                                      onClick={(e) => {
                                        addProductForCheckout(
                                          e,
                                          selectedSize,
                                          true
                                        );
                                        setIsLaodingCart(true);
                                        setTimeout(() => {
                                          setIsLaodingCart(false);
                                        }, 500);
                                      }}
                                      disabled={isLoadingCart}
                                      startIcon={
                                        <BuyNowIcon
                                          className={styles.buyNow__icon}
                                        />
                                      }
                                    >
                                      BUY NOW
                                    </FyButton>
                                  )}
                                </div>
                              )}

                            <div className={styles.line}></div>
                          </div>
                        </>
                      );

                    case "size_guide":
                      return (
                        <>
                          {isSizeGuideAvailable && productMeta?.sellable && (
                            <div>
                              <button
                                type="button"
                                onClick={() => setShowSizeGuide(true)}
                                className={`${styles["product__size--guide"]} ${styles.buttonFont} ${styles.fontBody}`}
                              >
                                <span>SIZE GUIDE</span>
                                <ScaleIcon className={styles.scaleIcon} />
                              </button>
                            </div>
                          )}
                        </>
                      );

                    case "custom_button":
                      return (
                        <div className={styles.customBtn}>
                          {getBlockConfigValue(block, "custom_button_text") && (
                            <FDKLink
                              to={getBlockConfigValue(
                                block,
                                "custom_button_link"
                              )}
                            >
                              <button
                                type="button"
                                className={`${styles.button} btnPrimary ${styles.customBtnStyle} ${styles.fontBody}`}
                              >
                                {getBlockConfigValue(
                                  block,
                                  "custom_button_icon"
                                ) && (
                                    <FyImage
                                      customClass={styles.customIcon}
                                      src={getBlockConfigValue(
                                        block,
                                        "custom_button_icon"
                                      )}
                                      globalConfig={globalConfig}
                                    />
                                  )}
                                {getBlockConfigValue(
                                  block,
                                  "custom_button_text"
                                )}
                              </button>
                            </FDKLink>
                          )}
                        </div>
                      );

                    case "pincode":
                      return (
                        <>
                          {productMeta?.sellable && selectedSize && (
                            <DeliveryInfo
                              className={styles.deliveryInfoBlock}
                              pincode={pincode}
                              deliveryPromise={
                                productPriceBySlug?.delivery_promise
                              }
                              selectPincodeError={selectPincodeError}
                              pincodeErrorMessage={pincodeErrorMessage}
                              setErrorMessage={setErrorMessage}
                              // setCurrentPincode={setCurrentPincode}
                              checkPincode={checkPincode}
                              fpi={fpi}
                              pincodeInput={pincodeInput}
                              isValidDeliveryLocation={isValidDeliveryLocation}
                              deliveryLocation={deliveryLocation}
                              isServiceabilityPincodeOnly={
                                isServiceabilityPincodeOnly
                              }
                              setPincodeErrorMessage={setPincodeErrorMessage}
                              showLogo={getBlockConfigValue(block, "show_logo")}
                              city={city}
                              state={state}
                            />
                          )}
                        </>
                      );

                    case "add_to_compare":
                      return (
                        <ProductCompareButton
                          customClass={styles.compareBtn}
                          fpi={fpi}
                          slug={slug}
                        />
                      );

                    case "offers":
                      return (
                        <>
                          {/* {productMeta?.sellable && */}
                          {getBlockConfigValue(block, "show_offers") && coupons?.length > 0 && (
                            <Offers
                              couponsList={coupons}
                              promotionsList={promotions || []}
                              setShowMoreOffers={setShowMoreOffers}
                              setSidebarActiveTab={setSidebarActiveTab}
                            />
                          )}
                          {/* } */}
                        </>
                      );


                    case "prod_meta":
                      return (
                        <>
                          <ul
                            className={`${styles.productDetail} ${styles.fontBody}`}
                          >
                            {getBlockConfigValue(block, "return") && (
                              <>
                                {productPriceBySlug?.return_config
                                  ?.returnable && (
                                    <li className={styles.b2}>
                                      {`${productPriceBySlug?.return_config?.time} ${productPriceBySlug?.return_config?.unit} return`}
                                    </li>
                                  )}
                                {/* v-else-if="returnConfig.returnable === false"  */}
                                {!productPriceBySlug?.return_config
                                  ?.returnable &&
                                  selectedSize && (
                                    <li className={styles.b2}>
                                      No return available on this product
                                    </li>
                                  )}
                              </>
                            )}
                            {isMto &&
                              getManufacturingTime() &&
                              selectedSize && (
                                <li className={styles.b2}>
                                  {`Shipping within ${productDetails?.custom_order?.manufacturing_time} ${productDetails?.custom_order?.manufacturing_time_unit}`}
                                </li>
                              )}
                            {/*  */}
                            {getBlockConfigValue(block, "item_code") &&
                              productDetails?.item_code && (
                                <li className={styles.b2}>
                                  Item code : {productDetails?.item_code}
                                </li>
                              )}
                          </ul>
                        </>
                      );

                    case "trust_markers":
                      return <Badges blockProps={block.props} />;

                    case "extension-binding":
                      return <BlockRenderer block={block} />;
                      return (
                        <div className={styles.notifyBlock}>
                          <span>Size not available?&nbsp;</span>
                          <span className="b2" style={{ color: '#FF4D4F', cursor: 'pointer' }}>
                            Notify Me
                          </span>
                        </div>
                      );
                    // case "line":
                    //   return <div className={styles.line}></div>

                    case "product_details":
                      return (
                        <div>
                          <h3>Product Details</h3>
                        </div>
                      );

                    default:
                      return <div>Invalid block</div>;
                  }
                })}



              {/* line */}
              {/* <div className={styles.line}></div> */}

              {/* ---------- Prod Desc ---------- */}
              {/* {variant_position?.value === "accordion" && ( */}

              <ProdDesc product={productDetails}
                config={props}
                productDetailsList={productDetailsList}
                productDescription={productDescription}
                cleaningAndCareFields={cleaningAndCareFields}
                additionalInfoFields={additionalInfoFields}
                returnExchangeFields={returnExchangeFields}
                customClass={styles.productDescDesktop}
              />


              {/* fabric and material */}

              {/* <div className={styles.fabricAndMaterialContainer}>
                <div className={styles.fabricAndMaterialHeader}>
                  <span className={styles.fabricAndMaterialHeaderTitle}>Fabric/aterial</span>
                  <span className={styles.fabricAndMaterialHeaderSubtitle}>What's this?</span>
                </div>
                <div className={styles.fabricAndMaterialImage}>
                  <img src="" alt="" />
                </div>
                <div className={styles.fabricAndMaterialContent}>
                  <div className={styles.fabricAndMaterialContentItem}>
                    Fabric 100% Cotton
                  </div>
                  <div className={styles.fabricAndMaterialContentItem}>
                    Material 100% Cotton
                  </div>
                </div>
              </div> */}




              {/* )} */}
            </div>

            {/* fabric and material */}

            <FabricAndFeels fabricAndMaterialData={fabricAndMaterialData} howItFeelsData={howItFeelsData} />

          </div>
        </div>



        {/* <ProdDesc
          customClass={
            variant_position?.value === "tabs"
              ? ""
              : styles.productDescMobileAcc
          }
          product={productDetails}
          config={props}
        /> */}
      </div>
      {/* occasion to wear */}

      {/* <CompleteLook /> */}
      {
        isRunningOnClient() &&
        document?.getElementById("sticky-add-to-cart") &&
        !disable_cart &&
        productMeta?.sellable &&
        isSizeWrapperAvailable &&
        createPortal(
          <StickyAddToCart
            productDetails={productDetails}
            showBuyNow={enable_buy_now?.value}
            addToCartBtnRef={addToCartBtnRef}
            productMeta={productMeta}
            selectedSize={selectedSize}
            blockProps={blockProps}
            sizes={sizes}
            getProductPrice={getProductPrice}
            addProductForCheckout={addProductForCheckout}
            onSizeSelection={onSizeSelection}
            productPriceBySlug={productPriceBySlug}
            isSizeGuideAvailable={blockProps.size_guide && isSizeGuideAvailable}
            isMto={isMto}
            handleShare={handleShare}
            deliveryInfoProps={{
              fpi,
              pincode,
              deliveryPromise: productPriceBySlug?.delivery_promise,
              selectPincodeError,
              pincodeErrorMessage,
              pincodeInput,
              isValidDeliveryLocation,
              deliveryLocation,
              isServiceabilityPincodeOnly,
              checkPincode,
              setErrorMessage,
              setPincodeErrorMessage,
              showLogo: blockProps.show_logo,
            }}
            quantityControllerProps={{
              singleItemDetails,
              show_quantity_control,
              isCartUpdating,
              cartUpdateHandler,
              currentSize,
              incrementDecrementUnit,
              minCartQuantity,
              maxCartQuantity,
            }}
            itemDetails={productDetails}
          />,
          document?.getElementById("sticky-add-to-cart")
        )
      }



      <div className={styles.subContainer}>
        <div className={styles.occasionToWearContainerMobile}>
          <OccasionToWear productDetails={productDetails} />
        </div>
        <div className={styles.othersHaveWornContainerMobile}>
          <OthersHaveWorn productDetails={productDetails} />
        </div>
      </div>

      <MoreOffers
        isOpen={showMoreOffers}
        onCloseDialog={() => setShowMoreOffers(false)
        }
        couponsList={coupons}
        promotionsList={promotions}
        sidebarActiveTab={sidebarActiveTab}
      />
      {/* size guide */}

      {/* size guide */}

      <SizeGuide
        isOpen={showSizeGuide}
        onCloseDialog={() => setShowSizeGuide(false)}
        customClass={styles.sizeGuide}
        productMeta={productMeta}
      />




    </div>
  );
}

export const settings = {
  label: "Product Description",
  props: [
    {
      type: "product",
      name: "Product",
      id: "product",
      label: "Select a Product",
      info: "This config works on all pages except the product description page",
    },
    {
      type: "checkbox",
      id: "enable_buy_now",
      label: "Enable Buy now",
      info: "Enable buy now feature",
      default: true,
    },
    {
      type: "checkbox",
      id: "product_details_bullets",
      label: "Show Bullets in Product Details",
      default: true,
    },
    {
      type: "color",
      id: "icon_color",
      label: "Play video icon color",
      default: "#D6D6D6",
    },
    {
      type: "checkbox",
      id: "mandatory_pincode",
      label: "Mandatory Delivery check",
      default: true,
    },
    {
      type: "radio",
      id: "variant_position",
      label: "Product Detail Position",
      default: "accordion",
      options: [
        { value: "accordion", text: "Accordion style" },
        { value: "tabs", text: "Tab style" },
      ],
    },
    {
      type: "checkbox",
      id: "show_products_breadcrumb",
      label: "Show Products breadcrumb",
      default: true,
    },
    {
      type: "checkbox",
      id: "show_category_breadcrumb",
      label: "Show Category breadcrumb",
      default: true,
    },
    {
      type: "checkbox",
      id: "show_brand_breadcrumb",
      label: "Show Brand breadcrumb",
      default: true,
    },
    {
      type: "checkbox",
      id: "first_accordian_open",
      label: "First Accordian Open",
      default: true,
    },
    {
      id: "img_resize",
      label: "Image size for Tablet/Desktop",
      type: "select",
      options: [
        {
          value: "700",
          text: "700px",
        },
        {
          value: "900",
          text: "900px",
        },
        {
          value: "1100",
          text: "1100px",
        },
        {
          value: "1300",
          text: "1300px",
        },
        {
          value: "1500",
          text: "1500px",
        },
        {
          value: "1700",
          text: "1700px",
        },
        {
          value: "2000",
          text: "2000px",
        },
      ],
      default: "700",
    },
    {
      id: "img_resize_mobile",
      label: "Image size for Mobile",
      type: "select",
      options: [
        {
          value: "300",
          text: "300px",
        },
        {
          value: "500",
          text: "500px",
        },
        {
          value: "700",
          text: "700px",
        },
        {
          value: "900",
          text: "900px",
        },
      ],
      default: "700",
    },
    {
      type: "checkbox",
      id: "product_name_show_brand",
      label: "Product name show brand",
      default: true,
    },
    {
      type: "checkbox",
      id: "product_name_show_brand",
      label: "Product name show brand",
      default: true,
    },
  ],
  blocks: [
    {
      type: "product_name",
      name: "Product Name",
      props: [
        {
          type: "checkbox",
          id: "show_brand",
          label: "Display Brand name",
          default: true,
        },
      ],
    },
    {
      type: "product_price",
      name: "Product Price",
      props: [
        {
          type: "checkbox",
          id: "mrp_label",
          label: "Display MRP label text",
          default: true,
        },
      ],
    },
    {
      type: "product_tax_label",
      name: "Product Tax Label",
      props: [
        {
          type: "text",
          id: "tax_label",
          label: "Price tax label text",
          default: "Price inclusive of all tax",
        },
      ],
    },
    { type: "short_description", name: "Short Description", props: [] },
    {
      type: "product_variants", name: "Product Variants", props: [
        {
          type: "checkbox",
          id: "show_product_variant_details",
          label: "Show Product Variant Details",
          default: true,
        },
      ]
    },
    {
      type: "seller_details",
      name: "Seller Details",
      props: [
        {
          type: "checkbox",
          id: "show_seller",
          label: "Show Seller",
          default: true,
        },
      ],
    },
    {
      type: "size_wrapper",
      name: "Size Container with Action Buttons",
      props: [
        {
          type: "checkbox",
          id: "hide_single_size",
          label: "Hide single size",
          default: false,
        },
        {
          type: "checkbox",
          id: "preselect_size",
          label: "Preselect size",
          info: "Applicable only for multiple-size products",
          default: true,
        },
        {
          type: "radio",
          id: "size_selection_style",
          label: "Size selection style",
          default: "dropdown",
          options: [
            { value: "dropdown", text: "Dropdown style" },
            { value: "block", text: "Block style" },
          ],
        },
      ],
    },
    { type: "size_guide", name: "Size Guide", props: [] },
    {
      type: "custom_button",
      name: "Custom Button",
      props: [
        {
          type: "text",
          id: "custom_button_text",
          label: "Custom Button text",
          default: "Enquire now",
          info: "Applicable for PDP Section",
        },
        {
          type: "url",
          id: "custom_button_link",
          label: "Custom Button link",
          default: "",
        },
        {
          type: "image_picker",
          id: "custom_button_icon",
          label: "Custom Button Icon",
          default: "",
          options: { aspect_ratio: "1:1", aspect_ratio_strict_check: true },
        },
      ],
    },
    {
      type: "pincode",
      name: "Pincode",
      props: [
        {
          type: "checkbox",
          id: "show_logo",
          label: "Show brand logo",
          default: true,
          info: "The pincode section will show the brand logo and name",
        },
      ],
    },
    { type: "add_to_compare", name: "Add to Compare", props: [] },
    {
      type: "offers",
      name: "Offers",
      props: [
        {
          type: "checkbox",
          id: "show_offers",
          label: "Show Offers",
          default: true,
        },
      ],
    },
    {
      type: "prod_meta",
      name: "Prod Meta",
      props: [
        { type: "checkbox", id: "return", label: "Return", default: true },
        {
          type: "checkbox",
          id: "item_code",
          label: "Show Item code",
          default: true,
        },
      ],
    },

    {
      type: "trust_markers",
      name: "Trust Markers",
      props: [
        {
          type: "image_picker",
          id: "badge_logo_1",
          label: "Badge logo 1",
          default: "",
          options: { aspect_ratio: "1:1", aspect_ratio_strict_check: true },
        },
        {
          type: "text",
          id: "badge_label_1",
          label: "Badge label 1",
          default: "",
        },
        { type: "url", id: "badge_url_1", label: "Badge URL 1", default: "" },
        {
          type: "image_picker",
          id: "badge_logo_2",
          label: "Badge logo 2",
          default: "",
          options: { aspect_ratio: "1:1", aspect_ratio_strict_check: true },
        },
        {
          type: "text",
          id: "badge_label_2",
          label: "Badge label 2",
          default: "",
        },
        { type: "url", id: "badge_url_2", label: "Badge URL 2", default: "" },
        {
          type: "image_picker",
          id: "badge_logo_3",
          label: "Badge logo 3",
          default: "",
          options: { aspect_ratio: "1:1", aspect_ratio_strict_check: true },
        },
        {
          type: "text",
          id: "badge_label_3",
          label: "Badge label 3",
          default: "",
        },
        { type: "url", id: "badge_url_3", label: "Badge URL 3", default: "" },
        {
          type: "image_picker",
          id: "badge_logo_4",
          label: "Badge logo 4",
          default: "",
          options: { aspect_ratio: "1:1", aspect_ratio_strict_check: true },
        },
        {
          type: "text",
          id: "badge_label_4",
          label: "Badge label 4",
          default: "",
        },
        { type: "url", id: "badge_url_4", label: "Badge URL 4", default: "" },
        {
          type: "image_picker",
          id: "badge_logo_5",
          label: "Badge logo 5",
          default: "",
          options: { aspect_ratio: "1:1", aspect_ratio_strict_check: true },
        },
        {
          type: "text",
          id: "badge_label_5",
          label: "Badge label 5",
          default: "",
        },
        { type: "url", id: "badge_url_5", label: "Badge URL 5", default: "" },
      ],
    },
    {
      type: "product_details",
      name: "Product Details",
      props: []
    },
    {
      type: "fit_type",
      name: "Fit Type",
      props: [
        {
          type: "checkbox",
          id: "show_fit_type",
          label: "Show Fit Type",
          default: true,
          info: "Display fit type information for the product"
        }
      ]
    },
    {
      type: "line",
      name: "Line",
      props: [
        {
          type: "checkbox",
          id: "show_line",
          label: "Show Line",
          default: true,
        }
      ]
    },
    {
      type: "product_service_badges",
      name: "Product Service Badges",
      props: [
        {
          type: "checkbox",
          id: "show_product_service_badges",
          label: "Show Product Service Badges",
          default: true,
        }
      ]
    }
  ],
  preset: {
    blocks: [
      { name: "Product Name" },
      { name: "Product Price" },
      { name: "Product Tax Label" },
      { name: "Short Description" },
      { name: "Seller Details" },
      { name: "Size Guide" },
      { name: "Custom Button" },
      { name: "Pincode" },
      { name: "Product Variants" },
      { name: "Add to Compare" },
      { name: "Offers" },
      { name: "Prod Meta" },
      { name: "Size Container with Action Buttons" },
      { name: "Fit Type" },
      { name: "Product Details" },
      { name: "Line" },
      { name: "Product Service Badges" },
    ],
  },
};

Component.serverFetch = async ({ fpi, router, props }) => {
  const isPDP = /^\/product\/[^/]+\/?$/.test(router.pathname);
  const slug = isPDP ? router?.params?.slug : props?.product?.value;

  if (slug) {
    const values = { slug };

    fpi.custom.setValue("isPdpSsrFetched", true);
    fpi.custom.setValue("isProductNotFound", false);

    return fpi.executeGQL(GET_PRODUCT_DETAILS, values).then((result) => {
      if (result.errors && result.errors.length) {
        fpi.custom.setValue("isProductNotFound", true);
      } else {
        fpi.custom.setValue(
          "productPromotions",
          result?.data?.promotions || {}
        );
      }
      return result;
    });
  }
};

export default Component;
