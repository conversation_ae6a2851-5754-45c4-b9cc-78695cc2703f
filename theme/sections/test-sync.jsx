import React from 'react';

function Component({ props, blocks }) {
    const descValue = props?.description?.value;
    if (!descValue) return null;

    return <p>{descValue}</p>;
}

export const settings = {
    label: "Section Description",
    props: [
        {
            id: "description",
            label: "Description",
            type: "text",
            default: "",
            info: "Description text of the section",
        },
    ],
    blocks: [],
};

export default Component;