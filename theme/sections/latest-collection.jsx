import React, { useMemo, useRef } from "react";
import { FDKLink } from "fdk-core/components";
import Slider from "react-slick";
import styles from "../styles/sections/latest-collection.less";
import FyImage from "fdk-react-templates/components/core/fy-image/fy-image";
import "fdk-react-templates/components/core/fy-image/fy-image.css";
import placeholderImage from "../assets/images/placeholder/image-gallery.png";
import SliderRightIcon from "../assets/images/glide-arrow-right.svg";
import SliderLeftIcon from "../assets/images/glide-arrow-left.svg";
import { useViewport } from "../helper/hooks/useViewPort";
import Vector from "../assets/images/vector.svg";
import ArrowRightIcon from "../assets/images/arrow-right.svg";

export function Component({ props, blocks = [], globalConfig = {}, preset }) {
  const {
    autoplay: { value: autoplay } = {},
    play_slides: { value: playSlides } = {},
    title: { value: title } = {},
    description: { value: description } = {},
    desktop_layout: { value: desktopLayout } = {},
    item_count = {},
    mobile_layout: { value: mobileLayout } = {},
    item_count_mobile = {},
    card_radius: { value: cardRadius } = {},
    button_text: { value: button_text } = {},
    padding_top: { value: paddingTop = 16 } = {},
    padding_bottom: { value: paddingBottom = 16 } = {},
    img_container_bg,
    text_color,
    forDarkMode
  } = props;

  const itemCount = Number(item_count?.value ?? 4);
  const itemCountMobile = Number(item_count_mobile?.value ?? 2);
  // console.log("button_text :>> ", button_text);
  // console.log("button_text?.value :>> ", button_text?.value);

  const isDarkMode = forDarkMode?.value || false;

  const isMobileView = useViewport(0, 768);

  const galleryItems = blocks?.length ? blocks : preset?.blocks || [];

  const isStackView = desktopLayout === "grid" || mobileLayout === "grid";
  const isHorizontalView =
    desktopLayout === "horizontal" || mobileLayout === "horizontal";

  const getImgSrcSet = useMemo(() => {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      { breakpoint: { min: 1728 }, width: Math.round(3564 / itemCount) },
      { breakpoint: { min: 1512 }, width: Math.round(3132 / itemCount) },
      { breakpoint: { min: 1296 }, width: Math.round(2700 / itemCount) },
      { breakpoint: { min: 1080 }, width: Math.round(2250 / itemCount) },
      { breakpoint: { min: 900 }, width: Math.round(1890 / itemCount) },
      { breakpoint: { min: 720 }, width: Math.round(1530 / itemCount) },
      { breakpoint: { min: 540 }, width: Math.round(1170 / itemCountMobile) },
      { breakpoint: { min: 360 }, width: Math.round(810 / itemCountMobile) },
      { breakpoint: { min: 180 }, width: Math.round(450 / itemCountMobile) },
    ];
  }, [globalConfig?.img_hd, itemCount, itemCountMobile]);

  console.log("button_text :>> ", button_text);

  const dynamicStyles = {
    padding: "80px 32px",
    maxWidth: "100vw",
    // "--bd-radius": `${(cardRadius || 0) / 2}%`,
    borderRadius: "20px",
    backgroundColor: "#E2E2E2",
  };

  return (
    <div className={`${isDarkMode ? styles.darkModeMainContainer : styles.mainContainer}`}>
      <section className={styles.dynamicStyles}>
        <div className={`fx-title-block ${styles.titleBlock}`}>
          <div>
            {title && (
              <h2 className={`fx-title ${isDarkMode ? styles.darkModeSectionHeading : styles.sectionHeading} fontHeader`}>
                {title}
              </h2>
            )}
            {description && (
              <p className={`fx-description ${isDarkMode ? styles.darkModeDescription : styles.description} b2`}>
                {description}
              </p>
            )}
          </div>
          {button_text && (
            <button
              type="button"
              className={`fx-button btn-secondary ${isDarkMode ? styles.darkModeSectionButton : styles["section-button"]} ${styles.fontBody}`}
            >
              {button_text}
              {!isMobileView && <Vector />}
            </button>
          )}
        </div>
        {isHorizontalView && (
          <HorizontalLayout
            className={`${desktopLayout === "grid" ? styles.hideOnDesktop : ""} ${mobileLayout === "grid" ? styles.hideOnTablet : ""
              }`}
            items={galleryItems}
            globalConfig={globalConfig}
            colCount={itemCount}
            colCountMobile={itemCountMobile}
            sources={getImgSrcSet}
            autoplay={autoplay}
            autoplaySpeed={playSlides * 1000}
          />
        )}
        {isStackView && (
          <StackLayout
            className={`${desktopLayout === "horizontal" ? styles.hideOnDesktop : ""
              } ${mobileLayout === "horizontal" ? styles.hideOnTablet : ""}`}
            items={galleryItems}
            globalConfig={globalConfig}
            colCount={itemCount}
            colCountMobile={itemCountMobile}
            sources={getImgSrcSet}
            desktopLayout={desktopLayout}
            mobileLayout={mobileLayout}
          />
        )}
      </section>
    </div>
  );
}

const StackLayout = ({
  className,
  items,
  globalConfig,
  colCount,
  colCountMobile,
  sources,
}) => {
  const dynamicStyles = {
    "--item-count": `${colCount}`,
    "--item-count-mobile": `${colCountMobile}`,
  };

  return (
    <div className={`${styles.imageGrid} ${className}`} style={dynamicStyles}>
      {items.map(({ props: block }, index) => (
        <div key={index}>
          <FDKLink to={block?.link?.value || ""}>
            <FyImage
              customClass={styles.imageGallery}
              src={block?.image?.value || placeholderImage}
              sources={sources}
              globalConfig={globalConfig}
              isFixedAspectRatio={false}
            />
          </FDKLink>
        </div>
      ))}
    </div>
  );
};

const HorizontalLayout = ({
  className,
  items,
  globalConfig,
  colCount,
  colCountMobile,
  sources,
  autoplay,
  autoplaySpeed,
}) => {
  const config = useMemo(
    () => ({
      dots: false,
      arrows: false,
      infinite: false,
      speed: 500,
      slidesToShow: 4,
      slidesToScroll: 1,
      swipeToSlide: true,
      autoplay,
      autoplaySpeed,
      cssEase: "linear",
      // arrows: getGallery.length > item_count?.value || false,
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      responsive: [
        {
          breakpoint: 800,
          settings: {
            arrows: false,
            slidesToShow: 3,
            slidesToScroll: 1,
            swipe: true,
            swipeToSlide: false,
            touchThreshold: 80,
            draggable: false,
            touchMove: true,
          },
        },
      ],
    }),
    [items.length, colCount, autoplay, autoplaySpeed]
  );
  const configMobile = useMemo(
    () => ({
      dots: false,
      arrows: false,
      infinite: false,
      slidesToShow: 1.2,
      slidesToScroll: 1,
      speed: 500,
      autoplay,
      autoplaySpeed,
      cssEase: "linear",
      centerMode: true,
      centerPadding: "25px",
      swipe: true,
      swipeToSlide: false,
      touchThreshold: 80,
      draggable: false,
      touchMove: true,
      // arrows: getGallery.length > item_count?.value || false,
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
    }),
    [items?.length, colCountMobile, autoplay, autoplaySpeed]
  );

  const bannerRef = useRef(null);
  const isMobileView = useViewport(0, 768);

  return (
    <div
      className={`${styles.imageSlider} ${items?.length <= colCountMobile ? styles.mobileItemLess : ""} ${className}`}
      style={{
        "--slick-dots": `${Math.ceil(items?.length / colCount) * 22 + 10}px`,
        maxWidth: "100vw",
      }}
    >
      <>
        <Slider {...config} ref={bannerRef} className={styles.hideOnMobile}>
          {items.map(({ props: block }, index) => (
            <div key={index} className={`${styles.sliderItem} ${index === 0 ? styles.firstItem : ''}`}>
              <FDKLink to={block?.link?.value || ""}>
                <FyImage
                  customClass={styles.imageGallery}
                  src={block?.image?.value || placeholderImage}
                  sources={sources}
                  globalConfig={globalConfig}
                  isFixedAspectRatio={false}
                />
              </FDKLink>
            </div>
          ))}
        </Slider>
        <span
          className={styles.customPrevBtn}
          onClick={() => bannerRef.current.slickPrev()}
        >
          <ArrowRightIcon />
        </span>
        <span
          className={styles.customNextBtn}
          onClick={() => {
            bannerRef.current.slickNext()
          }}
        >
          <ArrowRightIcon />
        </span>
      </>
      {isMobileView && (
        <Slider
          {...configMobile}
          ref={bannerRef}
          className={styles.showOnMobile}
        >
          {items.map(({ props: block }, index) => (
            <div key={index} className={styles.sliderItem}>
              <FDKLink to={block?.link?.value || ""}>
                <FyImage
                  customClass={styles.imageGallery}
                  src={block?.image_mobile?.value || placeholderImage}
                  sources={sources}
                  globalConfig={globalConfig}
                  isFixedAspectRatio={false}
                />
              </FDKLink>
            </div>
          ))}
        </Slider>
      )}
    </div>
  );
};

export const settings = {
  label: "Latest Collection",
  props: [
    {
      type: "text",
      id: "title",
      default: "Customize Your Style",
      label: "Heading",
    },
    {
      type: "text",
      id: "description",
      default:
        "This flexible gallery lets you highlight key products and promotions, guiding customers to the right places.",
      label: "Description",
    },
    {
      type: "range",
      id: "card_radius",
      min: 0,
      max: 100,
      step: 1,
      unit: "%",
      label: "Card Radius",
      default: 0,
    },
    {
      id: "desktop_layout",
      type: "select",
      options: [
        {
          value: "grid",
          text: "Stack",
        },
        {
          value: "horizontal",
          text: "Horizontal scroll",
        },
      ],
      default: "horizontal",
      label: "Desktop Layout",
      info: "Items per row should be less than number of blocks to show horizontal scroll",
    },
    {
      type: "range",
      id: "item_count",
      min: 3,
      max: 10,
      step: 1,
      unit: "",
      label: "Items per row (Desktop)",
      default: 5,
    },
    {
      id: "mobile_layout",
      type: "select",
      options: [
        {
          value: "grid",
          text: "Stack",
        },
        {
          value: "horizontal",
          text: "Horizontal scroll ",
        },
      ],
      default: "grid",
      label: "Mobile Layout",
      info: "Alignment of content",
    },
    {
      type: "range",
      id: "item_count_mobile",
      min: 1,
      max: 5,
      step: 1,
      unit: "",
      label: "Items per row (Mobile)",
      default: 2,
    },
    {
      type: "checkbox",
      id: "autoplay",
      default: false,
      label: "Auto Play Slides",
    },
    {
      type: "checkbox",
      id: "forDarkMode",
      default: false,
      label: "For Dark Mode",
    },
    {
      type: "range",
      id: "play_slides",
      min: 1,
      max: 10,
      step: 1,
      unit: "sec",
      label: "Change slides every",
      default: 3,
    },
    {
      type: "text",
      id: "button_text",
      default: "View all",
      label: "Button text",
    },
    {
      type: "range",
      id: "padding_top",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Top padding",
      default: 16,
      info: "Top padding for section",
    },
    {
      type: "range",
      id: "padding_bottom",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Bottom padding",
      default: 16,
      info: "Bottom padding for section",
    },
    {
      type: "color",
      id: "img_container_bg",
      category: "Image Container",
      default: "#00000000",
      label: "Container Background Color",
      info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
    },
    {
      type: "color",
      id: "text_color",
      category: "Text",
      default: "#00000000",
      label: "Text Color",
      info: "This color will be used as the text color of the section",
    }
  ],
  blocks: [
    {
      name: "Image card",
      type: "gallery",
      props: [
        {
          type: "image_picker",
          id: "image",
          label: "Image",
          default: "",
          options: {
            aspect_ratio: "209:269",
            aspect_ratio_strict_check: true,
          },
        },
        {
          type: "image_picker",
          id: "image_mobile",
          label: "Image for Mobile",
          default: "",
          options: {
            aspect_ratio: "233:300",
            aspect_ratio_strict_check: true,
          },
        },
        {
          type: "url",
          id: "link",
          label: "Redirect",
          default: "",
          info: "Search Link Type",
        },
      ],
    },
  ],
  preset: {
    blocks: [
      {
        name: "Image card",
        props: {
          image: {
            type: "image_picker",
          },
          link: {
            type: "url",
          },
        },
      },
      {
        name: "Image card",
        props: {
          image: {
            type: "image_picker",
          },
          link: {
            type: "url",
          },
        },
      },
      {
        name: "Image card",
        props: {
          image: {
            type: "image_picker",
          },
          link: {
            type: "url",
          },
        },
      },
      {
        name: "Image card",
        props: {
          image: {
            type: "image_picker",
          },
          link: {
            type: "url",
          },
        },
      },
    ],
  },
};
export default Component;
