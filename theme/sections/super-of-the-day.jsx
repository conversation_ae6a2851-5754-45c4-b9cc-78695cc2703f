import React, { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/effect-cards";
import 'swiper/css/navigation';
import styles from "../styles/sections/super-of-the-day.less";
import { EffectCards, Navigation } from "swiper/modules";
import { useViewport } from "../helper/hooks";
import { FDKLink } from "fdk-core/components";
import HeartIcon from "../assets/images/heart-fill.svg"

export function Component({ props }) {
    const isMobile = useViewport(0, 768);

    const { heading, collection, sub_heading } = props;
    
    // Real product images - replace with your actual collection data
    const originalSlides = collection?.length > 0 ? collection : [
        "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=600&fit=crop&crop=center", // Watch
        "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=600&fit=crop&crop=center", // Sunglasses
        "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=600&fit=crop&crop=center", // Red sneakers
    ];

    // Create loopable slides for smooth infinite scrolling
    const getLoopableSlides = (slides) => {
        if (slides.length === 0) return [];
        
        const minSlides = 8; // Minimum slides needed for smooth infinite loop
        const duplicatedSlides = [];
        
        // Keep duplicating until we have enough slides
        while (duplicatedSlides.length < minSlides) {
            duplicatedSlides.push(...slides);
        }
        
        return duplicatedSlides;
    };

    const loopableSlides = getLoopableSlides(originalSlides);

    return (
        !isMobile ? (
            <div style={{ backgroundColor: "#F2F2F2", padding: "30px 40px" }}>
                <div style={{
                    backgroundColor: "black", 
                    display: "flex", 
                    justifyContent: "space-evenly", 
                    alignItems: "center", 
                    padding: "20px 0px",
                    borderRadius: "30px"
                }}>
                    <div style={{ 
                        width: "50%", 
                        display: "flex", 
                        flexDirection: "column", 
                        justifyContent: "center", 
                        alignItems: "center" 
                    }}>
                        <p className={styles.heading}>{heading}</p>
                        <p className={styles.sub_heading}>{sub_heading}</p>
                    </div>
                    <div>
                        <Swiper
                            effect={"cards"}
                            grabCursor={true}
                            modules={[EffectCards, Navigation]}
                            loop={true}
                            navigation={true}
                            className={`${styles.swiper} ${styles["swiper-slide"]}`}
                            loopAdditionalSlides={3}
                            loopFillGroupWithBlank={false}
                            watchSlidesProgress={true}
                            observer={true}
                            observeParents={true}
                        >
                            {loopableSlides.map((imageUrl, index) => (
                                <SwiperSlide key={`slide-${index}`}>
                                    <img
                                        src={imageUrl}
                                        alt={`slide-${index}`}
                                        style={{
                                            width: "100%",
                                            height: "100%",
                                            borderRadius: "18px",
                                            objectFit: "cover"
                                        }}
                                    />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                        <div style={{ 
                            display: "flex", 
                            gap: "10px", 
                            width: "100%", 
                            marginTop: "40px" 
                        }}>
                            <FDKLink style={{ width: "50%" }}>
                                <div style={{ 
                                    display: "flex", 
                                    alignItems: "center", 
                                    justifyContent: "center", 
                                    gap: "10px", 
                                    backgroundColor: "white", 
                                    borderRadius: "1205px", 
                                    padding: "10px 24px" 
                                }}>
                                    <HeartIcon /> <span>wishlist</span>
                                </div>
                            </FDKLink>
                            <FDKLink style={{ width: "50%" }}>
                                <div style={{ 
                                    backgroundColor: "#FF1E00", 
                                    borderRadius: "1205px", 
                                    padding: "10px 24px", 
                                    textAlign: "center" 
                                }}>
                                    <p>add to bag</p>
                                </div>
                            </FDKLink>
                        </div>
                    </div>
                </div>
            </div>
        ) : (
            <div style={{ 
                backgroundColor: "black", 
                display: "flex", 
                flexDirection: "column", 
                alignItems: "center", 
                padding: "20px 0px" 
            }}>
                <div style={{ 
                    display: "flex", 
                    flexDirection: "column", 
                    justifyContent: "center", 
                    alignItems: "center", 
                    padding: "20px 0px" 
                }}>
                    <p className={styles.heading}>{heading}</p>
                    <p className={styles.sub_heading}>{sub_heading}</p>
                </div>
                <div>
                    <Swiper
                        effect={"cards"}
                        grabCursor={true}
                        modules={[EffectCards]}
                        loop={true}
                        className={`${styles.swiper} ${styles["swiper-slide"]}`}
                        loopAdditionalSlides={3}
                        loopFillGroupWithBlank={false}
                        watchSlidesProgress={true}
                        observer={true}
                        observeParents={true}
                    >
                        {loopableSlides.map((imageUrl, index) => (
                            <SwiperSlide key={`mobile-slide-${index}`}>
                                <img
                                    src={imageUrl}
                                    alt={`mobile-slide-${index}`}
                                    style={{
                                        width: "100%",
                                        height: "100%",
                                        borderRadius: "18px",
                                        objectFit: "cover"
                                    }}
                                />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                    <div style={{ 
                        display: "flex", 
                        gap: "10px", 
                        width: "100%", 
                        marginTop: "40px" 
                    }}>
                        <FDKLink>
                            <div style={{ 
                                display: "flex", 
                                alignItems: "center", 
                                justifyContent: "center", 
                                gap: "10px", 
                                backgroundColor: "white", 
                                borderRadius: "1205px", 
                                padding: "8px 20px", 
                                fontSize: "14px" 
                            }}>
                                <HeartIcon /> <span>wishlist</span>
                            </div>
                        </FDKLink>
                        <FDKLink>
                            <div style={{ 
                                backgroundColor: "#FF1E00", 
                                borderRadius: "1205px", 
                                padding: "8px 20px", 
                                textAlign: "center", 
                                fontSize: "14px" 
                            }}>
                                <p style={{ margin: 0 }}>add to bag</p>
                            </div>
                        </FDKLink>
                    </div>
                </div>
            </div>
        )
    )
}

export const settings = {
    label: "Super of the Day",
    props: [
        {
            type: "text",
            id: "heading",
            default: "",
            label: "Heading",
        },
        {
            type: "text",
            id: "sub_heading",
            default: "",
            label: "Sub Heading",
        },
        {
            type: "collection",
            id: "collection",
            default: "",
            label: "Collection",
        },
        {
            type: "color",
            id: "img_container_bg",
            category: "Image Container",
            default: "#00000000",
            label: "Container Background Color",
            info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
          },
          {
            type: "color",
            id: "text_color",
            category: "Text",
            default: "#00000000",
            label: "Text Color",
            info: "This color will be used as the text color of the section",
          }
    ]
}

export default Component;