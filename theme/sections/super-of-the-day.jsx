import React, { useRef, useState, useEffect, useMemo } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/effect-cards";
import 'swiper/css/navigation';
import styles from "../styles/sections/super-of-the-day.less";
import { EffectCards, Navigation } from "swiper/modules";
import { useViewport } from "../helper/hooks";
import { FDKLink } from "fdk-core/components";
import HeartIcon from "../assets/images/heart-fill.svg"
import { useGlobalStore, useFPI } from "fdk-core/utils";
import {
    FEATURED_COLLECTION,
    COLLECTION_ITEMS,
  } from "../queries/collectionsQuery";
  import Vector from "../assets/images/vector.svg";
  import "../components/product-card/product-card.less";
  import placeholderProduct from "../assets/images/placeholder/featured-collection-product.png";
  import useAddToCartModal from "../page-layouts/plp/useAddToCartModal";
  import {
    useAccounts,
    useWishlist,
    useThemeFeature,
    useWindowWidth,
  } from "../helper/hooks";

export function Component({ props, globalConfig }) {
    const isMobile = useViewport(0, 768);
    const i18nDetails = useGlobalStore(fpi.getters.i18N_DETAILS);
    const [isLoading, setIsLoading] = useState(true);
    const [products, setProducts] = useState([]);
    const [collectionInfo, setCollectionInfo] = useState(null);

    console.log(props, "props");
    
    // Fetch collection data for single collection
    useEffect(() => {
        const fetchCollectionData = async () => {
            setIsLoading(true);
            
            try {
                // Check if collection exists in props
                if (props.collection?.value) {
                    const payload = {
                        slug: props.collection.value,
                        first: 12,
                        pageNo: 1,
                    };

                    console.log(`Fetching collection: ${props.collection.value}`);

                    try {
                        const res = await fpi.executeGQL(FEATURED_COLLECTION, payload);
                        console.log("Collection response:", res);

                        const collectionData = res.data?.collection;
                        const productItems = collectionData?.products?.items || [];

                        setProducts(productItems);
                        setCollectionInfo({
                            name: collectionData?.name || props.collection.value,
                            slug: props.collection.value,
                            productCount: productItems.length,
                            totalProductCount: collectionData?.products?.page_info?.total || 0,
                        });

                    } catch (err) {
                        console.error(`Error fetching collection ${props.collection.value}:`, err);
                        // Set empty state on error
                        setProducts([]);
                        setCollectionInfo({
                            name: props.collection.value,
                            slug: props.collection.value,
                            productCount: 0,
                            totalProductCount: 0,
                        });
                    }
                } else {
                    console.log("No collection specified in props");
                    setProducts([]);
                    setCollectionInfo(null);
                }
            } catch (error) {
                console.error("Error in fetchCollectionData:", error);
                setProducts([]);
                setCollectionInfo(null);
            } finally {
                setIsLoading(false);
            }
        };

        fetchCollectionData();
    }, [props.collection?.value, i18nDetails?.currency?.code]);

    // Create product image slides from the fetched products
    const productSlides = useMemo(() => {
        if (!products || products.length === 0) return [];
        
        return products.map(product => {
            // Get the first available image from the product
            const imageUrl = product.medias?.[0]?.url || 
                           product.images?.[0]?.url || 
                           placeholderProduct;
            return imageUrl;
        });
    }, [products]);

    // Create loopable slides for smooth infinite scrolling
    const getLoopableSlides = (slides) => {
        if (slides.length === 0) return [];
        
        const minSlides = 8; // Minimum slides needed for smooth infinite loop
        const duplicatedSlides = [];
        
        // Keep duplicating until we have enough slides
        while (duplicatedSlides.length < minSlides) {
            duplicatedSlides.push(...slides);
        }
        
        return duplicatedSlides;
    };

    const loopableSlides = getLoopableSlides(productSlides);

    console.log("Product slides:", productSlides);
    console.log("Loopable slides:", loopableSlides);

    // Show loading state
    if (isLoading) {
        return (
            <div style={{ 
                backgroundColor: "#F2F2F2", 
                padding: "30px 40px",
                textAlign: "center" 
            }}>
                <p>Loading collection...</p>
            </div>
        );
    }

    // Show empty state if no products
    if (loopableSlides.length === 0) {
        return (
            <div style={{ 
                backgroundColor: "#F2F2F2", 
                padding: "30px 40px",
                textAlign: "center" 
            }}>
                <p>No products found in this collection.</p>
            </div>
        );
    }

    return (
        !isMobile ? (
            <div style={{ backgroundColor: "#F2F2F2", padding: "30px 40px" }}>
                <div style={{
                    backgroundColor: "black", 
                    display: "flex", 
                    justifyContent: "space-evenly", 
                    alignItems: "center", 
                    padding: "20px 0px",
                    borderRadius: "30px"
                }}>
                    <div style={{ 
                        width: "50%", 
                        display: "flex", 
                        flexDirection: "column", 
                        justifyContent: "center", 
                        alignItems: "center" 
                    }}>
                        <p className={styles.heading}>{props.heading?.value}</p>
                        <p className={styles.sub_heading}>{props.sub_heading?.value}</p>
                        {collectionInfo && (
                            <p className={styles.collection_info}>
                                {collectionInfo.name} ({collectionInfo.productCount} items)
                            </p>
                        )}
                    </div>
                    <div>
                        <Swiper
                            effect={"cards"}
                            grabCursor={true}
                            modules={[EffectCards, Navigation]}
                            loop={true}
                            navigation={true}
                            className={`${styles.swiper} ${styles["swiper-slide"]}`}
                            loopAdditionalSlides={3}
                            loopFillGroupWithBlank={false}
                            watchSlidesProgress={true}
                            observer={true}
                            observeParents={true}
                        >
                            {loopableSlides.map((imageUrl, index) => (
                                <SwiperSlide key={`slide-${index}`}>
                                    <img
                                        src={imageUrl}
                                        alt={`Product ${index + 1}`}
                                        style={{
                                            width: "100%",
                                            height: "100%",
                                            borderRadius: "18px",
                                            objectFit: "cover"
                                        }}
                                        onError={(e) => {
                                            e.target.src = placeholderProduct;
                                        }}
                                    />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                        <div style={{ 
                            display: "flex", 
                            gap: "10px", 
                            width: "100%", 
                            marginTop: "40px" 
                        }}>
                            <FDKLink 
                                to={`/wishlist`}
                                style={{ width: "50%" }}
                            >
                                <div style={{ 
                                    display: "flex", 
                                    alignItems: "center", 
                                    justifyContent: "center", 
                                    gap: "10px", 
                                    backgroundColor: "white", 
                                    borderRadius: "1205px", 
                                    padding: "10px 24px" 
                                }}>
                                    <HeartIcon /> <span>wishlist</span>
                                </div>
                            </FDKLink>
                            <FDKLink 
                                to={`/collection/${collectionInfo?.slug}`}
                                style={{ width: "50%" }}
                            >
                                <div style={{ 
                                    backgroundColor: "#FF1E00", 
                                    borderRadius: "1205px", 
                                    padding: "10px 24px", 
                                    textAlign: "center" 
                                }}>
                                    <p>add to bag</p>
                                </div>
                            </FDKLink>
                        </div>
                    </div>
                </div>
            </div>
        ) : (
            <div style={{ 
                backgroundColor: "black", 
                display: "flex", 
                flexDirection: "column", 
                alignItems: "center", 
                padding: "20px 0px" 
            }}>
                <div style={{ 
                    display: "flex", 
                    flexDirection: "column", 
                    justifyContent: "center", 
                    alignItems: "center", 
                    padding: "20px 0px" 
                }}>
                    <p className={styles.heading}>{props.heading?.value}</p>
                    <p className={styles.sub_heading}>{props.sub_heading?.value}</p>
                    {collectionInfo && (
                        <p className={styles.collection_info}>
                            {collectionInfo.name} ({collectionInfo.productCount} items)
                        </p>
                    )}
                </div>
                <div>
                    <Swiper
                        effect={"cards"}
                        grabCursor={true}
                        modules={[EffectCards]}
                        loop={true}
                        className={`${styles.swiper} ${styles["swiper-slide"]}`}
                        loopAdditionalSlides={3}
                        loopFillGroupWithBlank={false}
                        watchSlidesProgress={true}
                        observer={true}
                        observeParents={true}
                    >
                        {loopableSlides.map((imageUrl, index) => (
                            <SwiperSlide key={`mobile-slide-${index}`}>
                                <img
                                    src={imageUrl}
                                    alt={`Product ${index + 1}`}
                                    style={{
                                        width: "100%",
                                        height: "100%",
                                        borderRadius: "18px",
                                        objectFit: "cover"
                                    }}
                                    onError={(e) => {
                                        e.target.src = placeholderProduct;
                                    }}
                                />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                    <div style={{ 
                        display: "flex", 
                        gap: "10px", 
                        width: "100%", 
                        marginTop: "40px" 
                    }}>
                        <FDKLink to={`/collection/${collectionInfo?.slug}`}>
                            <div style={{ 
                                display: "flex", 
                                alignItems: "center", 
                                justifyContent: "center", 
                                gap: "10px", 
                                backgroundColor: "white", 
                                borderRadius: "1205px", 
                                padding: "8px 20px", 
                                fontSize: "14px" 
                            }}>
                                <HeartIcon /> <span>wishlist</span>
                            </div>
                        </FDKLink>
                        <FDKLink to={`/collection/${collectionInfo?.slug}`}>
                            <div style={{ 
                                backgroundColor: "#FF1E00", 
                                borderRadius: "1205px", 
                                padding: "8px 20px", 
                                textAlign: "center", 
                                fontSize: "14px" 
                            }}>
                                <p style={{ margin: 0 }}>view collection</p>
                            </div>
                        </FDKLink>
                    </div>
                </div>
            </div>
        )
    )
}

export const settings = {
    label: "Super of the Day",
    props: [
        {
            type: "text",
            id: "heading",
            default: "",
            label: "Heading",
        },
        {
            type: "text",
            id: "sub_heading",
            default: "",
            label: "Sub Heading",
        },
        {
            type: "collection",
            id: "collection",
            default: "",
            label: "Collection",
        },
        {
            type: "color",
            id: "img_container_bg",
            category: "Image Container",
            default: "#00000000",
            label: "Container Background Color",
            info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
        },
        {
            type: "color",
            id: "text_color",
            category: "Text",
            default: "#00000000",
            label: "Text Color",
            info: "This color will be used as the text color of the section",
        }
    ]
};

export default Component;