import React, { useMemo } from "react";
import Slider from "react-slick";
import FyImage from "fdk-react-templates/components/core/fy-image/fy-image";
import "fdk-react-templates/components/core/fy-image/fy-image.css";
import styles from "../styles/sections/testimonials.less";
import { useWindowWidth } from "../helper/hooks";
import SliderRightIcon from "../assets/images/glide-arrow-right.svg";
import SliderLeftIcon from "../assets/images/glide-arrow-left.svg";

export function Component({ props, globalConfig, blocks, preset }) {
  const { title, autoplay, slide_interval, padding_top, padding_bottom } =
    props;
  const windowWidth = useWindowWidth();

  const testimonialsList = useMemo(() => {
    const blocksData = blocks?.length > 0 ? blocks : preset?.blocks;
    const testimonial =
      blocksData.length !== 0 &&
      blocksData.filter(
        (block) =>
          block.props.author_image ||
          block.props.author_testimonial ||
          block.props.author_name ||
          block.props.author_description
      );
    if (blocksData.length !== 0) {
      if (windowWidth > 480) {
        return testimonial.slice(0, 8);
      }
      return testimonial.slice(0, 12);
    }
  }, [blocks, preset]);

  const slickSetting = () => {
    return {
      dots: testimonialsList.length > 2,
      arrows: testimonialsList.length > 2,
      focusOnSelect: true,
      infinite: testimonialsList.length > 2,
      speed: 600,
      slidesToShow: 2,
      slidesToScroll: 2,
      autoplay: autoplay?.value && testimonialsList.length > 2,
      autoplaySpeed: Number(slide_interval?.value) * 1000,
      centerMode: testimonialsList.length > 2,
      centerPadding: testimonialsList.length > 2 ? "75px" : "0px",
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      responsive: [
        {
          breakpoint: 1023,
          settings: {
            arrows: false,
            centerPadding: testimonialsList.length > 2 ? "50px" : "0px",
          },
        },
        {
          breakpoint: 768,
          settings: {
            arrows: false,
            centerPadding: testimonialsList.length > 2 ? "64px" : "0px",
          },
        },
      ],
    };
  };

  const slickSettingMobile = () => {
    return {
      dots: false,
      arrows: false,
      slidesToShow: 1,
      slidesToScroll: 1,
      focusOnSelect: true,
      infinite: testimonialsList.length > 1,
      speed: 600,
      autoplay: autoplay?.value && testimonialsList.length > 1,
      autoplaySpeed: Number(slide_interval?.value) * 1000,
      centerMode: testimonialsList.length > 1,
      centerPadding: "50px",
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
    };
  };

  const dynamicStyles = {
    paddingTop: `${padding_top?.value ?? 16}px`,
    paddingBottom: `${padding_bottom?.value ?? 16}px`,
  };

  return (
    <section style={dynamicStyles}>
      <h2 className={`fx-title ${styles.testimonialTitle} fontHeader`}>
        {title?.value}
      </h2>

      <div
        className={`${styles.testimonialSlider} 
          ${testimonialsList?.length === 1 ? styles.oneItem : ""}  
          ${testimonialsList?.length === 2 ? styles.twoItem : ""}
        `}
        style={{
          "--slick-dots": `${Math.ceil(testimonialsList?.length) * 22 + 10}px`,
        }}
      >
        {testimonialsList?.length > 0 && (
          <>
            <Slider className={`${styles.hideOnMobile}`} {...slickSetting()}>
              {testimonialsList.map((block, index) => (
                <TestimonialItem
                  key={`desktop_${index}`}
                  className={styles.sliderItem}
                  testimonial={block.props}
                  globalConfig={globalConfig}
                />
              ))}
            </Slider>
            <Slider
              className={`${styles.hideOnDesktop}`}
              {...slickSettingMobile()}
            >
              {testimonialsList.map((block, index) => (
                <TestimonialItem
                  key={`mobile_${index}`}
                  className={styles.sliderItem}
                  testimonial={block.props}
                  globalConfig={globalConfig}
                />
              ))}
            </Slider>
          </>
        )}
      </div>
    </section>
  );
}

const TestimonialItem = ({ className = "", testimonial, globalConfig }) => {
  const { author_image, author_testimonial, author_name, author_description } =
    testimonial;
  return (
    <div className={className}>
      <div className={`fx-testimonial-card ${styles.testimonial}`}>
        {author_image?.value && (
          <FyImage
            customClass={styles.testimonialImage}
            src={author_image?.value}
            sources={globalConfig?.img_hd ? [] : [{ width: 700 }]}
            isFixedAspectRatio={false}
          />
        )}
        <div
          className={`fx-testimonial-info ${styles.testimonialInfo} ${
            author_image?.value
              ? styles.testimonial__block__info__has__image
              : ""
          }`}
        >
          <div
            className={`fx-text ${styles.testimonialText}`}
            title={author_testimonial?.value}
          >
            {`"${author_testimonial?.value || "Add customer reviews and testimonials to showcase your store's happy customers."}"`}
          </div>
          <div className={styles.testimonialAuthorInfo}>
            <h5
              className={`fx-author ${styles.authorName}`}
              title={author_name?.value}
            >
              {author_name?.value || ""}
            </h5>
            <div
              className={`fx-author-description ${styles.authorDescription} captionNormal`}
              title={author_description?.value}
            >
              {author_description?.value || ""}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const settings = {
  label: "Testimonial",
  name: "testimonials",
  props: [
    {
      type: "text",
      id: "title",
      default: "What People Are Saying About Us ",
      label: "Heading",
    },
    {
      type: "checkbox",
      id: "autoplay",
      default: false,
      label: "AutoPlay Slides",
    },
    {
      type: "range",
      id: "slide_interval",
      min: 1,
      max: 10,
      step: 1,
      unit: "sec",
      label: "Change slides every",
      default: 2,
    },
    {
      type: "range",
      id: "padding_top",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Top padding",
      default: 16,
      info: "Top padding for section",
    },
    {
      type: "range",
      id: "padding_bottom",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Bottom padding",
      default: 16,
      info: "Bottom padding for section",
    },
  ],
  blocks: [
    {
      type: "testimonial",
      name: "Testimonial",
      props: [
        {
          type: "image_picker",
          id: "author_image",
          default: "",
          label: "Image",
          options: {
            aspect_ratio: "1:1",
          },
        },
        {
          type: "textarea",
          id: "author_testimonial",
          label: "Testimonial",
          default:
            "Add customer reviews and testimonials to showcase your store's happy customers.",
          info: "Text for testimonial",
          placeholder: "Text",
        },
        {
          type: "text",
          id: "author_name",
          default: "Author Name",
          label: "Author Name",
        },
        {
          type: "text",
          id: "author_description",
          default: "Author Description",
          label: "Author Description",
        },
      ],
    },
  ],
  preset: {
    blocks: [
      {
        name: "Testimonial",
        props: {
          author_image: {
            type: "image_picker",
            value: "",
          },
          author_testimonial: {
            type: "textarea",
            value:
              "Add customer reviews and testimonials to showcase your store's happy customers.",
          },
          author_name: {
            type: "text",
            value: "Author Description",
          },
          author_description: {
            type: "text",
            value: "Author Description",
          },
        },
      },
      {
        name: "Testimonial",
        props: {
          author_image: {
            type: "image_picker",
            value: "",
          },
          author_testimonial: {
            type: "textarea",
            value:
              "Add customer reviews and testimonials to showcase your store's happy customers.",
          },
          author_name: {
            type: "text",
            value: "Author Description",
          },
          author_description: {
            type: "text",
            value: "Author Description",
          },
        },
      },
      {
        name: "Testimonial",
        props: {
          author_image: {
            type: "image_picker",
            value: "",
          },
          author_testimonial: {
            type: "textarea",
            value:
              "Add customer reviews and testimonials to showcase your store's happy customers.",
          },
          author_name: {
            type: "text",
            value: "Author Description",
          },
          author_description: {
            type: "text",
            value: "Author Description",
          },
        },
      },
    ],
  },
};

export default Component;
