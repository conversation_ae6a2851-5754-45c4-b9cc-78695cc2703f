import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { debounce, getProductImgAspectRatio } from "../helper/utils";
import SearchIcon from "../assets/images/search.svg";
import styles from "../styles/sections/search-bar.less";
import CloseIcon from "../assets/images/close.svg";
import InputSearchIcon from "../assets/images/search.svg";
import FyImage from "fdk-react-templates/components/core/fy-image/fy-image";
import { SEARCH_PRODUCT, AUTOCOMPLETE } from "../queries/headerQuery";
import { useViewport } from "../helper/hooks";
import useSearchModal from "../helper/hooks/useSearchModal";

export function Component({
  screen,
  props,
  globalConfig,
  fpi,
  customSearchClass = "",
  customSearchWrapperClass = "",
  showCloseButton = true,
  alwaysOnSearch = false,
  onFocus = () => { },
  onBlur = () => { },
}) {
  const navigate = useNavigate();
  const isMobile = useViewport(0, 480);

  const [searchData, setSearchData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [showSearch, setShowSearch] = useState(alwaysOnSearch);
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const { showMobileModal, setShowMobileModal, openSearchModal, closeSearchModal } = useSearchModal();


  const inputRef = useRef(null);
  const mobileInputRef = useRef(null);
  const isDoubleRowHeader = globalConfig?.header_layout === "double";
  const isAlgoliaEnabled = globalConfig?.algolia_enabled;

  const Suggested = ["Superdry Polo", "Leather Jacket", "Jeans"];

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    const savedSearches = localStorage.getItem('recentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }
  }, []);

  // Handle focus state when component mounts or user returns to page
  useEffect(() => {
    const handleWindowFocus = () => {
      if (inputRef.current && document.activeElement === inputRef.current) {
        setIsSearchFocused(true);
        setShowSearchSuggestions(true);
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden && inputRef.current && document.activeElement === inputRef.current) {
        setIsSearchFocused(true);
        setShowSearchSuggestions(true);
      }
    };

    window.addEventListener('focus', handleWindowFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    if (inputRef.current && document.activeElement === inputRef.current) {
      setIsSearchFocused(true);
      setShowSearchSuggestions(true);
    }

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Handle mobile modal
  useEffect(() => {
    if (showMobileModal && mobileInputRef.current) {
      // Focus the mobile input when modal opens
      setTimeout(() => {
        mobileInputRef.current.focus();
      }, 100);
    }
  }, [showMobileModal]);

  // Prevent body scroll when mobile modal is open
  useEffect(() => {
    if (showMobileModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showMobileModal]);

  const saveRecentSearch = (search) => {
    const updatedSearches = [search, ...recentSearches.filter(s => s !== search)].slice(0, 5);
    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
  };

  const closeSearch = () => {
    setShowSearch(false);
    setSearchText("");
    setIsSearchFocused(false);
    setShowSearchSuggestions(false);
    closeSearchModal();

    if (inputRef?.current) {
      inputRef.current.value = "";
      inputRef.current.blur();
    }
    if (mobileInputRef?.current) {
      mobileInputRef.current.value = "";
      mobileInputRef.current.blur();
    }
  };

  const handleMobileSearchClick = () => {
    openSearchModal();
  };

  const handleFocus = () => {
    setIsSearchFocused(true);
    setShowSearchSuggestions(true);
    onFocus();
  };

  const handleBlur = (e) => {
    // Check if the click was inside the search suggestions panel
    const searchPanel = document.querySelector(`.${styles.search__suggestions}`);
    if (searchPanel && searchPanel.contains(e.relatedTarget)) {
      return;
    }
    setIsSearchFocused(false);
    setShowSearchSuggestions(false);
    onBlur();
  };

  const getEnterSearchData = (searchText) => {
    setShowSearchSuggestions(false);

    if (isAlgoliaEnabled) {
      const BASE_URL = `${window.location.origin}/ext/algolia/application/api/v1.0/products`;
      const url = new URL(BASE_URL);
      url.searchParams.append("page_size", "4");
      url.searchParams.append("q", searchText);

      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          const productDataNormalization = data.items?.map((item) => ({
            ...item,
            media: item.medias,
          }));
          if (productDataNormalization.length) {
            setSearchData(productDataNormalization);
            setTotalCount(data.page?.item_total || 0);
          } else {
            setSearchData([]);
            setTotalCount(0);
          }
        })
        .finally(() => {
          setShowSearchSuggestions(searchText?.length > 2);
        });
    } else {
      const payload = {
        pageNo: 1,
        search: searchText,
        filterQuery: "",
        enableFilter: false,
        sortOn: "",
        first: 8,
        after: "",
        pageType: "number",
      };
      fpi
        .executeGQL(SEARCH_PRODUCT, payload, { skipStoreUpdate: true })
        .then((res) => {
          setSearchData(res?.data?.products?.items);
          setTotalCount(res?.data?.products?.page?.item_total || 0);
        })
        .finally(() => {
          setShowSearchSuggestions(searchText?.length > 2);
        });
    }
    fpi.executeGQL(AUTOCOMPLETE, { query: searchText });
  };

  const setEnterSearchData = debounce((e) => {
    if (!showSearch) {
      setShowSearch(true);
    }

    setSearchText(e.target.value);
    getEnterSearchData(e.target.value);
  }, 400);

  const redirectToProduct = (link) => {
    if (searchText) {
      saveRecentSearch(searchText);
    }
    navigate(link);
    closeSearch();
  };

  const getProductSearchSuggestions = (results) => results?.slice(0, 4);

  const getDisplayData = (product) => {
    let displayName;

    if (screen === "mobile" && product.name.length > 40) {
      displayName = `${product.name.substring(0, 40)}...`;
    } else if (product.name.length > 95) {
      displayName = `${product.name.substring(0, 95)}...`;
    } else {
      displayName = product.name;
    }

    return <div>{displayName}</div>;
  };

  const getImage = (product) => {
    if (Array.isArray(product?.media)) {
      return product.media?.find((item) => item.type === "image") || "";
    }
    return "";
  };

  const clearAllRecentSearches = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
    setShowSearchSuggestions(true);
    setIsSearchFocused(true);
    const activeInput = isMobile && showMobileModal ? mobileInputRef.current : inputRef.current;
    if (activeInput) {
      activeInput.focus();
    }
  };

  const deleteRecentSearch = (e, searchToDelete) => {
    e.preventDefault();
    e.stopPropagation();
    const updatedSearches = recentSearches.filter(search => search !== searchToDelete);
    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
    setShowSearchSuggestions(true);
    setIsSearchFocused(true);
    const activeInput = isMobile && showMobileModal ? mobileInputRef.current : inputRef.current;
    if (activeInput) {
      activeInput.focus();
    }
  };

  const renderSearchSuggestions = (isModal = false) => (
    <div className={styles["search__suggestions--products"]}>
      {!searchText && (
        <>
          {recentSearches.length > 0 && (
            <>
              <div className={`b1 ${styles["search__suggestions--title"]} fontBody`}>
                <p className={styles.text}>Recents</p>
                <button
                  type="button"
                  className={`${styles.clearAllButton} btnLink`}
                  onClick={clearAllRecentSearches}
                >
                  <p className={styles.text}>Clear All</p>
                </button>
              </div>
              <ul>
                {recentSearches.map((search, index) => (
                  <li
                    key={index}
                    className={`${styles["search__suggestions--item"]} ${styles.flexAlignCenter}`}
                  >
                    <div
                      className={styles.searchItemContent}
                      onClick={() => redirectToProduct(`/products/?q=${search}`)}
                    >
                      <SearchIcon className={styles.recentSearchIcon} />
                      <div className={`${styles.searchText} b1 ${styles.fontBody}`}>
                        {search}
                      </div>
                    </div>
                    <button
                      type="button"
                      className={styles.deleteSearchButton}
                      onClick={(e) => deleteRecentSearch(e, search)}
                      aria-label={`Delete ${search} from recent searches`}
                    >
                      <CloseIcon className={styles.deleteIcon} />
                    </button>
                  </li>
                ))}
              </ul>
            </>
          )}
          <div className={`b1 ${styles["search__suggestions--title"]} fontBody`}>
            <p className={styles.text}>Suggested</p>
          </div>
          <ul>
            {Suggested.map((search, index) => (
              <li
                key={index}
                className={`${styles["search__suggestions--item"]} ${styles.flexAlignCenter}`}
              >
                <div
                  className={styles.searchItemContent}
                  onClick={() => redirectToProduct(`/products/?q=${search}`)}
                >
                  <SearchIcon className={styles.recentSearchIcon} />
                  <div className={`${styles.searchText} b1 ${styles.fontBody}`}>
                    {search}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </>
      )}
      {searchText && (
        <>
          <div
            className={`b1 ${styles["search__suggestions--title"]} fontBody`}
            style={{
              display: searchData?.length > 0 ? "block" : "none",
            }}
          >
            <p className={styles.text}>SEARCH RESULTS</p>
          </div>
          <ul
            style={{
              display: searchData?.length > 0 ? "block" : "none",
            }}
          >
            {getProductSearchSuggestions(searchData)?.map(
              (product, index) => (
                <li
                  key={index}
                  className={`${styles["search__suggestions--item"]} ${styles.flexAlignCenter}`}
                  onClick={() =>
                    redirectToProduct(`/product/${product.slug}`)
                  }
                >
                  <SearchIcon className={styles.searchIcon} />
                  <div className={`${styles.searchText} b1 ${styles.fontBody}`}>
                    {getDisplayData(product)} <span className={styles.totalCount}>({totalCount})</span>
                  </div>
                </li>
              )
            )}
          </ul>
          <ul
            style={{
              display: searchData?.length === 0 ? "block" : "none",
            }}
          >
            <button
              type="button"
              onClick={() =>
                redirectToProduct(`/products/?q=${searchText}`)
              }
            >
              <li
                className={`${styles.flexAlignCenter} ${styles.noResult} fontBody`}
              >
                <p className={styles.text}>No match found</p>
              </li>
            </button>
          </ul>
          <div
            className={styles["search__suggestions--button"]}
            style={{
              display: totalCount > 4 ? "block" : "none",
            }}
          >
            <button
              type="button"
              className="btnLink fontBody"
              onClick={() =>
                redirectToProduct(`/products/?q=${searchText}`)
              }
            >
              <span><p className={styles.text}>SEE ALL {totalCount} PRODUCTS</p></span>
            </button>
          </div>
        </>
      )}
    </div>
  );

  if (!isMobile) {
    // Desktop version
    return (
      <div className={`${styles.search__wrapper} ${customSearchWrapperClass}`}>
        <div className={styles.search__input}>
          <div className={`${styles.searchflex}`}>
            <SearchIcon />
            <input
              ref={inputRef}
              className={`${styles["search__input--text"]} ${isSearchFocused ? styles["search__input--removeSpace"] : ""}`.trim()}
              type="text"
              id="searchInput"
              autoComplete="off"
              defaultValue={searchText}
              placeholder="Hi User! check out 'winter collection' "
              onChange={(e) => setEnterSearchData(e)}
              onKeyUp={(e) =>
                e.key === "Enter" &&
                e.target?.value &&
                redirectToProduct(`/products/?q=${e.target?.value}`)
              }
              onFocus={handleFocus}
              onBlur={handleBlur}
              aria-labelledby="search-input-label"
              aria-label="search-input-label"
            />
            {showCloseButton && searchText && (
              <CloseIcon
                className={`${styles["search--closeIcon"]} ${styles.headerIcon}`}
                onClick={closeSearch}
              />
            )}
          </div>
        </div>
        <div
          className={styles.search__suggestions}
          style={{ display: isSearchFocused ? "block" : "none" }}
          onMouseDown={(e) => e.preventDefault()}
        >
          {renderSearchSuggestions()}
        </div>
      </div>
    );
  }

  // Mobile version
  return (
    <>
      {/* Mobile Search Bar */}
      <div className={`${styles.search__wrapper} ${customSearchWrapperClass}`}>
        <div className={styles.search__input}>
          <div className={`${styles.searchflex}`} onClick={handleMobileSearchClick}>
            <SearchIcon />
            <input
              ref={inputRef}
              className={`${styles["search__input--text"]} ${styles["search__input--mobile"]}`}
              type="text"
              id="searchInput"
              autoComplete="off"
              placeholder="Hi Sanjay! checkout 'winter jackets'"
              readOnly
              onClick={handleMobileSearchClick}
              aria-labelledby="search-input-label"
              aria-label="search-input-label"
            />
          </div>
        </div>
      </div>

      {/* Mobile Search Modal */}
      {showMobileModal && (
        <div className={styles.mobileSearchModal}>
          <div className={styles.mobileSearchOverlay} onClick={closeSearch} />
          <div className={styles.mobileSearchContent}>
            {/* Modal Header with Search Bar */}
            <div className={styles.mobileSearchHeader}>
              <div className={styles.mobileSearchInputWrapper}>
                <div className={`${styles.searchflex} ${styles.mobileSearchFlex}`}>
                  <SearchIcon />
                  <input
                    ref={mobileInputRef}
                    className={`${styles["search__input--text"]} ${styles["search__input--mobile-active"]}`}
                    type="text"
                    autoComplete="off"
                    placeholder="Hi Sanjay! checkout 'winter jackets'"
                    onChange={(e) => setEnterSearchData(e)}
                    onKeyUp={(e) =>
                      e.key === "Enter" &&
                      e.target?.value &&
                      redirectToProduct(`/products/?q=${e.target?.value}`)
                    }
                    aria-label="Mobile search input"
                  />
                  <CloseIcon
                    className={`${styles["search--closeIcon"]} ${styles.headerIcon}`}
                    onClick={closeSearch}
                  />
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className={styles.mobileSearchBody}>
              {renderSearchSuggestions(true)}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export const settings = {
  label: "Search",
  props: [
    {
      type: "text",
      id: "title",
      default: "Winter Jackets",
      label: "Heading",
    },
  ],
};

export default Component;