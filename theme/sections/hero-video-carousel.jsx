import React, { useEffect, useRef, useState } from "react";
import { useSnapCarousel } from "react-snap-carousel";
import styles from "../styles/sections/hero-video-carousel.less";
import { useViewport } from "../helper/hooks";

export function Component({ props, blocks }) {
  const { autoplay, speed } = props;
  const { scrollRef, pages, activePageIndex, next, goTo } = useSnapCarousel();
  const videoDurationsRef = useRef({});
  const [progressMap, setProgressMap] = useState({});

  useEffect(() => {
    if (!autoplay?.value || !blocks?.length) return;

    const block = blocks[activePageIndex];
    const isVideo =
      block?.props?.videoFile?.value || block?.props?.mobileVideoFile?.value;
    const isImage =
      block?.props?.imageFile?.value || block?.props?.mobileImageFile?.value;

    if (isVideo) return;

    const duration = speed?.value || 3;

    const timeoutId = setTimeout(() => {
      if (activePageIndex === pages.length - 1) {
        goTo(0);
      } else {
        next();
      }
    }, duration * 1000);

    return () => clearTimeout(timeoutId);
  }, [
    activePageIndex,
    autoplay?.value,
    blocks,
    pages.length,
    next,
    goTo,
    speed?.value,
  ]);

  const handleProgressUpdate = (index, progress) => {
    setProgressMap((prev) => ({ ...prev, [index]: progress }));
  };


  const handleVideoEnd = () => {
    if (activePageIndex === pages.length - 1) {
      goTo(0);
    } else {
      next();
    }
  };

  const isMobile = useViewport(0,768);

  // const [isMobile, setIsMobile] = useState(false);
  // useEffect(() => {
  //   const checkMobile = () => setIsMobile(window.innerWidth < 768);
  //   checkMobile();
  //   window.addEventListener("resize", checkMobile);
  //   return () => window.removeEventListener("resize", checkMobile);
  // }, []);

  return (
    <div className={styles.heroVideoCarousel}>
      <div className={styles.carouselContainer}>
        <div ref={scrollRef} className={styles.carouselItems}>
          {blocks?.map((block, index) => (
            <div key={index} className={styles.carouselItem}>
              <HeroCarousel
                block={block}
                isMobile={isMobile}
                isActive={activePageIndex === index}
                videoDurationsRef={videoDurationsRef}
                onLoadedMetadata={(duration) => {
                  videoDurationsRef.current[index] = duration;
                }}
                onProgressUpdate={(progress) =>
                  handleProgressUpdate(index, progress)
                }
                onVideoEnd={handleVideoEnd}
                imageDuration={(speed?.value || 3) * 1000}
              />
            </div>
          ))}
        </div>
        <div className={styles.carouselProgressBarWrapper}>
          {blocks.map((_, i) => (
            <div
              key={i}
              className={styles.progressTrack}
              onClick={() => {
                if (activePageIndex !== i) {
                  goTo(i);
                }
              }}
            >
              <div
                className={styles.progressFill}
                style={{
                  width:
                    i < activePageIndex
                      ? "100%"
                      : i === activePageIndex
                        ? `${progressMap[i] || 0}%`
                        : "0%",
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function HeroCarousel({
  block,
  isMobile,
  isActive,
  onLoadedMetadata,
  onProgressUpdate,
  onVideoEnd,
  imageDuration = 3000
}) {
  const {
    videoFile: desktopVideo,
    mobileVideoFile,
    imageFile: desktopImage,
    mobileImageFile,
  } = block.props || {};
  const videoRef = useRef(null);

  const videoFile =
    isMobile && mobileVideoFile?.value ? mobileVideoFile : desktopVideo;
  const imageFile =
    isMobile && mobileImageFile?.value ? mobileImageFile : desktopImage;

  useEffect(() => {
    if (videoRef.current) {
      if (isActive) {
        videoRef.current
          .play()
          .catch((e) => console.log("Auto-play prevented:", e));
      } else {
        videoRef.current.pause();
      }
    }
  }, [isActive]);

  useEffect(() => {
    const videoElement = videoRef.current;

    const handleLoadedMetadata = () => {
      onLoadedMetadata(
        isNaN(videoElement.duration) ? 0 : videoElement.duration
      );
    };

    if (videoElement?.readyState >= 1 && !isNaN(videoElement?.duration)) {
      handleLoadedMetadata();
    } else {
      videoElement?.addEventListener("loadedmetadata", handleLoadedMetadata);
      return () => {
        videoElement?.removeEventListener(
          "loadedmetadata",
          handleLoadedMetadata
        );
      };
    }
  }, [videoFile, onLoadedMetadata]);

  useEffect(() => {
    let intervalId;

    if (imageFile?.value && isActive && !videoFile?.value) {
      let progress = 0;
      const updateInterval = 50; // ms
      const steps = imageDuration / updateInterval;
      const increment = 100 / steps;

      intervalId = setInterval(() => {
        progress += increment;
        if (progress >= 100) {
          onProgressUpdate(100);
          clearInterval(intervalId);
          onVideoEnd();
        } else {
          onProgressUpdate(progress);
        }
      }, updateInterval);
    }

    return () => clearInterval(intervalId);
  }, [isActive, imageFile?.value, videoFile?.value, imageDuration]);

  const handleTimeUpdate = () => {
    if (videoRef.current && isActive) {
      const progress =
        (videoRef.current.currentTime / videoRef.current.duration) * 100;
      onProgressUpdate(progress);
    }
  };

  return (
    <div className={styles.videoItem}>
      {videoFile?.value ? (
        <video
          ref={videoRef}
          className={styles.video}
          muted
          playsInline
          preload="metadata"
          controls={false}
          onTimeUpdate={handleTimeUpdate}
          onEnded={onVideoEnd}
          loop={false}
        >
          <source src={videoFile.value} type="video/mp4" />
        </video>
      ) : (
        imageFile?.value && (
          <img
            src={imageFile?.value || "/placeholder.svg"}
            alt="Carousel slide"
            className={styles.image}
          />
        )
      )}
    </div>
  );
}

export const settings = {
  label: "hero-video-carousel",
  props: [
    {
      type: "checkbox",
      id: "autoplay",
      default: true,
      label: "Autoplay",
      info: "Check to enable autoplay",
    },
    {
      type: "range",
      id: "speed",
      min: 1,
      max: 4,
      step: 1,
      default: 1,
      label: "Speed of Autoplay",
      info: "Speed is only for images, not videos",
    },
  ],

  blocks: [
    {
      name: "HeroVideoCarousel",
      type: "HeroVideo-Carousel",
      props: [
        {
          type: "video",
          id: "videoFile",
          default: false,
          label: "Desktop Video",
        },
        {
          type: "video",
          id: "mobileVideoFile",
          default: false,
          label: "Mobile Video",
        },
        {
          id: "imageFile",
          type: "image_picker",
          label: "Desktop Banner",
          default: "",
        },
        {
          id: "mobileImageFile",
          type: "image_picker",
          label: "Mobile/Tablet Banner",
          default: ""
        },
      ],
    },
  ],
};

export default Component;
