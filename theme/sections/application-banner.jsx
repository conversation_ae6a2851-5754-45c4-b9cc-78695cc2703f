import React from "react";
import FyImage from "fdk-react-templates/components/core/fy-image/fy-image";
import "fdk-react-templates/components/core/fy-image/fy-image.css";
import placeholderDesktop from "../assets/images/placeholder/application-banner-desktop.png";
import placeholderMobile from "../assets/images/placeholder/application-banner-mobile.png";
import { FDKLink } from "fdk-core/components";
import styles from "../styles/sections/application-banner.less";
import Hotspot from "../components/hotspot/product-hotspot";
import { useViewport } from "../helper/hooks";
import Vector from "../assets/images/vector.svg"

export function Component({ props, blocks, globalConfig }) {
  const isMobile = useViewport(0, 540);
  const {
    image_desktop,
    image_mobile,
    banner_link,
    padding_top,
    padding_bottom,
    hover_application_banner,
    heading,
    sub_heading,
    description,
    button_text,
    img_container_bg,
    text_color
  } = props;

  const dynamicBoxStyle = (block) => {
    return {
      "--x_position": `${block.props?.x_position?.value || 0}%`,
      "--y_position": `${block.props?.y_position?.value || 0}%`,
      "--box_width": `${block.props?.box_width?.value || 0}%`,
      "--box_height": `${block.props?.box_height?.value || 0}%`,
      "--x_offset": `-${block.props?.x_position?.value || 0}%`,
      "--y_offset": `-${block.props?.y_position?.value || 0}%`,
    };
  };

  const desktopImage = image_desktop || placeholderDesktop;
  const mobileImage = image_mobile || placeholderMobile;

  const getImgSrcSet = () => {
    if (globalConfig?.img_hd) {
      return [
        { breakpoint: { min: 481 } },
        { breakpoint: { max: 540 }, url: mobileImage },
      ];
    }
    return [
      { breakpoint: { min: 1728 }, width: 3564 },
      { breakpoint: { min: 1512 }, width: 3132 },
      { breakpoint: { min: 1296 }, width: 2700 },
      { breakpoint: { min: 1080 }, width: 2250 },
      { breakpoint: { min: 900 }, width: 1890 },
      { breakpoint: { min: 720 }, width: 1530 },
      { breakpoint: { max: 180 }, width: 450, url: mobileImage },
      { breakpoint: { max: 360 }, width: 810, url: mobileImage },
      { breakpoint: { max: 540 }, width: 1170, url: mobileImage },
    ];
  };

  const getHotspots = () => {
    return {
      desktop: blocks?.filter((block) => block?.type === "hotspot_desktop"),
      mobile: blocks?.filter((block) => block?.type === "hotspot_mobile"),
    };
  };

  const dynamicStyles = {
    paddingTop: `${padding_top?.value ?? 0}px`,
    paddingBottom: `${padding_bottom?.value ?? 16}px`,
    backgroundColor: img_container_bg,
  };

  return (
    <section
      className={styles.applicationBannerContainer}
      style={{
        ...dynamicStyles,
        backgroundImage: `url(${isMobile ? mobileImage : desktopImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative'
      }}
    >
      <div className={styles.contentWrapper}>
        <h2 className={styles.heading}>
          <span style={{ color: text_color }}>{heading}</span>
          <span className={styles.redText}> {sub_heading}</span>
        </h2>
        <p className={styles.description} style={{ color: text_color }}>
          {description}
        </p>
        <FDKLink>
        <button className={styles.button} style={{ color: text_color }}>
          {button_text}
          <Vector style={{color: text_color}}/>
        </button>
        </FDKLink>
        </div>
    </section>
  );
}

export const settings = {
  label: "Application Banner",
  props: [
    {
      type: "text",
      id: "heading",
      label: "Title",
      default: "",
      info: "Title for section",
    },
    {
      type: "text",
      id: "sub_heading",
      label: "Sub Heading",
      default: "",
      info: "Sub Heading for section",
    },
    {
      type: "text",
      id: "description",
      label: "Description",
      default: "",
      info: "Description for section",
    },
    {
      type: "text",
      id: "button_text",
      label: "Button Text",
      default: "",
      info: "Button Text for section",
    },
    {
      type: "image_picker",
      id: "image_desktop",
      label: "Desktop Image",
      default: "",
      options: {
        aspect_ratio: "19:6",
      },
    },
    {
      type: "image_picker",
      id: "image_mobile",
      label: "Mobile Image",
      default: "",
      options: {
        aspect_ratio: "4:5",
      },
    },
    {
      type: "url",
      id: "banner_link",
      default: "",
      label: "Redirect Link",
    },
    {
      type: "range",
      id: "padding_top",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Top padding",
      default: 0,
      info: "Top padding for section",
    },
    {
      type: "range",
      id: "padding_bottom",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Bottom padding",
      default: 16,
      info: "Bottom padding for section",
    },
    {
      type: "checkbox",
      id: "hover_application_banner",
      label: "Enable hover effect on Banner",
      default: false,
    },
    {
      type: "color",
      id: "img_container_bg",
      category: "Image Container",
      default: "#00000000",
      label: "Container Background Color",
      info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
    },
    {
      type: "color",
      id: "text_color",
      category: "Text",
      default: "#00000000",
      label: "Text Color",
      info: "This color will be used as the text color of the section",
    }

  ],
  blocks: [
    {
      type: "hotspot_desktop",
      name: "Hotspot Desktop",
      props: [
        {
          type: "select",
          id: "pointer_type",
          label: "Pointer Type",
          options: [
            {
              value: "box",
              text: "Box",
            },
            {
              value: "pointer",
              text: "Pointer",
            },
          ],
          default: "box",
        },
        {
          type: "checkbox",
          id: "edit_visible",
          default: true,
          label: "Show Clickable Area",
        },
        {
          type: "range",
          id: "x_position",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Horizontal Position",
          default: 50,
        },
        {
          type: "range",
          id: "y_position",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Vertical Position",
          default: 50,
        },
        {
          type: "range",
          id: "box_width",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Width",
          default: 15,
          info: "Only applicable for box pointer type",
        },
        {
          type: "range",
          id: "box_height",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Height",
          default: 15,
          info: "Only applicable for box pointer type",
        },
        {
          type: "image_picker",
          id: "hotspot_image",
          label: "Hotspot Hover Image",
          options: {
            aspect_ratio: "1:1",
            aspect_ratio_strict_check: true,
          },
          info: "Only applicable for circular pointer type",
        },
        {
          type: "text",
          id: "hotspot_header",
          label: "Header",
          placeholder: "Header",
          value: "",
          info: "Only applicable for circular pointer type",
        },
        {
          type: "textarea",
          id: "hotspot_description",
          label: "Description",
          default: "",
          info: "Only applicable for circular pointer type",
        },
        {
          type: "text",
          id: "hotspot_link_text",
          label: "Hover Link Text",
          placeholder: "Link text",
          value: "",
          info: "Only applicable for circular pointer type",
        },
        {
          type: "url",
          id: "redirect_link",
          label: "Redirect Link",
        },
      ],
    },
    {
      type: "hotspot_mobile",
      name: "Hotspot Mobile",
      props: [
        {
          type: "select",
          id: "pointer_type",
          label: "Pointer Type",
          options: [
            {
              value: "box",
              text: "Box",
            },
            {
              value: "pointer",
              text: "Pointer",
            },
          ],
          default: "box",
        },
        {
          type: "checkbox",
          id: "edit_visible",
          default: true,
          label: "Show Clickable Area",
        },
        {
          type: "range",
          id: "x_position",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Horizontal Position",
          default: 50,
        },
        {
          type: "range",
          id: "y_position",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Vertical Position",
          default: 50,
        },
        {
          type: "range",
          id: "box_width",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Width",
          default: 15,
          info: "Only applicable for box pointer type",
        },
        {
          type: "range",
          id: "box_height",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          label: "Height",
          default: 15,
          info: "Only applicable for box pointer type",
        },
        {
          type: "image_picker",
          id: "hotspot_image",
          label: "Hotspot Hover Image",
          options: {
            aspect_ratio: "1:1",
            aspect_ratio_strict_check: true,
          },
          info: "Only applicable for circular pointer type",
        },
        {
          type: "text",
          id: "hotspot_header",
          label: "Header",
          placeholder: "Header",
          value: "",
          info: "Only applicable for circular pointer type",
        },
        {
          type: "textarea",
          id: "hotspot_description",
          label: "Description",
          default: "",
          info: "Only applicable for circular pointer type",
        },
        {
          type: "text",
          id: "hotspot_link_text",
          label: "Hover Link Text",
          placeholder: "Link text",
          value: "",
          info: "Only applicable for circular pointer type",
        },
        {
          type: "url",
          id: "redirect_link",
          label: "Redirect Link",
        },
      ],
    },
  ],
};
export default Component;
