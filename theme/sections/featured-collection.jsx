import React, { useEffect, useMemo, useRef, useState, Suspense } from "react";
import { FDKLink } from "fdk-core/components";
import { useGlobalStore, useFPI } from "fdk-core/utils";
import Slider from "react-slick";
// import ProductCard from "fdk-react-templates/components/product-card/product-card";
import ProductCard from "../components/product-card/product-card";
import styles from "../styles/sections/featured-collection.less";
// import FyImage from "fdk-react-templates/components/core/fy-image/fy-image";
import FyImage from "../components/core/fy-image/fy-image";
import "fdk-react-templates/components/core/fy-image/fy-image.css";
import SliderRightIcon from "../assets/images/glide-arrow-right.svg";
import SliderLeftIcon from "../assets/images/glide-arrow-left.svg";
import ArrowRightIcon from "../assets/images/arrow-right.svg";
import { FEATURED_COLLECTION } from "../queries/collectionsQuery";
// import "fdk-react-templates/components/product-card/product-card.css";
import Vector from "../assets/images/vector.svg";
import "../components/product-card/product-card.less";
import placeholderBanner from "../assets/images/placeholder/featured-collection-banner.png";
import placeholderProduct from "../assets/images/placeholder/featured-collection-product.png";
import useAddToCartModal from "../page-layouts/plp/useAddToCartModal";
import {
  useViewport,
  useAccounts,
  useWishlist,
  useThemeFeature,
  useWindowWidth,
} from "../helper/hooks";
import { getProductImgAspectRatio } from "../helper/utils";
const Modal = React.lazy(
  () => import("fdk-react-templates/components/core/modal/modal")
);
const AddToCart = React.lazy(
  () =>
    import(
      "fdk-react-templates/page-layouts/plp/Components/add-to-cart/add-to-cart"
    )
);
const SizeGuide = React.lazy(
  () =>
    import(
      "fdk-react-templates/page-layouts/plp/Components/size-guide/size-guide"
    )
);
import "fdk-react-templates/page-layouts/plp/Components/size-guide/size-guide.css";
import "fdk-react-templates/page-layouts/plp/Components/add-to-cart/add-to-cart.css";

export function Component({ props, globalConfig }) {
  const fpi = useFPI();
  const CONFIGURATION = useGlobalStore(fpi.getters.CONFIGURATION);
  const listingPrice =
    CONFIGURATION?.app_features?.common?.listing_price?.value || "range";
  const bannerRef = useRef(null);
  const isTablet = useViewport(0, 768);

  const addToCartConfigs = {
    mandatory_pincode: props.mandatory_pincode?.value,
    hide_single_size: props.hide_single_size?.value,
    preselect_size: props.preselect_size?.value,
  };
  const { isInternational } = useThemeFeature({ fpi });
  const addToCartModalProps = useAddToCartModal({
    fpi,
    pageConfig: addToCartConfigs,
  });
  const { isLoggedIn, openLogin } = useAccounts({ fpi });
  const { toggleWishlist, followedIdList } = useWishlist({ fpi });
  const {
    handleAddToCart,
    isOpen: isAddToCartOpen,
    showSizeGuide,
    handleCloseSizeGuide,
    ...restAddToModalProps
  } = addToCartModalProps;
  const {
    heading,
    description,
    item_count,
    mobile_layout,
    desktop_layout,
    img_fill,
    img_container_bg,
    button_text,
    button_position,
    collection,
    show_add_to_cart,
    item_count_mobile,
    show_view_all,
    max_count,
    text_alignment,
    img_resize,
    img_resize_mobile,
    padding_top,
    padding_bottom,
  } = props;
  const itemCountMobile = Number(item_count_mobile?.value ?? 1);

  const [isLoading, setIsLoading] = useState(
    !!collection?.value ? true : false
  );

  const showAddToCart =
    !isInternational && show_add_to_cart?.value && !globalConfig?.disable_cart;
  const customValues = useGlobalStore(fpi?.getters?.CUSTOM_VALUE);
  const getGallery =
    customValues?.[`featuredCollectionData-${collection?.value}`]?.data
      ?.collection?.products?.items ?? [];
  const bannerUrl =
    customValues?.[`featuredCollectionData-${collection?.value}`]?.data
      ?.collection?.banners?.portrait?.url || placeholderBanner;
  const imgAlt =
    customValues?.[`featuredCollectionData-${collection?.value}`]?.data
      ?.collection?.banners?.portrait?.alt || "collection";
  const slug =
    customValues?.[`featuredCollectionData-${collection?.value}`]?.data
      ?.collection?.slug ?? "";
  const windowWidth = useWindowWidth();
  const locationDetails = useGlobalStore(fpi?.getters?.LOCATION_DETAILS);
  const i18nDetails = useGlobalStore(fpi.getters.i18N_DETAILS);
  const [isClient, setIsClient] = useState(false);

  const imagesForScrollView = useMemo(() => {
    if (!getGallery) return [];
    return getGallery.slice(0, max_count?.value);
  }, [getGallery, max_count?.value]);

  const config = useMemo(() => {
    const itemCount = Number(item_count?.value ?? 4);
    const itemLength = imagesForScrollView?.length;
    return {
      dots: itemLength > itemCount,
      arrows: itemLength > itemCount,
      infinite: itemLength > itemCount,
      speed: 300,
      slidesToShow: itemCount,
      slidesToScroll: 1,
      swipeToSlide: true,
      autoplay: false,
      cssEase: "linear",
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      responsive: [
        {
          breakpoint: 780,
          settings: {
            arrows: false,
            slidesToShow: 3,
            slidesToScroll: 3,
            dots: true,
            swipe: true,
            swipeToSlide: false,
            touchThreshold: 80,
            draggable: false,
            touchMove: true,
          },
        },
      ],
    };
  }, [item_count?.value, imagesForScrollView?.length]);

  const configMobile = useMemo(() => {
    return {
      dots: false,
      arrows: false,
      speed: 300,
      slidesToShow: 2,
      slidesToScroll: 1,
      swipeToSlide: true,
      swipe: true,
      autoplay: false,
      infinite: false,
      cssEase: "linear",
      nextArrow: <SliderRightIcon />,
      prevArrow: <SliderLeftIcon />,
      // centerMode: imagesForScrollView?.length > itemCountMobile,
      // centerPadding: "25px",
      // touchThreshold: 80,
      draggable: false,
      touchMove: true,
    };
  }, [itemCountMobile, imagesForScrollView?.length]);

  const bannerConfig = useMemo(
    () => ({
      dots: false,
      speed: 500,
      slidesToShow: 2.75,
      slidesToScroll: 1,
      infinite: false,
      cssEase: "linear",
      arrows: false,
      centerMode: false,
      swipe: true,
      swipeToSlide: true,
      responsive: [
        {
          breakpoint: 780,
          settings: {
            arrows: false,
            dots: false,
            slidesToShow: 2.5,
            slidesToScroll: 1,
          },
        },
        {
          breakpoint: 500,
          settings: {
            dots: false,
            arrows: false,
            swipe: true,
            swipeToSlide: true,
            slidesToShow: 2,
            slidesToScroll: 1,
            infinite: false,
            // centerMode: imagesForScrollView?.length > 1,
            // centerPadding: "25px",
          },
        },
      ],
    }),
    [imagesForScrollView.length]
  );

  useEffect(() => {
    setIsClient(true);
    if (collection?.value) {
      const payload = {
        slug: collection?.value,
        first: 12,
        pageNo: 1,
      };
      fpi.executeGQL(FEATURED_COLLECTION, payload).then((res) => {
        setIsLoading(false);
        return fpi.custom.setValue(
          `featuredCollectionData-${collection?.value}`,
          res
        );
      });
    }
  }, [collection, locationDetails?.pincode, i18nDetails?.currency?.code]);

  function getImgSrcSet() {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      { breakpoint: { min: 1728 }, width: 1691 },
      { breakpoint: { min: 1512 }, width: 1487 },
      { breakpoint: { min: 1296 }, width: 1282 },
      { breakpoint: { min: 1080 }, width: 1069 },
      { breakpoint: { min: 900 }, width: 897 },
      { breakpoint: { min: 720 }, width: 1530 },
      { breakpoint: { min: 540 }, width: 1170 },
      { breakpoint: { min: 360 }, width: 810 },
      { breakpoint: { min: 180 }, width: 450 },
    ];
  }

  function getWidthByCount() {
    if (windowWidth <= 768) {
      return getGallery?.length <= 3 ? getGallery?.length : 3;
    }
    return getGallery?.length < item_count?.value
      ? getGallery?.length
      : item_count?.value;
  }

  function imagesForStackedView() {
    const itemCount = item_count?.value;

    if (!getGallery) return [];

    if (windowWidth <= 480) {
      return getGallery.slice(0, 4);
    }
    if (windowWidth <= 768) {
      return getGallery.slice(0, 6);
    }
    return getGallery.slice(0, itemCount * 2);
  }

  function showStackedView() {
    if (windowWidth <= 768) {
      return (
        mobile_layout?.value === "grid" ||
        mobile_layout?.value === "banner_stacked"
      );
    }
    return desktop_layout?.value === "grid";
  }

  function showScrollView() {
    if (windowWidth <= 768) {
      return mobile_layout?.value === "horizontal";
    }
    return desktop_layout?.value === "horizontal";
  }

  function showBannerScrollView() {
    if (windowWidth <= 768) {
      return mobile_layout?.value === "banner_horizontal_scroll";
    }
    return desktop_layout?.value === "banner_horizontal_scroll";
  }

  const handleWishlistToggle = (data) => {
    if (!isLoggedIn) {
      openLogin();
      return;
    }
    toggleWishlist(data);
  };

  const imgSrcSet = useMemo(() => {
    if (globalConfig?.img_hd) {
      return [];
    }
    return [
      { breakpoint: { min: 481 }, width: img_resize?.value ?? 300 },
      { breakpoint: { max: 480 }, width: img_resize_mobile?.value ?? 500 },
    ];
  }, [globalConfig?.img_hd, img_resize?.value, img_resize_mobile?.value]);

  const titleSizeDesktop = "32px";
  const titleSizeTablet = "28px";

  const dynamicStyles = {
    padding: "58px 0 32px 58px",
    // paddingTop: `${padding_top?.value ?? 16}px`,
    // paddingBottom: `${padding_bottom?.value ?? 16}px`,
    // "--bg-color": `${img_container_bg?.value || "#00000000"}`,
    background: "linear-gradient(0deg, #593630 -45.88%, #C5A175 98.11%)",
  };

  return (
    <>
      <section
        className={styles.sectionWrapper}
        //  style={dynamicStyles}
      >
        <div
          className={`fx-title-block ${styles.titleBlock}
          
            `}
          // style={{
          //   alignItems:
          //     text_alignment?.value === "left"
          //       ? "flex-start"
          //       : text_alignment?.value === "right"
          //         ? "flex-end"
          //         : "center",
          // }}
        >
          {/* {heading?.value?.length > 0 && (
            <h2
              className={`fx-title ${styles.sectionHeading} fontHeader`}
              style={{
                textAlign: text_alignment?.value,
                fontSize:
                  windowWidth > 768 ? titleSizeDesktop : titleSizeTablet,
              }}
            >
              {heading?.value}
            </h2>
          )}
          {description?.value?.length > 0 && (
            <p
              className={`fx-description ${styles.description} b2`}
              style={{ textAlign: text_alignment?.value }}
            >
              {description?.value}
            </p>
          )} */}
        </div>
        {getGallery?.length > 0 && (
          <div
            className={`${styles.bannerImageSliderWrap} ${
              desktop_layout?.value === "banner_horizontal_scroll"
                ? styles.desktopVisibleFlex
                : styles.desktopHiddenFlex
            } ${
              mobile_layout?.value === "banner_horizontal_scroll"
                ? styles.mobileVisible
                : styles.mobileHidden
            }`}
          >
            <FDKLink to={`/collection/${slug}`} className={styles.bannerImage}>
              <FyImage
                customClass={styles.bannerImage}
                globalConfig={globalConfig}
                src={bannerUrl}
                sources={getImgSrcSet()}
                aspectRatio="0.8"
                mobileAspectRatio="0.8"
                alt={imgAlt}
              />
            </FDKLink>
            <div className={styles.slideWrapBanner}>
              <div
                className={`${styles.titleBlock} ${styles.bannerTitleBlock}`}
              >
                {/* {heading?.value?.length > 0 && (
                  <h2
                    className={`fx-title ${styles.sectionHeading} fontHeader`}
                    style={{
                      textAlign: text_alignment?.value,
                      fontSize:
                        windowWidth > 768 ? titleSizeDesktop : titleSizeTablet,
                    }}
                  >
                    {heading?.value}
                  </h2>
                )}
                {description?.value?.length > 0 && (
                  <p
                    className={`fx-description ${styles.description} b2`}
                    style={{ textAlign: text_alignment?.value }}
                  >
                    {description?.value}
                  </p>
                )} */}
                {/* {button_text?.value &&
                  show_view_all?.value &&
                  button_position?.value !== "below_products" && (
                    <div
                      className={`${styles["gap-above-button"]} ${styles.visibleOnDesktop}`}
                    >
                      <FDKLink to={`/collection/${slug}`}>
                        <button
                          type="button"
                          className={`fx-button btn-secondary ${styles["section-button"]} ${styles.fontBody}`}
                        >
                          {button_text?.value}
                          <Vector />
                        </button>
                      </FDKLink>
                    </div>
                  )} */}
              </div>
              <div
                className={`${styles.productSlider} ${styles.bannerSlider} ${imagesForScrollView?.length <= 1 ? styles.lessItem : ""} ${styles.featuredCollectionSlider}`}
              >
                {button_text?.value &&
                  show_view_all?.value &&
                  button_position?.value !== "below_products" && (
                    <div
                      className={`${styles["gap-above-button"]} ${styles.visibleOnDesktop}`}
                    >
                      <FDKLink to={`/collection/${slug}`}>
                        <button
                          type="button"
                          className={`fx-button btn-secondary ${styles["section-button"]} ${styles.fontBody}`}
                        >
                          {button_text?.value}
                          <Vector />
                        </button>
                      </FDKLink>
                    </div>
                  )}
                <Slider {...bannerConfig} ref={bannerRef}>
                  {imagesForScrollView?.map((product, index) => (
                    <ProductCardItem
                      key={`${product.uid}_${index}`}
                      className={styles.sliderItem}
                      product={product}
                      imgSrcSet={imgSrcSet}
                      listingPrice={listingPrice}
                      props={props}
                      globalConfig={globalConfig}
                      showAddToCart={showAddToCart}
                      followedIdList={followedIdList}
                      isSlider={true}
                      handleWishlistToggle={handleWishlistToggle}
                      handleAddToCart={handleAddToCart}
                    />
                  ))}
                </Slider>
                {getGallery?.length > 1 && (
                  <>
                    <span
                      className={styles.customPrevBtn}
                      onClick={() => bannerRef.current.slickPrev()}
                    >
                      <ArrowRightIcon />
                    </span>
                    <span
                      className={styles.customNextBtn}
                      onClick={() => bannerRef.current.slickNext()}
                    >
                      <ArrowRightIcon />
                    </span>
                  </>
                )}
              </div>
              {button_text?.value && show_view_all?.value && (
                <div
                  className={`${styles["flex-justify-center"]} ${styles["gap-above-button"]} ${button_position?.value === "below_products" ? "" : styles.visibleOnMobile}`}
                >
                  <FDKLink to={`/collection/${slug}`}>
                    <button type="button" className={styles.sectionbutton}>
                      {button_text?.value}
                      <Vector />
                    </button>
                  </FDKLink>
                </div>
              )}
            </div>
          </div>
        )}
        {getGallery.length > 0 && (
          <div
            className={`${styles.productSlider} ${imagesForScrollView?.length <= itemCountMobile ? styles.lessItem : ""} ${
              desktop_layout?.value === "horizontal"
                ? styles.desktopVisible
                : styles.desktopHidden
            } ${
              mobile_layout?.value === "horizontal"
                ? styles.mobileVisible
                : styles.mobileHidden
            }`}
            style={{
              "--slick-dots": `${Math.ceil(imagesForScrollView?.length / item_count?.value) * 22 + 10}px`,
            }}
          >
            <Slider className={`${styles.hideOnMobile}`} {...config}>
              {imagesForScrollView?.map((product, index) => (
                <ProductCardItem
                  key={`${product.uid}_${index}`}
                  className={styles.sliderItem}
                  product={product}
                  imgSrcSet={imgSrcSet}
                  listingPrice={listingPrice}
                  props={props}
                  globalConfig={globalConfig}
                  showAddToCart={showAddToCart}
                  followedIdList={followedIdList}
                  isSlider={true}
                  handleWishlistToggle={handleWishlistToggle}
                  handleAddToCart={handleAddToCart}
                />
              ))}
            </Slider>
            <Slider className={`${styles.hideOnDesktop}`} {...configMobile}>
              {imagesForScrollView?.map((product, index) => (
                <ProductCardItem
                  key={`${product.uid}_${index}`}
                  className={styles.sliderItem}
                  product={product}
                  imgSrcSet={imgSrcSet}
                  listingPrice={listingPrice}
                  props={props}
                  globalConfig={globalConfig}
                  showAddToCart={showAddToCart}
                  followedIdList={followedIdList}
                  isSlider={true}
                  handleWishlistToggle={handleWishlistToggle}
                  handleAddToCart={handleAddToCart}
                />
              ))}
            </Slider>
          </div>
        )}
        {getGallery.length > 0 && (
          <div
            className={`${
              desktop_layout?.value === "grid"
                ? styles.desktopVisible
                : styles.desktopHidden
            } ${
              mobile_layout?.value === "grid" ||
              mobile_layout?.value === "banner_stacked"
                ? styles.mobileVisible
                : styles.mobileHidden
            }`}
          >
            {mobile_layout?.value === "banner_stacked" && (
              <FDKLink
                to={`/collection/${slug}`}
                className={`${styles.bannerImage} ${styles.hideOnDesktop}`}
                style={{ marginBottom: "24px" }}
              >
                <FyImage
                  globalConfig={globalConfig}
                  src={bannerUrl}
                  sources={getImgSrcSet()}
                  aspectRatio="0.8"
                  mobileAspectRatio="0.8"
                  alt={imgAlt}
                />
              </FDKLink>
            )}
            <div
              className={`${styles.imageGrid} ${
                imagesForStackedView().length === 1 && styles.singleItem
              }`}
              style={{
                "--per_row": item_count?.value,
                "--brand-item": getWidthByCount() || 1,
              }}
            >
              {imagesForStackedView().map((product, index) => (
                <ProductCardItem
                  className={styles.imageset}
                  key={`${product.uid}_${index}`}
                  product={product}
                  imgSrcSet={imgSrcSet}
                  listingPrice={listingPrice}
                  props={props}
                  globalConfig={globalConfig}
                  showAddToCart={showAddToCart}
                  followedIdList={followedIdList}
                  handleWishlistToggle={handleWishlistToggle}
                  handleAddToCart={handleAddToCart}
                />
              ))}
            </div>
          </div>
        )}
        {!getGallery.length && !isLoading && (
          <div
            className={`${styles.bannerImageSliderWrap} ${styles.slideWrap}  ${
              desktop_layout?.value === "banner_horizontal_scroll"
                ? styles.desktopVisibleFlex
                : styles.desktopHiddenFlex
            } ${
              mobile_layout?.value === "banner_horizontal_scroll"
                ? styles.mobileVisible
                : styles.mobileHidden
            } `}
          >
            <div className={styles.bannerImage}>
              <FyImage
                globalConfig={globalConfig}
                src={bannerUrl || placeholderBanner}
                sources={getImgSrcSet()}
                aspectRatio="0.8"
                mobileAspectRatio="0.8"
              />
            </div>
            <div className={styles.slideWrapBanner}>
              <div
                className={styles.titleBlock}
                style={{ paddingLeft: "10px" }}
              >
                {heading?.value?.length > 0 && (
                  <h2
                    className={`${styles.sectionHeading} fontHeader`}
                    style={{ textAlign: "left" }}
                  >
                    {heading?.value}
                  </h2>
                )}
                {description?.value?.length > 0 && (
                  <p
                    className={`${styles.description} b2`}
                    style={{ textAlign: "left" }}
                  >
                    {description?.value}
                  </p>
                )}
              </div>
              <div style={{ display: "flex" }}>
                {[1, 2, 3].map((category, index) => (
                  <div
                    key={index}
                    data-cardtype="'Categories'"
                    className={styles["pos-relative"]}
                    style={{ flex: "1" }}
                  >
                    <div style={{ padding: "0 12px" }}>
                      <FyImage
                        customClass={`${styles.imageGallery} ${
                          img_fill?.value ? styles.streach : ""
                        }`}
                        src={placeholderProduct}
                      />
                    </div>
                  </div>
                ))}
              </div>
              {button_text?.value && show_view_all?.value && (
                <div
                  className={`${styles["flex-justify-center"]} ${styles["gap-above-button"]}`}
                >
                  <FDKLink to={`/collection/${slug}`}>
                    <button
                      type="button"
                      className={`fx-button btn-secondary ${styles["section-button"]} ${styles.fontBody}`}
                    >
                      {button_text?.value}
                      {/* <Vector /> */}
                    </button>
                  </FDKLink>
                </div>
              )}
            </div>
          </div>
        )}
        {!getGallery.length && !isLoading && (
          <div
            className={`${styles.slideWrap}  ${
              desktop_layout?.value === "grid" ||
              desktop_layout?.value === "horizontal"
                ? styles.desktopVisible
                : styles.desktopHidden
            } ${
              mobile_layout?.value === "grid" ||
              mobile_layout?.value === "banner_stacked" ||
              mobile_layout?.value === "horizontal"
                ? styles.mobileVisible
                : styles.mobileHidden
            }`}
          >
            {mobile_layout?.value === "banner_stacked" && (
              <FyImage
                globalConfig={globalConfig}
                src={bannerUrl || placeholderBanner}
                sources={getImgSrcSet()}
                aspectRatio="0.8"
                mobileAspectRatio="0.8"
                alt={imgAlt}
                customClass={styles.hideOnDesktop}
              />
            )}
            <div
              className={`${showStackedView() ? styles.imageGrid : ""} ${showScrollView() ? styles.placeholderScroll : ""}`}
              style={{
                "--per_row": item_count?.value,
                "--per_row_mobile": itemCountMobile,
              }}
            >
              {Array.from(
                { length: item_count?.value },
                (_, index) => index
              ).map((index) => (
                <div key={index} style={{ padding: "0 12px" }}>
                  <FyImage
                    customClass={`${styles.imageGallery}`}
                    src={placeholderProduct}
                    isImageFill={!!img_fill?.value}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
        {button_text?.value &&
          show_view_all?.value &&
          !showBannerScrollView() &&
          getGallery?.length > 0 && (
            <div
              className={`${styles["flex-justify-center"]} ${
                imagesForScrollView?.length <= 3 ? styles.lessGap : ""
              } ${showScrollView() && isClient ? styles["gap-above-button-horizontal"] : styles["gap-above-button"]} ${button_position?.value === "below_products" ? "" : styles.visibleOnMobile}`}
            >
              <FDKLink to={`/collection/${slug}`}>
                <button
                  type="button"
                  className={`fx-button btn-secondary ${styles["section-button"]}`}
                >
                  {button_text?.value}
                </button>
                {/* <Vector/> */}
              </FDKLink>
            </div>
          )}
      </section>
      {showAddToCart && (
        <>
          {isAddToCartOpen && (
            <Suspense fallback={<div />}>
              <Modal
                isOpen={isAddToCartOpen}
                hideHeader={!isTablet}
                bodyClassName={styles.addToCartBody}
                title={
                  isTablet
                    ? restAddToModalProps?.productData?.product?.name
                    : ""
                }
                closeDialog={restAddToModalProps?.handleClose}
                containerClassName={styles.addToCartContainer}
              >
                <AddToCart
                  {...restAddToModalProps}
                  globalConfig={globalConfig}
                />
              </Modal>
            </Suspense>
          )}
          {showSizeGuide && (
            <Suspense fallback={<div />}>
              <SizeGuide
                isOpen={showSizeGuide}
                onCloseDialog={handleCloseSizeGuide}
                productMeta={restAddToModalProps?.productData?.product?.sizes}
              />
            </Suspense>
          )}
        </>
      )}
    </>
  );
}

const ProductCardItem = ({
  className = "",
  product,
  imgSrcSet,
  listingPrice,
  isSlider = false,
  props,
  globalConfig,
  showAddToCart = false,
  followedIdList,
  handleWishlistToggle,
  handleAddToCart,
}) => {
  const {
    show_badge,
    show_wishlist_icon,
    img_fill,
    item_count,
    item_count_mobile,
  } = props;

  const columnCount = {
    desktop: item_count?.value > 3 ? 4 : 2,
    tablet: item_count?.value > 2 ? 3 : 2,
    mobile: item_count_mobile?.value,
  };

  return (
    <div className={className}>
      <FDKLink to={`/product/${product.slug}`}>
        <ProductCard
          product={product}
          listingPrice={listingPrice}
          isSaleBadgeDisplayed={false}
          showProductTitle={false}
          showBadge={show_badge?.value}
          columnCount={columnCount}
          isWishlistDisplayed={false}
          onWishlistClick={handleWishlistToggle}
          followedIdList={followedIdList}
          isWishlistIcon={false}
          isImageFill={img_fill?.value}
          isPrice={false}
          aspectRatio={getProductImgAspectRatio(globalConfig)}
          imagePlaceholder={placeholderProduct}
          showAddToCart={showAddToCart}
          handleAddToCart={handleAddToCart}
          imgSrcSet={imgSrcSet}
          isSlider={isSlider}
        />
      </FDKLink>
    </div>
  );
};

export const settings = {
  label: "Featured Collection",
  props: [
    {
      type: "collection",
      id: "collection",
      label: "Collection",
      info: "Select a collection to display its products",
    },
    {
      id: "desktop_layout",
      type: "select",
      options: [
        {
          value: "horizontal",
          text: "Horizontal scroll",
        },
        {
          value: "grid",
          text: "Stack",
        },
        {
          value: "banner_horizontal_scroll",
          text: "Banner with horizontal carousel",
        },
      ],
      default: "banner_horizontal_scroll",
      label: "Layout (Desktop)",
      info: "Alignment of content in desktop",
    },
    {
      id: "mobile_layout",
      type: "select",
      options: [
        {
          value: "horizontal",
          text: "Horizontal scroll",
        },
        {
          value: "grid",
          text: "Stack",
        },
        {
          value: "banner_horizontal_scroll",
          text: "Banner with horizontal scroll",
        },
        {
          value: "banner_stacked",
          text: "Banner with Stack",
        },
      ],
      default: "horizontal",
      label: "Layout (Mobile)",
      info: "Alignment of content in mobile",
    },
    {
      id: "img_resize",
      label: "Image size for Tablet/Desktop",
      type: "select",
      options: [
        {
          value: "300",
          text: "300px",
        },
        {
          value: "500",
          text: "500px",
        },
        {
          value: "700",
          text: "700px",
        },
        {
          value: "900",
          text: "900px",
        },
        {
          value: "1100",
          text: "1100px",
        },
        {
          value: "1300",
          text: "1300px",
        },
      ],
      default: "300",
    },
    {
      id: "img_resize_mobile",
      label: "Image size for Mobile",
      type: "select",
      options: [
        {
          value: "300",
          text: "300px",
        },
        {
          value: "500",
          text: "500px",
        },
        {
          value: "700",
          text: "700px",
        },
        {
          value: "900",
          text: "900px",
        },
      ],
      default: "500",
    },
    {
      type: "color",
      id: "img_container_bg",
      category: "Image Container",
      default: "#00000000",
      label: "Container Background Color",
      info: "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable",
    },
    {
      type: "color",
      id: "text_color",
      category: "Text",
      default: "#00000000",
      label: "Text Color",
      info: "This color will be used as the text color of the section",
    }
    ,
    {
      type: "checkbox",
      id: "img_fill",
      category: "Image Container",
      default: true,
      label: "Fit image to the container",
      info: "If the image aspect ratio is different from the container, the image will be clipped to fit the container. The aspect ratio of the image will be maintained",
    },
    {
      type: "text",
      id: "heading",
      default: "New Arrivals",
      label: "Heading",
      info: "Heading text of the section",
    },
    {
      type: "text",
      id: "description",
      default:
        "Showcase your top collections here! Whether it's new arrivals, trending items, or special promotions, use this space to draw attention to what's most important in your store.",
      label: "Description",
      info: "Description text of the section",
    },
    {
      id: "text_alignment",
      type: "select",
      options: [
        {
          value: "left",
          text: "Left",
        },
        {
          value: "right",
          text: "Right",
        },
        {
          value: "center",
          text: "Center",
        },
      ],
      default: "center",
      label: "Text Alignment",
      info: "Alignment of text content",
    },
    // {
    //   id: "title_size",
    //   type: "select",
    //   options: [
    //     {
    //       value: "small",
    //       text: "Small",
    //     },
    //     {
    //       value: "medium",
    //       text: "Medium",
    //     },
    //     {
    //       value: "large",
    //       text: "Large",
    //     },
    //   ],
    //   default: "medium",
    //   label: "Title size",
    //   info: "Select title size",
    // },
    {
      type: "text",
      id: "button_text",
      default: "View all",
      label: "Button text",
    },
    {
      id: "button_position",
      type: "select",
      options: [
        {
          value: "below_description",
          text: "Below description",
        },
        {
          value: "below_products",
          text: "Below products",
        },
      ],
      default: "below_description",
      label: "Button Position",
      info: "Applicable for only desktop view",
    },
    {
      type: "range",
      id: "item_count",
      min: 3,
      max: 6,
      step: 1,
      unit: "",
      label: "Products per row (Desktop)",
      default: 4,
      info: "Maximum items allowed per row in horizontal scroll",
    },
    {
      type: "range",
      id: "item_count_mobile",
      min: 1,
      max: 2,
      step: 1,
      unit: "",
      label: "Products per row (Mobile)",
      default: 1,
      info: "Maximum items allowed per row in horizontal scroll",
    },
    {
      type: "range",
      id: "max_count",
      min: 1,
      max: 25,
      step: 1,
      unit: "",
      label: "Maximum products to show",
      default: 10,
      info: "Maximu products to show in horizontal scroll",
    },
    {
      type: "checkbox",
      id: "show_add_to_cart",
      label: "Show Add to Cart",
      info: "Not Applicable for International Websites",
      default: true,
    },
    {
      type: "checkbox",
      id: "show_wishlist_icon",
      label: "Show Wish List Icon",
      default: true,
    },
    {
      type: "checkbox",
      id: "show_badge",
      label: "Show Badge",
      default: true,
    },
    {
      type: "checkbox",
      id: "show_view_all",
      label: "Show View All Button",
      default: true,
    },
    {
      type: "checkbox",
      id: "mandatory_pincode",
      label: "Mandatory Delivery check",
      info: "Mandatory delivery check in Add to Cart popup. Not applicable for international websites",
      default: false,
    },
    {
      type: "checkbox",
      id: "hide_single_size",
      label: "Hide single size",
      info: "Hide single size in Add to Cart popup. Not applicable for international websites",
      default: false,
    },
    {
      type: "checkbox",
      id: "preselect_size",
      label: "Preselect size",
      info: "Preselect size in Add to Cart popup. Applicable only for multi-sized products. Not applicable for international websites",
      default: false,
    },
    {
      type: "range",
      id: "padding_top",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Top padding",
      default: 16,
      info: "Top padding for section",
    },
    {
      type: "range",
      id: "padding_bottom",
      min: 0,
      max: 100,
      step: 1,
      unit: "px",
      label: "Bottom padding",
      default: 16,
      info: "Bottom padding for section",
    },
  ],
};

Component.serverFetch = async ({ fpi, props, id }) => {
  try {
    const payload = {
      slug: props.collection.value,
      first: 12,
      pageNo: 1,
    };
    await fpi.executeGQL(FEATURED_COLLECTION, payload).then((res) => {
      return fpi.custom.setValue(
        `featuredCollectionData-${props.collection.value}`,
        res
      );
    });
  } catch (err) {
    console.log(err);
  }
};
export default Component;
