import React, { useEffect, useMemo, useState, useRef } from "react";
import { useFPI, useGlobalStore } from "fdk-core/utils";
import Slider from "react-slick";
import FyImage from "fdk-react-templates/components/core/fy-image/fy-image";
import ProductCard from "fdk-react-templates/components/product-card/product-card";
import styles from "../styles/sections/complete-look.less";
import ArrowRightIcon from "../assets/images/glide-arrow-right.svg";
import ArrowLeftIcon from "../assets/images/glide-arrow-left.svg";
import { FEATURED_COLLECTION } from "../queries/collectionsQuery";
import "fdk-react-templates/components/product-card/product-card.css";
import "fdk-react-templates/components/core/fy-image/fy-image.css";

export function Component({ props = {}, blocks = [], globalConfig = {} }) {
  const fpi = useFPI();
  const customValues = useGlobalStore(fpi?.getters?.CUSTOM_VALUE) ?? {};

  const {
    heading,
    main_image,
    main_image_alt,
    item_per_row,
    img_resize,
    img_resize_mobile,
  } = props;

  const [activeTag, setActiveTag] = useState(0);
  const [activeProducts, setActiveProducts] = useState([]);

  // Prepare tag configuration from blocks
  const tags = useMemo(
    () =>
      (blocks ?? []).map((blk) => ({
        label: blk?.props?.tag_label?.value ?? "",
        collection: blk?.props?.collection?.value ?? "",
        top: blk?.props?.pos_top?.value ?? 0,
        left: blk?.props?.pos_left?.value ?? 0,
      })),
    [blocks]
  );

  const carouselRef = useRef(null);

  const imgSrcSet = useMemo(() => {
    if (globalConfig?.img_hd) return [];
    return [
      { breakpoint: { min: 481 }, width: img_resize?.value ?? 300 },
      { breakpoint: { max: 480 }, width: img_resize_mobile?.value ?? 500 },
    ];
  }, [globalConfig?.img_hd, img_resize?.value, img_resize_mobile?.value]);

  // Slider config
  const sliderConfig = useMemo(() => {
    const perRow = Number(item_per_row?.value ?? 4);
    return {
      arrows: activeProducts.length > perRow,
      dots: activeProducts.length > perRow,
      infinite: activeProducts.length > perRow,
      speed: 400,
      slidesToShow: perRow,
      slidesToScroll: perRow,
      swipeToSlide: true,
      touchMove: true,
      nextArrow: <ArrowRightIcon />,
      prevArrow: <ArrowLeftIcon />,
      responsive: [
        {
          breakpoint: 780,
          settings: {
            arrows: false,
            slidesToShow: 2,
            slidesToScroll: 2,
            dots: true,
          },
        },
      ],
    };
  }, [activeProducts.length, item_per_row?.value]);

  const fetchCollectionProducts = (slug) => {
    if (!slug) return;

    // Check if products cached
    const cached = customValues[`complete-look-${slug}`];
    if (cached) {
      setActiveProducts(cached);
      return;
    }

    const payload = { slug, first: 12, pageNo: 1 };
    fpi.executeGQL(FEATURED_COLLECTION, payload).then((res) => {
      const items = res?.data?.collection?.products?.items ?? [];
      fpi.custom.setValue(`complete-look-${slug}`, items);
      setActiveProducts(items);
    });
  };

  // Initial fetch
  useEffect(() => {
    if (tags?.[activeTag]?.collection) {
      fetchCollectionProducts(tags[activeTag].collection);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTag, tags]);

  return (
    <section className={styles.wrapper}>
      {heading?.value && (
        <h2 className={`fx-title fontHeader ${styles.heading}`}>
          complete look
        </h2>
      )}
      <div className={styles.container}>
        {/* Left side main image with interactive tags */}
        <div className={styles.leftPanel}>
          <div className={styles.imageWrapper}>
            <FyImage
              src={main_image?.value}
              alt={main_image_alt?.value || "complete-look"}
              aspectRatio="0.8"
              isFixedAspectRatio
              isImageFill
              sources={imgSrcSet}
              backgroundColor="transparent"
              defer={false}
            />
            {tags.map((tag, index) => (
              <button
                key={`${tag.label}-${index}`}
                className={`${styles.tagButton} ${index === activeTag ? styles.activeTag : ""}`}
                style={{ top: `${tag.top}%`, left: `${tag.left}%` }}
                onClick={() => setActiveTag(index)}
                title={tag.label}
              >
                {tag.label}
              </button>
            ))}
          </div>
        </div>

        {/* Right side product carousel */}
        <div className={styles.rightPanel}>
          {activeProducts.length > 0 && (
            <Slider ref={carouselRef} {...sliderConfig}>
              {activeProducts.map((product, idx) => (
                <div key={`${product.uid}-${idx}`} className={styles.sliderItem}>
                  <ProductCard
                    product={product}
                    isPrice={globalConfig?.show_price}
                    isImageFill={true}
                    imgSrcSet={imgSrcSet}
                    listingPrice="range"
                  />
                </div>
              ))}
            </Slider>
          )}
        </div>
      </div>
    </section>
  );
}

export const settings = {
  label: "Complete Look",
  props: [
    {
      type: "text",
      id: "heading",
      default: "Complete your look",
      label: "Section Heading",
    },
    {
      type: "image_picker",
      id: "main_image",
      label: "Main Image (Model)",
      default: "",
      options: { aspect_ratio: "3:4", aspect_ratio_strict_check: false },
    },
    {
      type: "text",
      id: "main_image_alt",
      label: "Main Image Alt Text",
      default: "Model image",
    },
    {
      type: "range",
      id: "item_per_row",
      min: 2,
      max: 5,
      step: 1,
      unit: "",
      label: "Products per Row",
      default: 4,
    },
    {
      id: "img_resize",
      label: "Image size for Tablet/Desktop",
      type: "select",
      options: [
        { value: "300", text: "300px" },
        { value: "500", text: "500px" },
        { value: "700", text: "700px" },
        { value: "900", text: "900px" },
        { value: "1100", text: "1100px" },
      ],
      default: "300",
    },
    {
      id: "img_resize_mobile",
      label: "Image size for Mobile",
      type: "select",
      options: [
        { value: "300", text: "300px" },
        { value: "500", text: "500px" },
        { value: "700", text: "700px" },
        { value: "900", text: "900px" },
      ],
      default: "500",
    },
  ],
  blocks: [
    {
      type: "tag-item",
      name: "Look Tag",
      props: [
        {
          type: "text",
          id: "tag_label",
          label: "Tag Label (e.g., Tops)",
          default: "Tops",
        },
        {
          type: "collection",
          id: "collection",
          label: "Select Collection for Products",
        },
        {
          type: "range",
          id: "pos_top",
          label: "Tag Position - Top (%)",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          default: 50,
        },
        {
          type: "range",
          id: "pos_left",
          label: "Tag Position - Left (%)",
          min: 0,
          max: 100,
          step: 1,
          unit: "%",
          default: 50,
        },
      ],
    },
  ],
  preset: {
    blocks: [
      { name: "Look Tag" },
    ],
  },
};

Component.serverFetch = async ({ fpi, blocks }) => {
  // Prefetch products for first tag (if provided)
  const firstSlug = blocks?.[0]?.props?.collection?.value;
  if (firstSlug) {
    const payload = { slug: firstSlug, first: 12, pageNo: 1 };
    const res = await fpi.executeGQL(FEATURED_COLLECTION, payload);
    const items = res?.data?.collection?.products?.items ?? [];
    return fpi.custom.setValue(`complete-look-${firstSlug}`, items);
  }
};

export default Component; 