@import '../../styles//main.less';

.setWrapper {
	width: 400px;
	padding: 24px;

	.setContentTitle {
		color: @TextHeading;
		text-align: center;
		font-size: 32px;
		font-style: normal;
		font-weight: 700;
		line-height: 44.8px;
		margin-bottom: 32px;

		@media @tablet {
			font-size: 24px;
			line-height: 33.6px;
		}
	}
	.setInputGroup {
		margin-bottom: 20px;
		display: flex;
		flex-direction: column;
		input {
			height: 48px;
			border: 1px solid @DividerStokes;
			padding: 16px;
			border-radius: 4px;
			background-color: #fff;
			box-sizing: border-box;
			color: @TextBody;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 18px;
			&:focus-visible {
				outline: none;
			}
		}
	}
	.setInputTitle {
		position: absolute;
		background-color: white;
		padding: 0 4px;
		margin: 0 12px;
		z-index: 1;
		transform: translateY(-50%);
		color: @TextLabel;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 16.8px;
	}
	.setSubmitBtn {
		border: none;
		width: 100%;
		height: 48px;
		border-radius: 4px;
		background: @ButtonPrimary;
		color: @ButtonSecondary;
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 22.4px;
		text-transform: uppercase;
		cursor: pointer;

		&[disabled] {
			background: #dde7e8 !important;
			color: #b8b29d !important;
			cursor: default;
		}
	}
	.errorText {
		display: none;
	}
	.errorInput {
		input {
			border: 1px solid @ErrorText;
		}
		.errorText {
			display: block;
			color: @ErrorText;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 16.8px;
			margin-top: 8px;
		}
		label {
			color: @ErrorText;
		}
	}
	.loginAlert {
		height: 48px;
		padding: 0 7px;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		font-size: 13px;
		background-color: @ErrorBackground;
		border: 1px dashed @ErrorText;
		color: @ErrorText;
	}
}
