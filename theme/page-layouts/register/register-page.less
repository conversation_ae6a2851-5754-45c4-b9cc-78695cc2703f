@import "../../styles/main.less";

.containerWrapper {
  width: 400px;
  padding: 24px;
}

.registerFormWrapper {
  min-height: 417px;

  .title {
    color: @TextHeading;
    text-align: center;
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: 44.8px;
    margin-bottom: 32px;

    @media @tablet {
      font-size: 24px;
      line-height: 33.6px;
    }
  }

  @media @tablet {
    min-height: 367px;
  }
  input[type="text"],
  input[type="password"] {
    height: 48px;
    border: 1px solid @DividerStokes;
    padding: 16px;
    border-radius: 4px;
    background-color: @PageBackground;
    box-sizing: border-box;
    color: @TextBody;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 19.6px;
    width: 100%;
    &:focus-visible {
      outline: none;
    }
  }
  .inputTitle {
    position: absolute;
    background-color: @PageBackground;
    padding: 0 4px;
    margin: 0 12px;
    z-index: 1;
    transform: translateY(-50%);
    color: @TextLabel;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16.8px;

    input {
      background-color: @PageBackground;
    }
  }
  .genderRadioContainer {
    padding: 8px 0;

    .radioContainer {
      margin-bottom: 8px;
      padding-left: 24px;
      margin-right: 12px;
      position: relative;
      color: @TextBody;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 19.6px;

      input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
      }
      .checkmark {
        height: 16px;
        width: 16px;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        border: 1px solid #919191;
        border-radius: 50%;
      }
      .checkmark:after {
        content: "";
        position: absolute;
        display: none;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 9px;
        height: 9px;
        border-radius: 50%;
        background: #919191;
      }
      input:checked ~ .checkmark:after {
        display: block;
      }
    }
  }

  .resgisterPassworInput,
  .registerConfirmPasswordInput,
  .resgisterEmail,
  .registerNameInput {
    display: flex;
    flex-direction: column;
  }
  .registerNameInput,
  .genderRadioContainer,
  .registerPasswordInput,
  .registerConfirmPasswordInput,
  .registerMobileInput,
  .registerEmail {
    margin-bottom: 20px;
  }
  .registerMobileInput {
    min-height: 57px;
    @media @tablet {
      min-height: 49px;
    }
  }
  .registerBtn {
    display: block;
    border: none;
    width: 100%;
    margin-top: 32px;
    height: 48px;
    border-radius: 4px;
    background: @ButtonPrimary;
    color: @ButtonSecondary;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 22.4px;
    text-transform: uppercase;
  }
  .errorText {
    display: none;
  }
  .errorInput {
    input {
      border: 1px solid @ErrorText;
    }
    .errorText {
      display: inline-block;
      color: @ErrorText;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16.8px;
      margin-top: 8px;
    }
    label {
      color: @ErrorText;
    }
    .loginInputWrapper {
      color: @ErrorText;
    }
  }
  .loginAlert {
    height: 48px;
    padding: 0 7px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    font-size: 13px;
    background-color: @ErrorBackground;
    border: 1px dashed @ErrorText;
    color: @ErrorText;
  }
}
.passwordInputWrapper {
  position: relative;
  input {
    width: 100%;
    padding-right: 40px;
    background-color: @PageBackground;
  }
}

.passwordToggle {
  height: 24px;
  width: 24px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 12px;
  cursor: pointer;
  background: none;
  border: none;
  &:focus-visible {
    outline: none;
  }
}
