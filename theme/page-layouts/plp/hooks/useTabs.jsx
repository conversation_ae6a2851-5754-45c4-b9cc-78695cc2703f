import { useMemo, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useFPI } from 'fdk-core/utils';
import useHeader from '../../../components/header/useHeader';
/**
 * Custom hook for managing PLP tabs and navigation
 * @returns {Object} Object containing tabs, activeTab, mainCategory, subsection, and navigation handlers
 */
const useTabs = () => {
    const fpi = useFPI();
    const navigate = useNavigate();
    const location = useLocation();
    const { plpNavigation } = useHeader(fpi);
    console.log("plpNavigation", plpNavigation);


    // Extract main category from path and subsection from query params
    const { mainCategory, subsection } = useMemo(() => {
        const pathSegments = location.pathname.split("/").filter(Boolean);
        const mainCategory = pathSegments[pathSegments.length - 1]?.toLowerCase() || "";
        const subsection = new URLSearchParams(location.search).get("subsection")?.toLowerCase() || "";
        return { mainCategory, subsection };
    }, [location.pathname, location.search]);

    // Derive tabs from plpNavigation based on main category
    const { tabs, activeTab } = useMemo(() => {
        if (!mainCategory || !plpNavigation?.length) {
            return { tabs: [], activeTab: "" };
        }

        // Helper to map sub_navigation array into tab objects
        const mapTabs = (arr) =>
            arr.map((item) => ({
                display: item.display,
                path: item.display.toLowerCase().replace(/\s+/g, "-"),
                action: item.action,
            }));

        // Find the main category and its sub-navigation
        for (const l1 of plpNavigation) {
            if (l1.display.toLowerCase() === mainCategory) {
                return {
                    activeTab: subsection || mainCategory,
                    tabs: l1.sub_navigation ? mapTabs(l1.sub_navigation) : [],
                };
            }
        }

        return { tabs: [], activeTab: "" };
    }, [plpNavigation, mainCategory, subsection]);

    // Stable click handler for tab navigation
    const handleTabClick = useCallback(
        (tab) => {
            // Update only the query parameter without changing the main path
            navigate(`/collection/${mainCategory}?subsection=${tab.path}`, { replace: true });
        },
        [navigate, mainCategory],
    );

    // Check if tabs are available
    const hasTabs = tabs.length > 0;

    return {
        // Data
        tabs,
        activeTab,
        mainCategory,
        subsection,
        hasTabs,

        // Actions
        handleTabClick,

        // Utilities
        isActiveTab: (tabPath) => subsection === tabPath,
    };
};

export default useTabs; 