import React from "react"
import { useMemo, useCallback, useRef, useState, useEffect } from "react"
import styles from "./FilterSortPanel.less"
import filterIcon from "../../../../assets/images/filter.png"
import Sort from "../sort/sort"
import useTabs from "../../hooks/useTabs"
import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper"
import { FDKLink } from "fdk-core/components"
import FilterModal from "../../../../components/filter-modal/filter-modal"

const FilterSortPanel = ({
  selectedFilters = [],
  onFilterModalBtnClick,
  onSortUpdate,
  sortList,
  filterModalProps,
  isResetFilterDisable,
  onFilterUpdate,
  onResetBtnClick,
  productCount,
  size_selection_style = "",
  filters = [],
  isFilterModalOpen = false,
  onCloseFilterModal = () => { },
  onApplyFilterModal = () => { },
  fpi,
  plpWrapperRef,
}) => {
  const tabsContainerRef = useRef(null)
  const [showLeftArrow, setShowLeftArrow] = useState(false)
  const [showRightArrow, setShowRightArrow] = useState(false)

  // check filtermodal is outside the plpwrapper

  const filterModalRef = useRef(null);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      if (!filterModalRef.current || !plpWrapperRef.current) return;

      const modalRect = filterModalRef.current.getBoundingClientRect();
      const wrapperRect = plpWrapperRef.current.getBoundingClientRect();

      console.log("modalRect", modalRect);
      console.log("wrapperRect", wrapperRect);

      // Check if modal is outside the plpWrapper
      const isOutside =
        modalRect.bottom < wrapperRect.top || modalRect.top > wrapperRect.bottom;

      setIsVisible(!isOutside);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [plpWrapperRef]);


  // Use the custom hook for tab management
  const { tabs, hasTabs, isActiveTab } = useTabs();

  // Check if scrolling is needed
  useEffect(() => {
    const checkScroll = () => {
      if (tabsContainerRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } = tabsContainerRef.current
        const maxScrollLeft = scrollWidth - clientWidth

        setShowLeftArrow(scrollLeft > 0)
        setShowRightArrow(scrollLeft < maxScrollLeft - 1)
      }
    }

    // Initial check
    checkScroll()

    // Add event listener for scroll
    const tabsContainer = tabsContainerRef.current
    if (tabsContainer) {
      tabsContainer.addEventListener("scroll", checkScroll)
      window.addEventListener("resize", checkScroll)
    }

    // Clean up
    return () => {
      if (tabsContainer) {
        tabsContainer.removeEventListener("scroll", checkScroll)
      }
      window.removeEventListener("resize", checkScroll)
    }
  }, [tabs])

  // Scroll handlers
  const scrollToStart = () => {
    if (tabsContainerRef.current) {
      tabsContainerRef.current.scrollTo({
        left: -10,
        behavior: "smooth",
      })
    }
  }

  const scrollToEnd = () => {
    if (tabsContainerRef.current) {
      tabsContainerRef.current.scrollTo({
        left: tabsContainerRef.current.scrollWidth,
        behavior: "smooth",
      })
    }
  }

  const searchParams = new URLSearchParams(location.search);
  const queryString = searchParams.toString();

  // const handleTabClick = (url) => {
  //   // search params if it has already subsection then change only the value of subsection
  //   if (queryString && queryString.includes("subsection")) {
  //     const searchParams = new URLSearchParams(queryString);
  //     searchParams.set("subsection", url);
  //     return url + "?" + searchParams.toString();
  //   } else {
  //     return url + "?" + queryString;
  //   }


  //   if (queryString) {
  //     return url + "?" + queryString;
  //   }
  //   return url;
  // }

  // I write detail flow how it works
  // 1. when user click on the tab then it will navigate to the url with the subsection value
  // 2. if the url has already subsection then it will change the value of subsection
  // 3. if the url does not have subsection then it will add the subsection value to the url
  // 4. after that all other rest of state of filters and sort will be applied to the url


  const handleTabClick = (tabPath) => {
    if (!tabPath) return "";

    // Create a URL object to parse pathname and subsection from tabPath
    const url = new URL(tabPath, window.location.origin);
    const subsectionValue = url.searchParams.get("subsection");
    const basePath = url.pathname;

    const currentParams = new URLSearchParams(window.location.search);
    const paramsArray = [];
    let subsectionSet = false;

    // Rebuild the current params, replacing subsection if needed
    currentParams.forEach((value, key) => {
      if (key === "subsection") {
        paramsArray.push(["subsection", subsectionValue]); // Replace
        subsectionSet = true;
      } else {
        paramsArray.push([key, value]);
      }
    });

    if (!subsectionSet && subsectionValue) {
      paramsArray.unshift(["subsection", subsectionValue]); // Add as first
    }

    const newParams = new URLSearchParams();
    paramsArray.forEach(([key, value]) => {
      newParams.set(key, value);
    });

    return `${basePath}?${newParams.toString()}`;
  };



  return (
    <div className={styles.stickyContainer}>
      <div className={styles.panelContainer}>
        <div className={styles.filterTitleContainer}>
          <button className={styles.filterHeader} onClick={onFilterModalBtnClick}>
            <img src={filterIcon || "/placeholder.svg"} alt="Filter" />
            <span className={styles.filterText}>Filters</span>
            {selectedFilters.length > 0 && (
              <span className={styles.filterCountContainer}>
                <span className={styles.filterCount}>{selectedFilters.length}</span>
              </span>
            )}
          </button>
        </div>

        {hasTabs && (
          <div className={styles.tabsWrapper}>
            {showLeftArrow && (
              <button
                className={`${styles.scrollArrow} ${styles.scrollArrowLeft}`}
                onClick={scrollToStart}
                aria-label="Scroll to start"
              >
                <SvgWrapper svgSrc={"arrow-left-plp"} fill={"#000"} />
              </button>
            )}

            <div className={styles.categoryRow} ref={tabsContainerRef}>
              {tabs.map((tab) => (
                <button
                  key={tab.path}
                  className={`${styles.categoryItem} ${isActiveTab(tab.path) ? styles.active : ''}`}
                // onClick={() => handleTabClick(tab.action?.page?.url)}
                >
                  <FDKLink to={handleTabClick(tab.action?.page?.url)}>{tab.display}</FDKLink>
                  <div className={`${styles.line} ${isActiveTab(tab.path) ? styles.active : ''}`} />
                </button>
              ))}
            </div>

            {showRightArrow && (
              <button
                className={`${styles.scrollArrow} ${styles.scrollArrowRight}`}
                onClick={scrollToEnd}
                aria-label="Scroll to end"
              >
                <SvgWrapper svgSrc={"arrow-right"} fill={"#000"} />
              </button>
            )}
          </div>
        )}

        <Sort sortList={sortList} onSortUpdate={onSortUpdate} />
      </div>

      {isFilterModalOpen && (
        <div
          className={styles.filterModalOverlay}
          style={{ display: 'block' }}
        />
      )}

      {isFilterModalOpen && (
        <div
          className={styles.filterModal}
          ref={filterModalRef}
          style={{ display: isVisible ? 'block' : 'none' }}
        >
          <FilterModal
            isOpen={isFilterModalOpen}
            filters={filters}
            selectedFilters={selectedFilters}
            isResetFilterDisable={isResetFilterDisable}
            onCloseModalClick={onCloseFilterModal}
            onResetBtnClick={onResetBtnClick}
            onApplyBtnClick={onApplyFilterModal}
            onFilterUpdate={onFilterUpdate}
            productCount={productCount}
            onSortUpdate={onSortUpdate}
            sortList={sortList}
            size_selection_style={size_selection_style}
          />
        </div>
      )}
    </div>
  )
}

export default FilterSortPanel