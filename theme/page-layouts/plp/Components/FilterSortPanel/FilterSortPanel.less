@import "../../../../styles/main.less";

// Main sticky container that wraps both panel and modal
.stickyContainer {
  position: sticky;
  top: 130px;
  z-index: 30;
  background-color: @backgroundPlp;
  display: flex;
  justify-content: center;
  align-items: center;
}

.panelContainer {
  padding: clamp(12px, 1.25vw, 24px) clamp(16px, 1.875vw, 36px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  background-color: @backgroundPlp;
  height: clamp(48px, 4.17vw, 80px);
  width: 100%;
}

// .filterModalContainer {
//   position: absolute;
//   width: 500px;
//   height: 500px;
//   background-color: #fff;
//   border-radius: 10px;
//   box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
//   padding: 20px;
//   z-index: 100;
//   top: 0;
//   left: 0;
// }
.filterModal {
  position: absolute;
  // width: 500px;
  // height: 500px;
  background-color: #fff;
  z-index: 1000;
  top: 0;
  left: 0;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  width: clamp(300px, 25.21vw, 484px);

  @media (max-width: 768px) {
    width: 100%;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    position: fixed;
    bottom: 0;
    top: auto;
    // height: 100%;
  }
}
.filterHeader {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  transition: background 0.2s;
  position: relative;
  gap: 0.5rem;
}

.filterHeader:hover {
  background: #f5f5f5;
}

.filterTitleContainer {
  display: flex;
  align-items: center;
  gap: 6px;
}

.filterText {
  font-size: clamp(15px, 0.885vw, 17px);
  font-weight: 700;
  font-family: "Helvetica Bold";
  line-height: 130%;
  color: @Dark;
  text-align: center;
}

.filterCountContainer {
  display: flex;
  width: 16px;
  height: 16px;
  padding: 3px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #ff1e00;
  position: absolute;
  left: 76px;
  top: 0;
  @media (min-width: 1600px) {
    left: 81px;
    top: 2px;
  }
  @media (min-width: 1585px) and (max-width: 1760px) {
    left: 80px;
    top: 0;
  }
}

.filterCount {
  color: #fff;
  text-align: center;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Medium";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%;
  letter-spacing: 0.2px;
}

.sortButtonInner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.sortIcon {
  margin-right: 4px;
}

.sortButton:hover {
  background: #f5f5f5;
}

.sortText {
  color: #1a1a1a;
  font-family: "Helvetica Medium";
}

@media screen and (max-width: 768px) {
  .panelContainer {
    display: none;
  }
}

.tabsWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: clamp(320px, 48.38vw, 929px);
  margin: 0 auto;
}

.categoryRow {
  display: flex;
  gap: clamp(12px, 1.04vw, 20px);
  overflow-x: auto;
  justify-content: flex-start;
  align-items: center;
  padding-left: 8px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  max-width: 100%;
  white-space: nowrap;
}

.categoryRow::-webkit-scrollbar {
  display: none;
}

.categoryItem {
  color: @Dark;
  text-align: center;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Medium" !important;
  font-size: clamp(14px, 0.885vw, 17px);
  font-style: normal;
  font-weight: 500;
  padding: clamp(8px, 0.625vw, 12px) clamp(6px, 0.52vw, 10px)
    clamp(16px, 1.51vw, 29px) clamp(6px, 0.52vw, 10px);
  line-height: normal;
  display: flex;
  flex-direction: column;
  border: 2px solid transparent;

  .categoryItemText {
    font-size: clamp(14px, 0.9375vw, 18px);
    font-weight: 500;
    font-family: "Helvetica Medium";
    line-height: normal;
  }
}

.categoryItem:hover {
  color: #333;
}

.categoryItem.active {
  color: #1a1a1a;
  font-weight: 600;
  border-bottom: 2px solid @Brand;
  border-radius: 2px;
}

.scrollArrow {
  cursor: pointer;
  position: absolute;
  z-index: 2;
  transition: all 0.2s ease;
  display: flex;
  width: clamp(28px, 2.08vw, 40px);
  height: clamp(28px, 2.08vw, 40px);
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 1623.375px;
  background: #fff;

  svg {
    // width: clamp(14px, 1.04vw, 20px);
    // height: clamp(14px, 1.04vw, 20px);
  }
}

.scrollArrow:hover {
  background-color: #f5f5f5;
}

.scrollArrowLeft {
  left: -16px;
}

.scrollArrowRight {
  right: -16px;
}

@media screen and (max-width: 768px) {
  .tabsWrapper {
    max-width: 100%;
  }

  .scrollArrow {
    width: 28px;
    height: 28px;
  }

  .scrollArrowLeft {
    left: -8px;
  }

  .scrollArrowRight {
    right: -8px;
  }
}

@media (max-width: 768px) {
  .filterModalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    z-index: 999;
    display: block;
  }
}

@media (min-width: 769px) {
  .filterModalOverlay {
    display: none;
  }
}
