@import "../../../../styles/main.less";
/* Update for product-listing.less */

/* Existing styles preserved */
.mobileContainer {
  display: flex;
  padding: 20px;

  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-grow: 1;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 10;

  &.active {
    /* Preserve existing active class */
  }
  .headerContainer {
    display: flex;
    height: 44px;
    padding: 12px 20px;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    width: 100%;

    border-radius: 500px;
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(7.5px);
  }
  .homeIcon {
    width: 16px;
    height: 16px;
    cursor: pointer;
    display: flex; /* Changed from 'none' to make it visible as shown in image */
    margin-right: 8px;
  }

  @media @desktop {
    display: none;
  }

  .headerLeft {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    color: #000;
    text-align: center;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Medium";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 150%; /* 24px */
    letter-spacing: 0.16px;

    // button {
    //   .b1();
    //   display: flex;
    //   align-items: center;
    //   column-gap: 4px;
    //   color: @TextHeading;

    //   &.filterBtn {
    //     padding-right: 16px;
    //     border-right: 1px solid @DividerStokes;
    //     margin-right: 16px;
    //   }
    // }
  }

  .line {
    height: 14px;
    width: 0px;
    stroke-width: 0.75px;
    stroke: @Dark-10;
    opacity: 0.2;
  }
  .headerRight {
    display: flex;
    align-items: center;
    color: white;
  }

  /* New styles for filter & sort combined button */
  .filterSortBtn {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 4px;

    .filterSortText {
      font-size: 14px;
      font-weight: 500;
      color: @ThemeAccentL5;
      font-family: "Helvetica Medium";
      white-space: nowrap;
    }

    span {
      margin-left: 8px;
      font-weight: 500;
    }
  }

  /* Notification badge */
  .notificationBadge {
    position: absolute;
    top: -6px;
    right: -13px;
    width: 16px;
    height: 16px;
    background-color: #ff3b30;
    border-radius: 50%;
    color: white;
    font-family: "Helvetica Bold";
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
  }

  /* Grid icon buttons */
  .colIconBtn {
    // padding: 4px;
    // border-radius: 4px;
    // margin-left: 8px;
    height: 15px;
    width: 15px;
    background: transparent;
    border: none;
    margin-right: 20px;
    cursor: pointer;

    // &:hover {
    //   background-color: #f8f8f8;
    // }
    svg {
      fill: currentColor;
    }

    &.active {
      // background-color: #f2f2f2;
      color: @ThemeAccentL5;
    }
  }
}

/* Product grid styling for different views */
.productsGrid {
  display: grid;
  grid-gap: 16px;

  /* Default mobile view (1 column) */
  grid-template-columns: 1fr;

  /* Mobile two-grid view */
  &.twoGridView {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Mobile four-grid view - still 2 columns but different card styling */
  &.fourGridView {
    grid-template-columns: repeat(2, 1fr);

    /* Product cards in four-grid view are more compact */
    .productCard {
      .productImage {
        height: 120px; /* Shorter images */
      }

      .productTitle {
        font-size: 12px; /* Smaller text */
      }

      .productPrice {
        font-size: 12px;
      }

      /* Hide some elements to make cards more compact */
      .productDescription {
        display: none;
      }
    }
  }

  /* Tablet view */
  @media @tablet {
    &.twoColumnTablet {
      grid-template-columns: repeat(2, 1fr);
    }

    &.threeColumnTablet {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  /* Desktop view */
  @media @desktop {
    grid-template-columns: repeat(4, 1fr);
  }
}
