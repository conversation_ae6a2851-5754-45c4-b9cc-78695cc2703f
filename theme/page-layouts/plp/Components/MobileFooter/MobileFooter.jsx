import React, { useState, useEffect } from 'react'
import styles from './MobileFooter.less'
import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper";

const MobileFooter = ({
  filterList = [],
  onFilterModalBtnClick,
  onSortModalBtnClick,
  onColumnCountUpdate,
  columnCount,
  selectedFilters = [],
}) => {
  // Track window width for responsive logic
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 0);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Define breakpoints (non-overlapping)
  const isMobile = windowWidth <= 767;
  const isTablet = windowWidth >= 768 && windowWidth <= 1024;

  // Single toggle handler for both mobile and tablet
  const handleGridToggle = () => {
    console.log("isMobile", isMobile)
    console.log("isTablet", isTablet)
    console.log("columnCount", columnCount)

    if (isMobile) {
      console.log("mobile")
      const current = columnCount?.mobile || 3;
      const next = current === 3 ? 2 : current === 2 ? 1 : 3;
      console.log("next", next)
      console.log("current", current)
      onColumnCountUpdate({ screen: "mobile", count: next });
    } else if (isTablet) {
      console.log("tablet")
      const current = columnCount?.tablet || 3;
      const next = current === 3 ? 2 : current === 2 ? 1 : 3;
      onColumnCountUpdate({ screen: "tablet", count: next });
    }
  };

  // Choose icon based on current view and count
  const getGridIcon = () => {
    if (isMobile) {
      if (columnCount?.mobile === 3) return "grid-two";
      if (columnCount?.mobile === 2) return "grid-one-mob";
      return "grid-two-mob";
    } else if (isTablet) {
      if (columnCount?.tablet === 3) return "grid-two-mob";
      if (columnCount?.tablet === 2) return "grid-one-mob";
      return "grid-three";
    }
    return "grid-two"; // fallback
  };

  return (
    <div className={styles.mobileContainer}>
      <div className={styles.headerContainer}>
        <div className={styles.homeIcon} onClick={() => window.location.href = "/"}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M6.01203 1.89312L2.4187 4.69312C1.8187 5.15978 1.33203 6.15312 1.33203 6.90645V11.8464C1.33203 13.3931 2.59203 14.6598 4.1387 14.6598H11.8587C13.4054 14.6598 14.6654 13.3931 14.6654 11.8531V6.99978C14.6654 6.19312 14.1254 5.15978 13.4654 4.69978L9.34537 1.81312C8.41203 1.15978 6.91203 1.19312 6.01203 1.89312Z" fill="white" />
            <path d="M8 11.9932V9.99316" stroke="#1A1A1A" stroke-width="0.9375" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div className={styles.line}>
          <svg xmlns="http://www.w3.org/2000/svg" width="2" height="14" viewBox="0 0 2 14" fill="none">
            <path opacity="0.2" d="M1.00002 0L1 14" stroke="#E6E6E6" stroke-width="0.75" />
          </svg>
        </div>

        {/* Combined Filter & Sort button with notification badge */}
        <div className={styles.filterSortBtn} onClick={onFilterModalBtnClick}>
          <SvgWrapper svgSrc={"filter"} />
          <span className={styles.filterSortText}>Filters & Sort</span>
          <div className={`${selectedFilters.length > 0 ? styles.notificationBadge : ""} `}>{selectedFilters.length > 0 ? selectedFilters.length : ""}</div>
        </div>
        <div className={styles.line}>
          <svg xmlns="http://www.w3.org/2000/svg" width="2" height="14" viewBox="0 0 2 14" fill="none">
            <path opacity="0.2" d="M1.00002 0L1 14" stroke="#E6E6E6" stroke-width="0.75" />
          </svg>
        </div>
        <div className={styles.headerRight}>
          <button
            className={styles.colIconBtn}
            onClick={handleGridToggle}
            title="Toggle grid"
          >
            <SvgWrapper svgSrc={getGridIcon()} />
          </button>
        </div>
      </div>

    </div>
  );
};
export default MobileFooter


