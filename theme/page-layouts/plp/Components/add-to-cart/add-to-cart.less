@import "../../../../styles/main.less";

.productDescContainer {
  display: flex;
  box-sizing: border-box;

  @media @desktop {
    width: 80vw;
    max-width: 1200px;
  }

  .loader {
    position: relative;
    height: 500px;
    width: 80vw;

    @media @tablet {
      height: 360px;
      width: 100%;
    }

    .loaderContainer {
      position: absolute;
      height: 100%;
      background: transparent;

      .customLoader {
        margin-left: 0;
      }
    }
  }

  .left {
    padding: 24px 16px;
    align-self: center;

    @media @tablet {
      display: none;
    }

    width: 52%;
  }
  .right {
    width: 48%;
    box-sizing: border-box;
    position: relative;
    @media @tablet {
      width: 100%;
      box-sizing: border-box;
    }

    .preview {
      display: none;
      position: absolute;
      z-index: 1;
      margin-top: 15px;
      width: 100%;
    }

    .productInfo {
      .product {
        padding: 24px 24px 0px 24px;
        @media @tablet {
          padding: 16px;
        }

        &__title {
          margin-bottom: 4px;
          word-wrap: break-word;
          letter-spacing: -0.02em;
          overflow: hidden;
          color: @TextHeading;
          text-overflow: ellipsis;
          font-size: 20px;
          font-weight: 600;
          line-height: normal;
          width: 90%;

          @media @tablet {
            display: none;
          }
        }

        &__brand {
          color: @TextHeading;
          font-size: 14px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 16px;

          @media @tablet {
            margin-bottom: 8px;
          }
        }

        .taxLabel {
          color: @TextSecondary;
          margin-bottom: 16px;
          font-size: 12px;
          font-weight: 400;
          line-height: normal;
          letter-spacing: -0.24px;
        }

        .reviewRatingContainer {
          background-color: @ThemeAccentL4;
          border-radius: 4px;
          display: flex;
          width: fit-content;
          align-items: center;
          padding: 4.5px 10px;
          margin: 16px 0 24px;

          @media @tablet {
            margin: 24px 0;
          }

          .ratingWrapper {
            display: flex;
            align-items: center;

            .ratingIcon {
              width: 14px;
              height: 14px;
              margin-left: 4px;

              /deep/ svg path {
                fill: @ThemeAccentD2;
              }
            }
          }

          .separator {
            border-left: 1px solid @DividerStokes;
            height: 100%;
            margin: 0 6px;
            width: 1px;
          }

          .reviewWrapper {
            color: @TextLabel;
          }
        }
        &__price {
          font-size: 16px;
          margin-bottom: 2px;
          display: flex;
          align-items: center;

          &--effective {
            color: @TextHeading;
            font-size: 16px;
            font-weight: 600;
            line-height: normal;
            letter-spacing: -0.32px;

            @media @tablet {
              font-size: 14px;
              letter-spacing: -0.28px;
            }
          }

          &--marked {
            margin-left: 0.25rem;
            color: @TextLabel;
            font-size: 12px;
            font-weight: 400;
            line-height: normal;
            letter-spacing: -0.24px;
            text-decoration: line-through;
          }

          &--discount {
            margin-left: 8px;
            color: @SaleDiscountText;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: -0.24px;
          }
        }

        &__size {
          &--guide {
            display: flex;
            align-items: center;
            gap: 2px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            letter-spacing: -0.28px;
            padding: 0;
            height: auto;
            cursor: pointer;

            @media @tablet {
              font-size: 12px;
            }

            .scaleIcon {
              width: 25px;
              height: 12px;
            }
          }
        }

        .sizeError {
          margin-top: 8px;
          color: @ErrorText;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          letter-spacing: -0.24px;
        }

        .viewMore {
          font-size: 14px;
          font-weight: 500;
          line-height: normal;
          letter-spacing: -0.28px;
          text-transform: uppercase;
          margin-top: 24px;

          @media @tablet {
            margin-top: 16px;
            font-size: 12px;
            letter-spacing: -0.24px;
          }

          span {
            cursor: pointer;
          }
        }

        .crossIcon {
          position: absolute;
          top: 16px;
          right: 16px;
          cursor: pointer;

          @media @tablet {
            display: none;
          }
        }
      }
      .actionButtons {
        display: flex;
        align-items: center;
        gap: 12px;
        position: sticky;
        bottom: 0;
        padding: 16px 24px;
        z-index: 9;
        background-color: @DialogBackground;

        @media @tablet {
          padding: 16px;
        }

        .buyNow {
          border: 0.8px solid @ButtonPrimary;
        }

        button {
          flex: 1;

          @media @tablet {
            height: 40px;
            padding: 12px 16px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.sizeCartContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;

  @media @tablet {
    margin-top: 16px;
  }

  .sizeWrapper {
    position: relative;
    min-width: 0;
    flex-grow: 0;
    transition: all 0.5s;

    @media @tablet {
      margin-right: 8px;
    }

    .sizeButton {
      padding: 11px 16px;
      border: 0.8px solid @DividerStokes;
      border-radius: @ButtonRadius;
      background-color: @White;
      width: 100%;
      height: 100%;
      font-weight: 500;
      font-size: 14px;
      line-height: 16px;
      letter-spacing: -0.02em;
      text-transform: uppercase;
      color: @TextHeading;

      @media @tablet {
        font-size: 12px;
        line-height: 14px;
      }

      .selectedSize {
        text-align: left;
        color: @TextHeading;
        border-radius: unset;
        .text-line-clamp();
      }

      .dropdownArrow {
        height: 24px;
        width: 24px;
        flex: 0 0 24px;
      }

      .rotateArrow {
        transform: rotate(180deg);
      }
    }
    .disabledButton {
      color: @TextDisabled;
    }
    .sizeDropdown {
      position: absolute;
      background-color: @DialogBackground;
      top: 100%;
      min-width: 100%;
      white-space: nowrap;
      border: 1px solid #d4d1d1;
      box-shadow:
        0px 4px 4px rgba(0, 0, 0, 0.15),
        0px 12px 16px rgba(0, 0, 0, 0.16);
      border-radius: @ButtonRadius;
      padding: 0.5rem;
      z-index: 10;

      li {
        padding: 8px 12px;
        border-radius: 4px;
      }

      .selected_size {
        background-color: @ThemeAccentL3;
      }

      .disabled_size {
        text-decoration-line: line-through;
        color: @TextDisabled;
      }

      .selectable_size {
        cursor: pointer;

        &:hover {
          background-color: @ThemeAccentL3;
        }
      }
    }
  }
}

.short-description {
  margin: 24px 0;
}

.sizeSelection {
  margin-top: 24px;
  display: grid;
  grid-template-rows: 1fr;
  transition: grid-template-rows 0.5s;

  @media @tablet {
    margin-top: 16px;
  }

  .sizeHeaderContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  & > div {
    overflow: hidden;
  }
  &__label {
    font-size: 12px;
    font-weight: 400;
    line-height: normal;
    letter-spacing: -0.24px;
    color: @TextLabel;
  }
  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    .grid-gap(8px);
  }

  &__block {
    border-radius: @ButtonRadius;
    border: 1px solid @DividerStokes;
    padding: 6px 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    position: relative;

    &--selected {
      background-color: @ThemeAccent;
    }

    &--selectable {
      cursor: pointer;
    }

    &--disable {
      cursor: default;
      color: @TextDisabled;
    }

    svg {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;

      line {
        stroke: @DividerStokes;
        stroke-width: 1;
      }
    }
  }
}
