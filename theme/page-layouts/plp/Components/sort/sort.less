// @import "../../../../styles/main.less";

// .dropdown {
//   display: flex;
//   justify-content: space-between;
//   align-items: flex-start;

//   position: relative;
//   display: flex;
//   flex-direction: column;
//   font-size: 18px;
//   width: 100%;
//   .user-select-none();

//   .b1();
//   .sortLabel {
//     color: @Dark-80;
//   }

//   @media @tablet {
//     .sortLabel {
//       color: @Dark-80;
//       leading-trim: both;
//       text-edge: cap;
//       font-family: "Helvetica Bold";
//       font-size: 16px;
//       font-style: normal;
//       font-weight: 700;
//       line-height: 130%; /* 20.8px */
//     }
//   }

//   span {
//     font-family: "Helvetica Medium";
//   }
//   .flex {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     gap: 8px;
//     cursor: pointer;

//     // The arrow icon wrapper - desktop only
//     .sortIcon {
//       display: none; // hide by default
//       @media (min-width: 769px) {
//         display: block; // show on desktop (≥769px)
//       }
//     }

//     // The plus/minus wrapper - mobile only
//     .mobileIcon {
//       display: none; // hide by default
//       @media (max-width: 768px) {
//         display: block; // show on tablet+mobile (≤768px)
//         cursor: pointer;
//       }
//     }
//   }

//   .selectedSortWrapper {
//     margin-left: 8px;
//     color: @TextHeading;
//     display: flex;
//     align-items: center;
//     cursor: pointer;
//     .user-select-none();

//     .arrowIcon {
//       width: 12px;
//       height: 12px;
//       fill: black;
//       margin-left: 4px;
//       transition: transform 0.3s ease;
//     }
//   }
//   .rotateIcon {
//     transform: rotate(180deg);
//     transition: transform 0.3s ease;
//   }

//   // Desktop dropdown (original behavior)
//   .desktopDropdown {
//     display: none; // Hide on mobile

//     @media (min-width: 769px) {
//       display: block;
//     }

//     .dropdownList {
//       background-color: @DialogBackground;
//       position: absolute;
//       top: 32px;
//       right: 0;
//       z-index: @menu;
//       min-width: 260px;
//       padding: 1rem;
//       font-family: "Helvetica Medium";
//     }
//   }

//   // Mobile dropdown container with smooth transition
//   .dropdownContainer {
//     width: 100%;
//     max-height: 0;
//     overflow: hidden;
//     transition: max-height 0.3s ease-in-out;
//     position: relative;
//     display: none; // Hide on desktop

//     @media (max-width: 768px) {
//       display: block;
//     }

//     &.open {
//       max-height: 500px; // Adjust based on your content
//     }
//   }

//   // Common styles for both dropdown lists
//   .dropdownList,
//   .mobileDropdownList {
//     background-color: @DialogBackground;
//     font-family: "Helvetica Medium";
//     z-index: @menu;

//     .fontCircularMedium {
//       display: flex;
//       align-items: center;
//       justify-content: flex-start;
//       gap: 4px;
//       font-family: "Helvetica Medium";
//     }

//     .radioInput {
//       -webkit-appearance: none;
//       -moz-appearance: none;
//       appearance: none;
//       width: 18px;
//       height: 18px;
//       border-radius: 50%;
//       border: 1px solid #808080;
//       outline: none;
//       cursor: pointer;
//       margin-right: 5px;
//       position: relative;

//       &:checked {
//         border: 1px solid #ff3b30;

//         &::after {
//           content: "";
//           position: absolute;
//           top: 50%;
//           left: 50%;
//           transform: translate(-50%, -50%);
//           width: 10px;
//           height: 10px;
//           border-radius: 50%;
//           background-color: #ff3b30;
//         }
//       }
//     }

//     li {
//       display: flex;
//       align-items: center;
//       justify-content: flex-start;
//       column-gap: 4px;
//       font-size: 13px;
//       text-transform: capitalize;
//       line-height: 14px;
//       color: black;
//       margin: 0 15px;
//       padding: 5px 0;
//       height: 40px;
//       cursor: pointer;
//       span {
//         font-weight: bold;
//       }
//       .radioIcon {
//         width: 20px;
//         height: 20px;
//       }
//     }
//   }

//   // Mobile specific dropdown styling
//   .mobileDropdownList {
//     padding: 15px 10px;
//     width: 100%;

//     @media @mobile {
//       position: relative;
//       top: 0;
//       left: 0;
//       right: 0;

//       li {
//         padding: 10px 0;

//         justify-content: flex-start;
//       }
//     }
//   }

//   &.sort-list {
//     .sort-icon {
//       display: none;
//       @media @mobile {
//         display: flex;
//       }
//     }

//     .selectedSort {
//       text-decoration: none;
//     }

//     @media @mobile {
//       .selectedSortWrapper {
//         .selectedSort {
//           display: none;
//           text-decoration: none;
//         }
//       }
//     }
//   }
// }

// .OutsideClickHandler {
//   width: 100%;
//   position: relative;
// }

@import "../../../../styles/main.less";

.outSideContainer {
  width: 100%;
}
.dropdown {
  position: relative;
  .user-select-none();
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
  .b1();
  width: 100%;

  .sortLabelContainer {
    display: flex;
    align-items: center;
    cursor: pointer;
    @media @desktop {
      // margin-right: 0.75rem;
      gap: 0.75rem;
    }
  }
  .sortLabelWrapper {
    display: flex;
    align-items: center;
    gap: 4px;
    @media @tablet {
      .arrowIcon {
        display: none;
      }
    }
  }

  .sortLabel {
    color: var(--Colors-Black, #212121);
    text-align: center;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    // font-size: 1.125rem;
    font-size: clamp(14px, 0.94vw, 18px);
    font-style: normal;
    line-height: 130%; /* 23.4px */

    @media @tablet {
      color: @Dark-80;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Bold";
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 20.8px */
      margin-right: 0rem;
    }

    @media (min-width: 769px) and (max-width: 1024px) {
      font-size: 0.75rem;
      line-height: 130%; /* 20.8px */
    }
  }

  .selectedSortWrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    .user-select-none();
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.75rem;

    .arrowIcon,
    .toggleIcon {
      width: 16px;
      height: 16px;
      fill: @TextHeading;
      margin-right: 4px;
      // transition: transform 0.2s ease;
      // remove ease so basically remove transition
      &.open {
        transform: rotate(180deg);
      }
      @media (min-width: 769px) and (max-width: 1024px) {
        font-size: 0.75rem;
        line-height: 130%; /* 20.8px */
      }
    }

    .selectedSort {
      color: var(--Dark, #1a1a1a);
      text-align: center;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Medium";
      // font-size: 1.125rem;
      font-size: clamp(14px, 0.94vw, 18px);
      font-style: normal;
      line-height: 130%; /* 23.4px */
      @media @tablet {
        font-size: 1rem;
        line-height: 130%; /* 20.8px */
        color: var(--Dark-80, #333);
      }
      @media (min-width: 769px) and (max-width: 1024px) {
        font-size: 0.75rem;
        line-height: 130%; /* 20.8px */
      }
    }
  }

  .dropdownList {
    list-style: none;
    background-color: @DialogBackground;
    position: absolute;
    top: 2.5rem;
    right: 1.5rem;
    z-index: @menu;
    min-width: 240px;
    border-radius: 1.875rem;
    padding: 1rem;
    animation: fadeIn 0.2s ease;

    li {
      font-size: 0.75rem;
      line-height: 14px;
      cursor: pointer;
      font-family: "Helvetica Medium";

      &:not(:last-child) {
        margin-bottom: 14px;
      }

      label {
        display: flex;
        align-items: center;
      }

      input[type="radio"] {
        /* modern browsers */
        accent-color: red;
        /* fallbacks */
        margin-right: 8px;
      }
      span {
        color: var(--Dark-80, #333);
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Medium";
        font-size: 1.875rem;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 18.2px */
        letter-spacing: 0.14px;

        @media @desktop {
          font-size: 1rem;
          line-height: 130%; /* 18.2px */
        }
      }

      // &:hover {
      //   opacity: 0.5;
      // }
    }
    @media @tablet {
      display: none;
    }
  }

  .dropdownContainer {
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
    position: relative;
    display: none; // Hide on desktop

    @media (max-width: 768px) {
      display: block;
    }

    &.open {
      max-height: 500px; // Adjust based on your content
    }
  }

  &.sort-list {
    @media @mobile {
      .dropdownList {
        position: fixed;
        inset: 0; /* top:0; right:0; bottom:0; left:0 */
        padding-top: 60px;
        border-radius: 0;
        animation: slideUp 0.3s ease;

        li {
          justify-content: center;
          padding: 18px;
          border-bottom: 1px solid @Gray;
        }
      }
    }
  }
  .mobileDropdownList {
    margin-top: 1.25rem;
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    .mobileDropdownItem {
      gap: 10px;
    }

    .fontCircularMedium {
      color: var(--Dark-80, #333);
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Medium";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 18.2px */
      letter-spacing: 0.14px;
    }
    .radioInput {
      accent-color: red;
      margin-right: 0.5rem;
    }
  }
}

/* Animations */
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
// At the bottom of sort.less
@media (max-width: 769px) {
  .desktopOnly {
    display: none;
  }
}
@media (min-width: 769px) {
  .mobileOnly {
    display: none;
  }
}
