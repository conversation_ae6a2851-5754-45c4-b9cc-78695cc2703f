// import React, { useState, useMemo, useEffect, useRef } from "react";
// import OutsideClickHandler from "react-outside-click-handler";
// import { useLocation, useNavigate } from "react-router-dom";
// import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper";
// import styles from "./sort.less";
// import arrowRight from "../../../../assets/images/arrow-right.svg"
// import { cdl } from "three/src/nodes/TSL.js";
// import plusIcon from "../../../../assets/images/plus.png"
// import minusIcon from "../../../../assets/images/minus.png";

// function Sort({ sortList = [], onSortUpdate = () => { } }) {
//   const [sortOpen, setSortOpen] = useState(false);
//   const dropdownRef = useRef(null);
//   console.log(sortOpen, "sortOpen");


//   const selectedSort = useMemo(() => {
//     const selectedItem = sortList?.find((x) => x.is_selected);
//     if (selectedItem) {
//       return selectedItem.name;
//     }
//     return sortList?.[0]?.name;
//   }, [sortList]);

//   const [selectedSortItem, setSelectedSortItem] = useState(() => {
//     let selectedItem = sortList?.find((x) => x.is_selected);
//     return selectedItem || sortList?.[0];
//   });


//   function updateSortOption(e, sortValue) {
//     e.stopPropagation();
//     e.preventDefault();
//     onSortUpdate(sortValue);
//     closeSortOption(e);
//   }

//   function closeSortOption(event) {
//     setSortOpen(false);
//   }

//   useEffect(() => {
//     closeSortOption();
//   }, [location?.search]);

//   function toggleSortDropdown() {
//     setSortOpen(!sortOpen);
//   }

//   // Toggle specifically for mobile icons
//   function handleIconClick(e) {
//     e.stopPropagation(); // Prevent the parent div's onClick from firing
//     toggleSortDropdown();
//   }

//   return (
//     <OutsideClickHandler onOutsideClick={(e) => closeSortOption(e)} className={styles.OutsideClickHandler}>
//       <div className={styles.dropdown}>
//         <div className={styles.flex}>
//           <span className={`${styles.sortLabel} ${styles.fontHeader}`} onClick={toggleSortDropdown}>
//             Sort:
//           </span>

//           <span className={styles.selectedSort} onClick={toggleSortDropdown}>
//             {selectedSort}
//           </span>
//           <div
//             className={`${styles.sortIcon} ${sortOpen ? styles.rotateIcon : ""}`}
//             onClick={toggleSortDropdown}
//           >
//             <SvgWrapper
//               className={`${styles.arrowIcon}`}
//               svgSrc={"arrow-down"}
//             />
//           </div>
//           <div className={styles.mobileIcon} onClick={handleIconClick}>
//             <img
//               src={sortOpen ? minusIcon : plusIcon}
//               alt="icon"
//             />
//           </div>
//         </div>

//         {/* Desktop view dropdown */}
//         {sortOpen && (
//           <div className={styles.desktopDropdown}>
//             <ul className={styles.dropdownList}>
//               {sortList?.map((sortType, index) => (
//                 <li
//                   key={`desktop-${sortType.value}-${index}`}
//                   onClick={(e) => handleSortUpdate(sortType.value)}
//                 >
//                   <span className={styles.fontCircularMedium}>
//                     <input
//                       type="radio"
//                       name="sortType"
//                       className={styles.radioInput}
//                       checked
//                       onChange={() => { }} // React requires onChange for controlled inputs
//                     />
//                     {sortType.name}
//                   </span>
//                 </li>
//               ))}
//             </ul>
//           </div>
//         )}

//         {/* Mobile view dropdown with animation */}
//         <div
//           className={`${styles.dropdownContainer} ${sortOpen ? styles.open : ""}`}
//           ref={dropdownRef}
//         >
//           <ul className={styles.mobileDropdownList}>
//             {sortList?.map((sortType, index) => (
//               <li
//                 key={`mobile-${sortType.value}-${index}`}
//                 onClick={(e) => handleSortUpdate(sortType.value)}
//               >
//                 <span className={styles.fontCircularMedium}>
//                   <input
//                     type="radio"
//                     name="sortType"
//                     className={styles.radioInput}
//                     checked={selectedSort === sortType?.name}
//                     onChange={() => { }} // React requires onChange for controlled inputs
//                   />
//                   {sortType.name}
//                 </span>
//               </li>
//             ))}
//           </ul>
//         </div>

//       </div>
//     </OutsideClickHandler>
//   );
// }




import React, { useState, useMemo, useEffect, useRef } from "react";
import OutsideClickHandler from "react-outside-click-handler";
import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper";
import styles from "./sort.less";
import { useLocation, useNavigate } from "react-router-dom";

function Sort({
  sortList = [],
  onSortUpdate = () => { },
  mobileSortOpen = false,
  setMobileSortOpen = () => { },
}) {
  const [sortOpen, setSortOpen] = useState(false);
  const [currentValue, setCurrentValue] = useState(null);
  const location = useLocation();
  const dropdownRef = useRef(null);
  const navigate = useNavigate();

  const handleSortUpdate = (value) => {
    const searchParams = new URLSearchParams(location.search);
    if (value) searchParams.set("sort_on", value);
    else searchParams.delete("sort_on");
    searchParams.delete("page_no");

    navigate({ pathname: location.pathname, search: searchParams.toString() });
  };

  const selectedSort = useMemo(() => {
    const sel = sortList.find((x) => x.is_selected) || sortList[0] || {};
    setCurrentValue(sel.value);
    return sel.name || "";
  }, [sortList]);

  const updateSortOption = (e, sortValue) => {
    e.stopPropagation();
    e.preventDefault();
    setCurrentValue(sortValue);
    onSortUpdate(sortValue);
    setSortOpen(false);
    setMobileSortOpen(false);
  };

  useEffect(() => {
    setSortOpen(false);
    setMobileSortOpen(false);
  }, [location.search]);

  return (
    <OutsideClickHandler onOutsideClick={() => {
      setSortOpen(false);
      setMobileSortOpen(false);
    }}>
      <div className={`${styles.dropdown} ${styles["sort-list"]}`}>
        {/* Entire row clickable (desktop only) */}
        <div
          className={styles.sortLabelContainer}
          onClick={() => setSortOpen((o) => !o)}
        >
          <div className={styles.sortLabelWrapper}>
            <SvgWrapper className={styles.arrowIcon} svgSrc={"sortIcon"} />
            <span className={styles.sortLabel}>Sort: </span>
          </div>
          <div className={styles.selectedSortContainer}>
            <div className={styles.selectedSortWrapper}>
              <span className={styles.selectedSort}>&nbsp;{selectedSort}</span>

              {/* Desktop arrow */}
              <SvgWrapper
                className={`${styles.arrowIcon} ${sortOpen ? styles.open : ""} ${styles.desktopOnly}`}
                svgSrc="arrow-plp-right"
              />

              {/* Mobile icon should be rendered outside this component (in parent) */}
            </div>
          </div>
        </div>

        {/* Desktop dropdown */}
        {sortOpen && (
          <ul className={styles.dropdownList}>
            {sortList?.map((item, idx) => (
              <li key={item.value + idx} onClick={(e) => updateSortOption(e, item.value)}>
                <label>
                  <input
                    type="radio"
                    name="sortOption"
                    checked={currentValue === item.value}
                    readOnly
                  />
                  <span>{item.name}</span>
                </label>
              </li>
            ))}
          </ul>
        )}

        {/* Mobile dropdown */}
        <div
          className={`${styles.dropdownContainer} ${mobileSortOpen ? styles.open : ""}`}
          ref={dropdownRef}
        >
          <ul className={styles.mobileDropdownList}>
            {sortList?.map((sortType, index) => (
              <li
                key={`mobile-${sortType.value}-${index}`}
                onClick={() => {
                  handleSortUpdate(sortType.value);
                  setMobileSortOpen(false);
                }}
                className={styles.mobileDropdownItem}
              >
                <span className={styles.fontCircularMedium}>
                  <input
                    type="radio"
                    name="sortType"
                    className={styles.radioInput}
                    checked={currentValue === sortType.value}
                    readOnly
                  />
                  {sortType.name}
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </OutsideClickHandler>
  );
}

export default Sort;