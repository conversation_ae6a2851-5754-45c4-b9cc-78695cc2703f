.tabsCarouselContainer {
  width: 100%;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #e0e0e0;
}

.tabsSwiper {
  width: 100%;
  padding: 8px 0;
}

.swiperSlide {
  width: auto !important;
}

.tabItem {
  padding: 0 16px;
  font-size: 14px;
  display: inline-block;
  color: #666;
  cursor: pointer;
  white-space: nowrap;
  position: relative;
  height: 100%;

  &.active {
    color: #000;
    font-weight: 500;

    &:after {
      content: "";
      position: absolute;
      bottom: -9px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #000;
    }
  }
}

.tabContent {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabsPlaceholder {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none;
  padding: 8px 0;

  &::-webkit-scrollbar {
    display: none;
  }

  .tabItem {
    padding: 0 16px;
    font-size: 14px;
    display: inline-block;
    color: #666;
    white-space: nowrap;

    &.active {
      color: #000;
      font-weight: 500;
    }
  }
}
