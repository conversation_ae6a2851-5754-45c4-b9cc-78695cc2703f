import React, { useRef, useState, useEffect } from 'react';
import styles from './TabsCarousel.less';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';

/**
 * TabsCarousel - A custom carousel component for displaying category tabs with horizontal scrolling
 * 
 * @param {Array} tabs - Array of tab objects with display and path properties
 * @param {String} activeTab - Currently active tab
 * @param {Function} onTabClick - Callback function when a tab is clicked
 * @returns {JSX.Element}
 */
const HeaderPlpMobile = ({
    tabs = [],
    activeTab = '',
    onTabClick = () => { }
}) => {
    const swiperRef = useRef(null);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);

        // Find the active tab index to scroll to it on initial load
        if (swiperRef.current && activeTab) {
            const activeIndex = tabs.findIndex(tab =>
                tab.display.toLowerCase() === activeTab.toLowerCase());

            if (activeIndex > -1) {
                setTimeout(() => {
                    swiperRef.current.swiper.slideTo(activeIndex);
                }, 300);
            }
        }
    }, [activeTab, tabs]);

    // Ensure we only render Swiper on the client-side
    if (!isMounted) {
        return (
            <div className={styles.tabsPlaceholder}>
                {tabs.map((tab, index) => (
                    <div
                        key={index}
                        className={`${styles.tabItem} ${activeTab === tab.display.toLowerCase() ? styles.active : ''}`}
                    >
                        {tab.display}
                    </div>
                ))}
            </div>
        );
    }

    return (
        <div className={styles.tabsCarouselContainer}>
            <Swiper
                ref={swiperRef}
                slidesPerView="auto"
                spaceBetween={0}
                freeMode={true}
                className={styles.tabsSwiper}
            >
                <SwiperSlide className={`${styles.tabItem} ${activeTab === 'all' ? styles.active : ''}`}>
                    <div
                        onClick={() => onTabClick({ display: 'All', path: '' })}
                        className={styles.tabContent}
                    >
                        All
                    </div>
                </SwiperSlide>

                {tabs.map((tab, index) => (
                    <SwiperSlide key={index} className={styles.swiperSlide}>
                        <div
                            onClick={() => onTabClick(tab)}
                            className={`${styles.tabItem} ${activeTab === tab.display.toLowerCase() ? styles.active : ''}`}
                        >
                            <div className={styles.tabContent}>
                                {tab.display}
                            </div>
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    );
};

export default HeaderPlpMobile;