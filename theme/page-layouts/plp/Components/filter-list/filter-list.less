@import "../../../../styles/main.less";

.filter__list {
  font-family: "Helvetica Medium";
  &--search {
    position: relative;
    margin-bottom: 16px;
    .text {
      font-size: 12px;
      line-height: 14px;
      padding: 15px 15px 15px 44px;
      width: 100%;
      border: 1px solid @DividerStokes;
      background-color: transparent;
    }
    .search-icon {
      width: 18px;
      height: 18px;
      position: absolute;
      left: 12px;
    }
  }
  &--items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .filter__item {
      cursor: pointer;
      font-size: 12px;
      line-height: 14px;

      &:hover .checkbox-icon:not(.selected) {
        stroke: @ButtonPrimary;
      }
      .checkbox-icon {
        width: 26px;
        height: 26px;
        margin-right: 8px;
        stroke: @DividerStokes;
        &.selected {
          fill: red;
          stroke: none;
        }
      }
      &--color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
        border: 1px solid @DividerStokes;
      }
      &--value {
        color: var(--Dark-80, #333);
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Medium";
        font-size: clamp(12px, 0.73vw, 14px);
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 18.2px */
        letter-spacing: 0.14px;
        margin-right: 4px;
      }
      &--count {
        color: @TextDisabled;
      }
    }
  }
  &--item:not(:last-child) {
    margin-bottom: 8px;
  }
  .view-more {
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    margin-top: 16px;
    color: @TextBody;
    cursor: pointer;
    .arrow-icon {
      fill: @TextBody;
      width: 20px;
      height: 20px;
      &.expanded {
        transform: scale(1, -1);
      }
    }
  }
  .filter__popup {
    width: 1023px;
    height: calc(100vh - var(--headerHeight) - 40px);
    max-height: 562px;
    border: 1px solid @DividerStokes;
    background-color: @DialogBackground;
    position: fixed;
    top: calc(50% + (var(--headerHeight) / 2));
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 41;
    border-radius: 8px;
    &--header {
      padding: 16px 24px;
      border-bottom: 1px solid @DividerStokes;
      .search {
        &__input {
          border-radius: 4px;
          padding: 16px;
          width: 264px;
          border: 1px solid @DividerStokes;
          margin-right: 32px;
        }
      }
      .alphabets {
        display: flex;
        .column-gap(13px);
        .overflow-ellipsis();
        & > li {
          font-size: 14px;
          font-weight: 500;
          line-height: 16px;
          letter-spacing: -0.02em;
          color: @ButtonPrimary;
          &.disabled {
            color: @TextDisabled;
          }
        }
      }
      .close-icon {
        margin-left: auto;
        width: 24px;
        height: 24px;
        fill: @TextHeading;
        cursor: pointer;
      }
    }
    &--content {
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      height: calc(100% - 87px);
      padding: 24px 12px;
      overflow-x: auto;
      scroll-behavior: smooth;
      scroll-margin-left: 24px;
      .alphabet-label {
        padding-left: 12px;
        margin-bottom: 11px;
        & > h4 {
          color: @ButtonPrimary;
        }
      }
      .filter {
        padding-left: 12px;
        margin-bottom: 5px;
        &__item {
          font-size: 12px;
          line-height: 14px;
          cursor: pointer;
          &:hover .checkbox-icon:not(.selected) {
            stroke: @ButtonPrimary;
          }
          .checkbox-icon {
            width: 26px;
            height: 26px;
            margin-right: 8px;
            stroke: @DividerStokes;
            &.selected {
              // fill: @ButtonPrimary;
              fill: @Required;
            }
          }
          &--value {
            color: @TextBody;
            margin-right: 4px;
          }
          &--count {
            color: @TextDisabled;
          }
        }
      }
      & > * {
        min-width: 310px;
      }
    }
  }
  .overlay {
    position: fixed;
    .inset(0);
    background-color: @Overlay;
    opacity: 0.6;
    z-index: 40;
    &.show {
      display: block;
    }
  }
  .filter__item--color {
    overflow: hidden;
  }
  .multiIcon {
    background: linear-gradient(
      136deg,
      rgba(73, 52, 155, 1) 0%,
      rgba(28, 188, 217, 1) 33%,
      rgba(87, 210, 28, 1) 53%,
      rgba(255, 202, 25, 1) 73%,
      rgba(209, 11, 0, 1) 100%
    );
  }
}

.filterList {
  width: calc(100% - 10px);
  font-family: "Helvetica Medium";
}

.sizeButtonsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px;
}

.sizeButton {
  min-width: 40px;
  height: 40px;
  padding: 0 12px;
  border: 1px solid #e5e5e5;
  background: #ffffff;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: "Helvetica Bold";

  &:hover {
    border-color: #1a1a1a;
  }
}

.sizeButtonSelected {
  background: #ff0000;
  color: #ffffff;

  &:hover {
    border-color: #ff0000;
  }
}

.colorButtonsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 12px;
}

.colorButton {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e5e5;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: #1a1a1a;
  }
}

.colorButtonSelected {
  border: 2px solid #1a1a1a;
}

.colorCheckIcon {
  width: 16px;
  height: 16px;
  fill: #ffffff;
  filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));
}

// @import "../../../../styles/main.less";

// .filter__list {
//   &--search {
//     position: relative;
//     margin-bottom: 16px;
//     .text {
//       font-size: 12px;
//       line-height: 14px;
//       padding: 15px 15px 15px 44px;
//       width: 100%;
//       border: 1px solid @DividerStokes;
//       background-color: transparent;
//     }
//     .search-icon {
//       width: 18px;
//       height: 18px;
//       position: absolute;
//       left: 12px;
//     }
//   }
//   &--items {
//     .filter__item {
//       cursor: pointer;
//       font-size: 12px;
//       line-height: 14px;

//       &:hover .checkbox-icon:not(.selected) {
//         stroke: @ButtonPrimary;
//       }
//       .checkbox-icon {
//         width: 26px;
//         height: 26px;
//         margin-right: 8px;
//         stroke: @DividerStokes;
//         &.selected {
//           fill: @ButtonPrimary;
//           stroke: none;
//         }
//       }
//       &--color {
//         width: 16px;
//         height: 16px;
//         border-radius: 50%;
//         margin-right: 8px;
//         border: 1px solid @DividerStokes;
//       }
//       &--value {
//         color: @TextBody;
//         margin-right: 4px;
//       }
//       &--count {
//         color: @TextDisabled;
//       }
//     }
//   }
//   &--item:not(:last-child) {
//     margin-bottom: 8px;
//   }
//   .view-more {
//     text-transform: uppercase;
//     font-size: 14px;
//     font-weight: 500;
//     line-height: 16px;
//     margin-top: 16px;
//     color: @TextBody;
//     cursor: pointer;
//     .arrow-icon {
//       fill: @TextBody;
//       width: 20px;
//       height: 20px;
//       &.expanded {
//         transform: scale(1, -1);
//       }
//     }
//   }
//   .filter__popup {
//     width: 1023px;
//     height: calc(100vh - var(--headerHeight) - 40px);
//     max-height: 562px;
//     border: 1px solid @DividerStokes;
//     background-color: @DialogBackground;
//     position: fixed;
//     top: calc(50% + (var(--headerHeight) / 2));
//     left: 50%;
//     transform: translate(-50%, -50%);
//     z-index: 41;
//     border-radius: 8px;
//     &--header {
//       padding: 16px 24px;
//       border-bottom: 1px solid @DividerStokes;
//       .search {
//         &__input {
//           border-radius: 4px;
//           padding: 16px;
//           width: 264px;
//           border: 1px solid @DividerStokes;
//           margin-right: 32px;
//         }
//       }
//       .alphabets {
//         display: flex;
//         .column-gap(13px);
//         .overflow-ellipsis();
//         & > li {
//           font-size: 14px;
//           font-weight: 500;
//           line-height: 16px;
//           letter-spacing: -0.02em;
//           color: @ButtonPrimary;
//           &.disabled {
//             color: @TextDisabled;
//           }
//         }
//       }
//       .close-icon {
//         margin-left: auto;
//         width: 24px;
//         height: 24px;
//         fill: @TextHeading;
//         cursor: pointer;
//       }
//     }
//     &--content {
//       display: flex;
//       flex-wrap: wrap;
//       flex-direction: column;
//       height: calc(100% - 87px);
//       padding: 24px 12px;
//       overflow-x: auto;
//       scroll-behavior: smooth;
//       scroll-margin-left: 24px;
//       .alphabet-label {
//         padding-left: 12px;
//         margin-bottom: 11px;
//         & > h4 {
//           color: @ButtonPrimary;
//         }
//       }
//       .filter {
//         padding-left: 12px;
//         margin-bottom: 5px;
//         &__item {
//           font-size: 12px;
//           line-height: 14px;
//           cursor: pointer;
//           &:hover .checkbox-icon:not(.selected) {
//             stroke: @ButtonPrimary;
//           }
//           .checkbox-icon {
//             width: 26px;
//             height: 26px;
//             margin-right: 8px;
//             stroke: @DividerStokes;
//             &.selected {
//               fill: @ButtonPrimary;
//             }
//           }
//           &--value {
//             color: @TextBody;
//             margin-right: 4px;
//           }
//           &--count {
//             color: @TextDisabled;
//           }
//         }
//       }
//       & > * {
//         min-width: 310px;
//       }
//     }
//   }
//   .overlay {
//     position: fixed;
//     .inset(0);
//     background-color: @Overlay;
//     opacity: 0.6;
//     z-index: 40;
//     &.show {
//       display: block;
//     }
//   }
//   .filter__item--color {
//     overflow: hidden;
//   }
//   .multiIcon {
//     background: linear-gradient(
//       136deg,
//       rgba(73, 52, 155, 1) 0%,
//       rgba(28, 188, 217, 1) 33%,
//       rgba(87, 210, 28, 1) 53%,
//       rgba(255, 202, 25, 1) 73%,
//       rgba(209, 11, 0, 1) 100%
//     );
//   }
// }
