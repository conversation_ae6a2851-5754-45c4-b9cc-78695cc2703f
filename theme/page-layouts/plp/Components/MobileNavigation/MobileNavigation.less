@import "../../../../styles/main.less";
.mobileNavigationWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 0px 0px var(--radius-main, 20px) var(--radius-main, 20px);
  border-bottom: 1px solid #e6e6e6;
  background: #fff;
  gap: 0.5rem;
}

/* Header Row */
.headerRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.25rem 1.25rem;
  width: 100%;
}

/* Icons */
.leftIcon {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 999px;
  border: 1px solid #e6e6e6;
  background: #fff;
}
.rightIcons {
  display: flex;
  justify-content: center;
  align-items: center;
}
.rightIcon1,
.rightIcon2 {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

/* Main Text */
.mainText {
  color: var(--Dark-80, #333);
  leading-trim: both;
  text-edge: cap;
  /* H5 */
  font-family: "Helvetica Bold";
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 23.4px */
}

/* Tabs Wrapper */
.tabsWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1000px;
  max-height: 40px;
  background-color: #ffffff;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

/* Category Row */
.categoryRow {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  justify-content: flex-start;
  align-items: center;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  max-width: 100%;
  white-space: nowrap;
  padding: 0 1.25rem;
  font-family: "Helvetica Medium";

  &::-webkit-scrollbar {
    display: none;
  }
}

/* Individual Tab */
.categoryItem {
  display: flex;
  padding: 10px;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
  border: none;
  background: none;
  cursor: pointer;

  .categoryItemText {
    font-size: clamp(14px, 0.94vw, 18px);
    color: var(--Dark-80, #333);
    font-family: "Helvetica Medium";
  }

  .line {
    height: 1px;
    width: 100%;
    background-color: @Dark-10;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  &.active {
    .line {
      background-color: #ff1e00;
      transform: scaleX(1);
    }
  }
}

/* Below tabs button container */
.tabsContainer {
  display: flex;
  width: 375px;
  padding: 1px var(--padding-main, 20px);
  align-items: flex-start;
}

/* Tab button */
.categoryTabs {
  display: flex;
  gap: 16px;
}

.categoryButton {
  display: flex;
  padding: 10px 10px 0px 10px;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
}

/* Tab label */
.categoryLabel {
  color: var(--Dark-80, #333);
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Now Display";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%;
  letter-spacing: 0.14px;
}

/* Animation */
@keyframes slideInBottom {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

// .line {
//   height: 4px;
//   width: 100%;
//   background-color: transparent;
//   border-top-left-radius: 35px;
//   border-top-right-radius: 35px;
//   animation: slideInBottom 0.3s ease-out;
//   &.active {
//     background-color: #ff1e00;
//     box-shadow: 0 1px 2px rgba(255, 30, 0, 0.2);
//   }
// }

/* Search Modal Styles */
.mobileSearchModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  animation: fadeInOverlay 0.3s ease-out;
}

.mobileSearchOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.mobileSearchContent {
  position: relative;
  width: 100%;
  background-color: white;
  border-radius: 20px 20px 0 0;
  height: 90vh;
  display: flex;
  flex-direction: column;
  animation: slideUpFromBottom 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: bottom;
}

.mobileSearchHeader {
  padding: 20px 16px 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.mobileSearchInputWrapper {
  width: 100%;
}

.mobileSearchFlex {
  background-color: #f5f5f5;
  border-radius: 25px;
  padding: 12px 16px;
  width: 100%;
  gap: 12px;
  margin: 0;
  display: flex;
  align-items: center;
}

.mobileSearchBody {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.searchflex {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  gap: 8px;
  transition: all 0.3s ease;
}

.search__input--text {
  background: transparent;
  border: none;
  padding: 0;
  height: 100%;
  border-radius: unset;
  font-size: 14px;
  line-height: 20px;
  font-family: "Helvetica Medium";
  flex: 1;

  &::placeholder {
    color: #666;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
  }
}

.search__input--mobile-active {
  cursor: text;
  caret-color: auto;
  font-family: "Helvetica Medium" !important;
  &:focus {
    outline: none;
  }
}

.search--closeIcon {
  margin-left: 8px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  color: #555;
  transition: color 0.2s ease;

  &:hover {
    color: #222;
  }
}

.headerIcon {
  cursor: pointer;
  color: #555;
  transition: color 0.2s ease;

  &:hover {
    color: #222;
  }
}

.search__suggestions--products {
  width: 100%;
}

.search__suggestions--title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 8px 0;
  margin-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;

  .text {
    font-family: "Helvetica Medium";
    font-size: 14px;
    color: #333;
    font-weight: 600;
  }
}

.search__suggestions--item {
  position: relative;
  padding: 12px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #f5f5f5;

  &:hover {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.searchItemContent {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}

.searchText {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: "Helvetica Medium";
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #333;

  .totalCount {
    color: #666;
    font-size: 0.9em;
    font-weight: normal;
  }
}

.recentSearchIcon,
.searchIcon {
  width: 16px;
  height: 16px;
  color: #666;
  flex-shrink: 0;
}

.clearAllButton {
  font-family: "Helvetica Medium";
  background: none;
  border: none;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    color: #333;
    text-decoration: underline;
  }

  .text {
    font-size: 12px;
  }
}

.deleteSearchButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  margin-left: auto;

  &:hover {
    opacity: 1;
  }

  .deleteIcon {
    width: 14px;
    height: 14px;
  }
}

.search__suggestions--button {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  button {
    width: 100%;
    padding: 12px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 8px;
    font-family: "Helvetica Medium";
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #e5e5e5;
    }
  }
}

.noResult {
  padding: 12px 0;
  color: #666;
  font-size: 14px;
  text-align: center;
  width: 100%;
  background: none;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #f9f9f9;
  }
}

.text {
  font-family: "Helvetica Medium";
  font-size: 14px;
  color: #333;
}

.flexAlignCenter {
  display: flex;
  align-items: center;
}

.fontBody {
  font-family: "Helvetica Medium";
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
