@import "../../../../styles/main.less";
.mobileNavigationWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 0px 0px var(--radius-main, 20px) var(--radius-main, 20px);
  border-bottom: 1px solid #e6e6e6;
  background: #fff;
  gap: 0.5rem;
}

/* Header Row */
.headerRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.25rem 1.25rem;
  width: 100%;
}

/* Icons */
.leftIcon {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 999px;
  border: 1px solid #e6e6e6;
  background: #fff;
}
.rightIcons {
  display: flex;
  justify-content: center;
  align-items: center;
}
.rightIcon1,
.rightIcon2 {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

/* Main Text */
.mainText {
  color: var(--Dark-80, #333);
  leading-trim: both;
  text-edge: cap;
  /* H5 */
  font-family: "Helvetica Bold";
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 23.4px */
}

/* Tabs Wrapper */
.tabsWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1000px;
  max-height: 40px;
  background-color: #ffffff;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

/* Category Row */
.categoryRow {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  justify-content: flex-start;
  align-items: center;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  max-width: 100%;
  white-space: nowrap;
  padding: 0 1.25rem;
  font-family: "Helvetica Medium";

  &::-webkit-scrollbar {
    display: none;
  }
}

/* Individual Tab */
.categoryItem {
  display: flex;
  padding: 10px;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
  border: none;
  background: none;
  cursor: pointer;

  .categoryItemText {
    font-size: clamp(14px, 0.94vw, 18px);
    color: var(--Dark-80, #333);
    font-family: "Helvetica Medium";
  }

  .line {
    height: 1px;
    width: 100%;
    background-color: @Dark-10;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  &.active {
    .line {
      background-color: #ff1e00;
      transform: scaleX(1);
    }
  }
}

/* Below tabs button container */
.tabsContainer {
  display: flex;
  width: 375px;
  padding: 1px var(--padding-main, 20px);
  align-items: flex-start;
}

/* Tab button */
.categoryTabs {
  display: flex;
  gap: 16px;
}

.categoryButton {
  display: flex;
  padding: 10px 10px 0px 10px;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
}

/* Tab label */
.categoryLabel {
  color: var(--Dark-80, #333);
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Now Display";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%;
  letter-spacing: 0.14px;
}

/* Animation */
@keyframes slideInBottom {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

// .line {
//   height: 4px;
//   width: 100%;
//   background-color: transparent;
//   border-top-left-radius: 35px;
//   border-top-right-radius: 35px;
//   animation: slideInBottom 0.3s ease-out;
//   &.active {
//     background-color: #ff1e00;
//     box-shadow: 0 1px 2px rgba(255, 30, 0, 0.2);
//   }
// }
