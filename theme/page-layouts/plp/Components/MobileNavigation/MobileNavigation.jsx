import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import styles from './MobileNavigation.less';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGlobalStore } from "fdk-core/utils";
import SvgWrapper from '../../../../components/core/svgWrapper/SvgWrapper';
import useHeader from '../../../../components/header/useHeader';
import { FDKLink } from "fdk-core/components";
import useTabs from '../../hooks/useTabs';
import useSearchModal from '../../../../helper/hooks/useSearchModal';
import SearchIcon from "../../../../assets/images/search.svg";
import CloseIcon from "../../../../assets/images/close.svg";
import { debounce } from "../../../../helper/utils";
import { SEARCH_PRODUCT, AUTOCOMPLETE } from "../../../../queries/headerQuery";


const MobileNavigation = ({ fpi }) => {
    const navigate = useNavigate();
    const location = useLocation();
    const [activeDisplay, setActiveDisplay] = useState('');
    const [activeTab, setActiveTab] = useState('');

    // Search modal state and functions
    const { openSearchModal, showMobileModal, closeSearchModal } = useSearchModal();

    // Search functionality state
    const [searchData, setSearchData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [searchText, setSearchText] = useState("");
    const [recentSearches, setRecentSearches] = useState([]);
    const [showSearchSuggestions, setShowSearchSuggestions] = useState(false);

    // Refs
    const mobileInputRef = useRef(null);

    // Constants
    const Suggested = ["Superdry Polo", "Leather Jacket", "Jeans"];
    const isAlgoliaEnabled = false; // You can get this from globalConfig if needed

    const { tabs, hasTabs, isActiveTab, mainCategory } = useTabs();

    // Load recent searches from localStorage on component mount
    useEffect(() => {
        const savedSearches = localStorage.getItem('recentSearches');
        if (savedSearches) {
            setRecentSearches(JSON.parse(savedSearches));
        }
    }, []);

    // Handle mobile modal focus
    useEffect(() => {
        if (showMobileModal && mobileInputRef.current) {
            setTimeout(() => {
                mobileInputRef.current.focus();
            }, 100);
        }
    }, [showMobileModal]);

    // Prevent body scroll when mobile modal is open
    useEffect(() => {
        if (showMobileModal) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [showMobileModal]);

    // Search functions
    const saveRecentSearch = (search) => {
        const updatedSearches = [search, ...recentSearches.filter(s => s !== search)].slice(0, 5);
        setRecentSearches(updatedSearches);
        localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
    };

    const closeSearch = () => {
        setSearchText("");
        setShowSearchSuggestions(false);
        closeSearchModal();
        if (mobileInputRef?.current) {
            mobileInputRef.current.value = "";
            mobileInputRef.current.blur();
        }
    };

    const getEnterSearchData = (searchText) => {
        setShowSearchSuggestions(false);

        if (isAlgoliaEnabled) {
            const BASE_URL = `${window.location.origin}/ext/algolia/application/api/v1.0/products`;
            const url = new URL(BASE_URL);
            url.searchParams.append("page_size", "4");
            url.searchParams.append("q", searchText);

            fetch(url)
                .then((response) => response.json())
                .then((data) => {
                    const productDataNormalization = data.items?.map((item) => ({
                        ...item,
                        media: item.medias,
                    }));
                    if (productDataNormalization.length) {
                        setSearchData(productDataNormalization);
                        setTotalCount(data.page?.item_total || 0);
                    } else {
                        setSearchData([]);
                        setTotalCount(0);
                    }
                })
                .finally(() => {
                    setShowSearchSuggestions(searchText?.length > 2);
                });
        } else {
            const payload = {
                pageNo: 1,
                search: searchText,
                filterQuery: "",
                enableFilter: false,
                sortOn: "",
                first: 8,
                after: "",
                pageType: "number",
            };
            fpi
                .executeGQL(SEARCH_PRODUCT, payload, { skipStoreUpdate: true })
                .then((res) => {
                    setSearchData(res?.data?.products?.items);
                    setTotalCount(res?.data?.products?.page?.item_total || 0);
                })
                .finally(() => {
                    setShowSearchSuggestions(searchText?.length > 2);
                });
        }
        fpi.executeGQL(AUTOCOMPLETE, { query: searchText });
    };

    const setEnterSearchData = debounce((e) => {
        setSearchText(e.target.value);
        getEnterSearchData(e.target.value);
    }, 400);

    const redirectToProduct = (link) => {
        if (searchText) {
            saveRecentSearch(searchText);
        }
        navigate(link);
        closeSearch();
    };

    const getProductSearchSuggestions = (results) => results?.slice(0, 4);

    const getDisplayData = (product) => {
        let displayName;

        if (product.name.length > 40) {
            displayName = `${product.name.substring(0, 40)}...`;
        } else {
            displayName = product.name;
        }

        return <div>{displayName}</div>;
    };

    const clearAllRecentSearches = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setRecentSearches([]);
        localStorage.removeItem('recentSearches');
        setShowSearchSuggestions(true);
        if (mobileInputRef.current) {
            mobileInputRef.current.focus();
        }
    };

    const deleteRecentSearch = (e, searchToDelete) => {
        e.preventDefault();
        e.stopPropagation();
        const updatedSearches = recentSearches.filter(search => search !== searchToDelete);
        setRecentSearches(updatedSearches);
        localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
        setShowSearchSuggestions(true);
        if (mobileInputRef.current) {
            mobileInputRef.current.focus();
        }
    };

    const renderSearchSuggestions = () => (
        <div className={styles["search__suggestions--products"]}>
            {!searchText && (
                <>
                    {recentSearches.length > 0 && (
                        <>
                            <div className={`b1 ${styles["search__suggestions--title"]} fontBody`}>
                                <p className={styles.text}>Recents</p>
                                <button
                                    type="button"
                                    className={`${styles.clearAllButton} btnLink`}
                                    onClick={clearAllRecentSearches}
                                >
                                    <p className={styles.text}>Clear All</p>
                                </button>
                            </div>
                            <ul>
                                {recentSearches.map((search, index) => (
                                    <li
                                        key={index}
                                        className={`${styles["search__suggestions--item"]} ${styles.flexAlignCenter}`}
                                    >
                                        <div
                                            className={styles.searchItemContent}
                                            onClick={() => redirectToProduct(`/products/?q=${search}`)}
                                        >
                                            <SearchIcon className={styles.recentSearchIcon} />
                                            <div className={`${styles.searchText} b1 ${styles.fontBody}`}>
                                                {search}
                                            </div>
                                        </div>
                                        <button
                                            type="button"
                                            className={styles.deleteSearchButton}
                                            onClick={(e) => deleteRecentSearch(e, search)}
                                            aria-label={`Delete ${search} from recent searches`}
                                        >
                                            <CloseIcon className={styles.deleteIcon} />
                                        </button>
                                    </li>
                                ))}
                            </ul>
                        </>
                    )}
                    <div className={`b1 ${styles["search__suggestions--title"]} fontBody`}>
                        <p className={styles.text}>Suggested</p>
                    </div>
                    <ul>
                        {Suggested.map((search, index) => (
                            <li
                                key={index}
                                className={`${styles["search__suggestions--item"]} ${styles.flexAlignCenter}`}
                            >
                                <div
                                    className={styles.searchItemContent}
                                    onClick={() => redirectToProduct(`/products/?q=${search}`)}
                                >
                                    <SearchIcon className={styles.recentSearchIcon} />
                                    <div className={`${styles.searchText} b1 ${styles.fontBody}`}>
                                        {search}
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                </>
            )}
            {searchText && (
                <>
                    <div
                        className={`b1 ${styles["search__suggestions--title"]} fontBody`}
                        style={{
                            display: searchData?.length > 0 ? "block" : "none",
                        }}
                    >
                        <p className={styles.text}>SEARCH RESULTS</p>
                    </div>
                    <ul
                        style={{
                            display: searchData?.length > 0 ? "block" : "none",
                        }}
                    >
                        {getProductSearchSuggestions(searchData)?.map(
                            (product, index) => (
                                <li
                                    key={index}
                                    className={`${styles["search__suggestions--item"]} ${styles.flexAlignCenter}`}
                                    onClick={() =>
                                        redirectToProduct(`/product/${product.slug}`)
                                    }
                                >
                                    <SearchIcon className={styles.searchIcon} />
                                    <div className={`${styles.searchText} b1 ${styles.fontBody}`}>
                                        {getDisplayData(product)} <span className={styles.totalCount}>({totalCount})</span>
                                    </div>
                                </li>
                            )
                        )}
                    </ul>
                    <ul
                        style={{
                            display: searchData?.length === 0 ? "block" : "none",
                        }}
                    >
                        <button
                            type="button"
                            onClick={() =>
                                redirectToProduct(`/products/?q=${searchText}`)
                            }
                        >
                            <li
                                className={`${styles.flexAlignCenter} ${styles.noResult} fontBody`}
                            >
                                <p className={styles.text}>No match found</p>
                            </li>
                        </button>
                    </ul>
                    <div
                        className={styles["search__suggestions--button"]}
                        style={{
                            display: totalCount > 4 ? "block" : "none",
                        }}
                    >
                        <button
                            type="button"
                            className="btnLink fontBody"
                            onClick={() =>
                                redirectToProduct(`/products/?q=${searchText}`)
                            }
                        >
                            <span><p className={styles.text}>SEE ALL {totalCount} PRODUCTS</p></span>
                        </button>
                    </div>
                </>
            )}
        </div>
    );

    // const handleTabClick = useCallback(
    //     (tab) => {
    //         console.log("tab action", tab.action?.page?.url)
    //         setActiveTab(tab.path);
    //         setActiveDisplay(tab.display);
    //         // Update only the query parameter without changing the main path
    //         // navigate(`/collection/${mainCategory}?subsection=${tab.path}`, { replace: true });
    //     },
    //     [navigate, mainCategory],
    // );
    const handleSearchClick = () => {
        openSearchModal();
    }

    const handleBackClick = () => {
        const currentPath = location.pathname;

        // If we're on a collection page (e.g., /collection/men), go back to collections
        if (currentPath.startsWith('/collection/')) {
            navigate('/collections');
        }
        // If we're on products page, go back to homepage
        else if (currentPath.startsWith('/products')) {
            navigate('/');
        }
        // Default fallback to collections
        else {
            navigate('/collections');
        }
    }

    return (
        <div className={styles.mobileNavigationWrapper}>
            <div className={styles.headerRow}>
                <div className={styles.leftIcon} onClick={handleBackClick}>
                    <SvgWrapper svgSrc={"arrow-left"} />
                </div>

                {/* <div className={styles.mainText}>{activeDisplay}</div> */}
                <div className={styles.mainText}>{mainCategory}</div>
                <div className={styles.rightIcons}>
                    <div className={styles.rightIcon1}>
                        <SvgWrapper svgSrc={"search"} onClick={() => handleSearchClick()} />
                    </div>
                    <div className={styles.rightIcon2} onClick={() => navigate("/cart")}>
                        <SvgWrapper svgSrc={"single-row-cart"} />
                    </div>
                </div>
            </div>

            {/* Tabs Section */}
            <div className={styles.tabsWrapper}>
                <div className={styles.categoryRow}>
                    {tabs.map((tab) => {

                        return (
                            <button
                                key={tab.path}
                                className={`${styles.categoryItem} ${isActiveTab(tab.path) ? styles.active : ''}`}
                            // onClick={() => handleTabClick(tab)}
                            >
                                <FDKLink to={tab.action?.page?.url} className={styles.categoryItemText}>{tab.display}</FDKLink>
                                <div className={`${styles.line} ${isActiveTab(tab.path) ? styles.active : ''}`} />
                            </button>
                        );
                    })}
                </div>
            </div>



            {showMobileModal && (
                <div className={styles.mobileSearchModal}>
                    <div className={styles.mobileSearchOverlay} onClick={closeSearch} />
                    <div className={styles.mobileSearchContent}>
                        {/* Modal Header with Search Bar */}
                        <div className={styles.mobileSearchHeader}>
                            <div className={styles.mobileSearchInputWrapper}>
                                <div className={`${styles.searchflex} ${styles.mobileSearchFlex}`}>
                                    <SearchIcon />
                                    <input
                                        ref={mobileInputRef}
                                        className={`${styles["search__input--text"]} ${styles["search__input--mobile-active"]}`}
                                        type="text"
                                        autoComplete="off"
                                        placeholder="Hi Sanjay! checkout 'winter jackets'"
                                        onChange={(e) => setEnterSearchData(e)}
                                        onKeyUp={(e) =>
                                            e.key === "Enter" &&
                                            e.target?.value &&
                                            redirectToProduct(`/products/?q=${e.target?.value}`)
                                        }
                                        aria-label="Mobile search input"
                                    />
                                    <CloseIcon
                                        className={`${styles["search--closeIcon"]} ${styles.headerIcon}`}
                                        onClick={closeSearch}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Modal Content */}
                        <div className={styles.mobileSearchBody}>
                            {renderSearchSuggestions()}
                        </div>
                    </div>
                </div>
            )}

        </div>
    );
};

export default MobileNavigation;

