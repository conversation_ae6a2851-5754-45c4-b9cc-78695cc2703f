import React, { useState, useEffect, useCallback, useMemo } from 'react';
import styles from './MobileNavigation.less';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGlobalStore } from "fdk-core/utils";
import SvgWrapper from '../../../../components/core/svgWrapper/SvgWrapper';
import useHeader from '../../../../components/header/useHeader';
import { FDKLink } from "fdk-core/components";
import useTabs from '../../hooks/useTabs';

const MobileNavigation = ({ fpi }) => {
    const navigate = useNavigate();
    const location = useLocation();
    const [activeDisplay, setActiveDisplay] = useState('');
    const [activeTab, setActiveTab] = useState('');

    const { tabs, hasTabs, isActiveTab, mainCategory } = useTabs();
    // const handleTabClick = useCallback(
    //     (tab) => {
    //         console.log("tab action", tab.action?.page?.url)
    //         setActiveTab(tab.path);
    //         setActiveDisplay(tab.display);
    //         // Update only the query parameter without changing the main path
    //         // navigate(`/collection/${mainCategory}?subsection=${tab.path}`, { replace: true });
    //     },
    //     [navigate, mainCategory],
    // );

    return (
        <div className={styles.mobileNavigationWrapper}>
            <div className={styles.headerRow}>
                <div className={styles.leftIcon} onClick={() => navigate("/")}>
                    <SvgWrapper svgSrc={"arrow-left"} />
                </div>

                {/* <div className={styles.mainText}>{activeDisplay}</div> */}
                <div className={styles.mainText}>{mainCategory}</div>
                <div className={styles.rightIcons}>
                    <div className={styles.rightIcon1}>
                        <SvgWrapper svgSrc={"search"} />
                    </div>
                    <div className={styles.rightIcon2} onClick={() => navigate("/cart")}>
                        <SvgWrapper svgSrc={"single-row-cart"} />
                    </div>
                </div>
            </div>

            {/* Tabs Section */}
            <div className={styles.tabsWrapper}>
                <div className={styles.categoryRow}>
                    {tabs.map((tab) => {

                        return (
                            <button
                                key={tab.path}
                                className={`${styles.categoryItem} ${isActiveTab(tab.path) ? styles.active : ''}`}
                            // onClick={() => handleTabClick(tab)}
                            >
                                <FDKLink to={tab.action?.page?.url} className={styles.categoryItemText}>{tab.display}</FDKLink>
                                <div className={`${styles.line} ${isActiveTab(tab.path) ? styles.active : ''}`} />
                            </button>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default MobileNavigation;

