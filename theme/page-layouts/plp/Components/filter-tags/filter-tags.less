@import "../../../../styles/main.less";

.filterTagsContainer {
  display: flex;
  // flex-wrap: wrap;
  gap: 10px;
  max-width: 100%;
  padding: 20px 20px 20px 60px;
  align-items: flex-start;
  align-self: stretch;
  @media (max-width: 768px) {
    padding: 0px;
  }

  // border-bottom: 1px solid var(--Dark-10, #e6e6e6);

  @media @desktop {
    flex-direction: column;
    align-items: unset;
    border-bottom: none;
    padding: 0px;
  }

  .filterTags {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    align-self: stretch;
    flex-wrap: wrap;

    @media @tablet {
      flex-wrap: unset;
      max-width: 100%;
      overflow-x: auto;
    }

    .filterTagItem {
      display: flex;
      // height: 26px;
      padding: clamp(6px, 0.42vw, 8px) clamp(6px, 0.52vw, 10px);
      justify-content: center;
      align-items: center;
      gap: 8px;
      border-radius: var(--radius-main, 20px);
      border: 1px solid @Dark-40;

      @media @tablet {
        padding: 4px 8px;
      }

      .tagTitle {
        color: @Dark-40;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 18.2px */
        letter-spacing: 0.28px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-family: "Helvetica Medium";

        @media @tablet {
          font-size: 14px;
          line-height: 18px;
        }
      }

      .removeTag {
        height: 10px;
        width: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #333333;
      }
    }
  }

  .actionButton {
    color: var(--Buttons-Primary, #4e3f09);
    font-size: 12px;
    font-weight: 500;
    line-height: normal;
    text-decoration-line: underline;
    text-transform: uppercase;
    white-space: nowrap;
    cursor: pointer;
    display: none;
    font-family: "Helvetica Medium";

    &.clearAll {
      @media @tablet {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px 20px;
        border-radius: 10px;
      }
    }

    &.viewAll {
      @media @desktop {
        display: flex;
        font-family: "Helvetica Medium";
      }
    }
  }
}
