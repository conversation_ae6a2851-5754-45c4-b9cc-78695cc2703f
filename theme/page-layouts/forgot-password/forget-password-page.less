@import '../../styles/main.less';

.forgotPasswordTitle {
	margin-bottom: 32px;
	color: @TextHeading;
	text-align: center;
	font-size: 32px;
	font-style: normal;
	font-weight: 700;
	line-height: 44.8px;

	@media @tablet {
		font-size: 24px;
		line-height: 33.6px;
	}

	&--mb-8 {
		margin-bottom: 8px;
	}
}
.forgotPasswordWrapper {
	width: 400px;
	padding: 24px;

	.forgotPasswordAlert {
		height: 48px;
		padding: 0 7px;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		background-color: @ErrorBackground;
		border: 1px dashed @ErrorText;
		color: @ErrorText;
		&.alert-success {
			background-color: #eefefa;
			border: 1px dashed @TextHeading;
			color: @TextHeading;
		}
		.alertMessage {
			display: inline-block;
			margin-right: 8px;
			font-size: 13px;
		}
		.alert-link {
			color: @ErrorText;
			font-size: 9px;
			cursor: pointer;
		}
	}
	.forgotPasswordInputGroup {
		margin-bottom: 24px;
		display: flex;
		flex-direction: column;

		.loginInputTitle {
			position: absolute;
			padding: 0 4px;
			margin: 0 12px;
			z-index: 1;
			transform: translateY(-50%);
			color: var(--textLabel);
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 16.8px;
			background: @PageBackground;
		}
		input {
			width: 100%;
			border: 1px solid @DividerStokes;
			padding: 16px;
			border-radius: 4px;
			height: 48px;
			font-size: 16px;
			box-sizing: border-box;
			background: @PageBackground;
			&:focus-visible {
				outline: none;
			}

			&::placeholder {
				color: @DividerStokes;
				opacity: 0.3;
			}
		}

		.emailErrorMessage {
			color: @ErrorText;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 16.8px;
			margin-top: 16px;
		}
	}
	.forgotPasswordSubmitBtn {
		border: none;
		margin-bottom: 24px;
		height: 48px;
		border-radius: 4px;
		background: @ButtonPrimary;
		color: @ButtonSecondary;
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 22.4px;
		text-transform: uppercase;
		width: 100%;
		&[disabled] {
			cursor: default;
		}
	}
	.loginLink {
		text-align: center;
		width: fit-content;
		display: block;
		cursor: pointer;
		margin: 0 auto;
		color: @TextSecondary;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: 19.6px;
		text-transform: uppercase;
		border: none;
		background: none;
		&:focus-visible {
			outline: none;
		}
	}
}
.submitWrapper {
	.submitSuccessMsg {
		color: @TextBody;
		text-align: center;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 19.6px;
	}

	.resendBtn {
		color: @ButtonLink;
		text-decoration: underline;
		display: block;
		text-align: center;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		letter-spacing: -0.28px;
		width: fit-content;
		margin: 0 auto;
		cursor: pointer;
		margin-top: 32px;
		border: none;
		background: none;
		&:focus-visible {
			outline: none;
		}
	}
}
.loading {
	div {
		width: 100%;
		height: 100%;
	}
}
