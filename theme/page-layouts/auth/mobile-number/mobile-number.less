@import "../../../styles/main.less";

.mobileInputWrapper {
  display: flex;
  flex-direction: column;
  .errorText {
    color: @ErrorText;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16.8px;
    margin-top: 5px;
  }
  &.errorInput {
    label {
      color: @ErrorText;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16.8px;
    }

    .mobileNumberInput {
      border: 1px solid @ErrorText;
    }
  }

  .mobileInputContainer {
    height: auto;

    .dialCodePreview {
      border-inline: none;
      padding-right: 4px;
    }

    .mobileNumberInput {
      width: 100%;
      font-weight: 400;
      display: flex;
      border-left: none;
      padding-left: 4px;

      &:focus-visible {
        outline: none;
      }

      &::placeholder {
        color: @TextLabel;
        opacity: 0.3;
      }
    }
  }
}

 .required {
   color: @ErrorText;
 }

.countryDropdown {
  background-color: aquamarine;
}

.inputTitle {
  position: absolute;
  background-color: @PageBackground;
  padding: 0 4px;
  margin: 0 12px;
  z-index: 1;
  transform: translateY(-50%);
  color: var(--textLabel);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16.8px;
}
