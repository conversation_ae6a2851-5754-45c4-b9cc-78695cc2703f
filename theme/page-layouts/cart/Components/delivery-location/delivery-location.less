@import "../../../../styles/main.less";

.cartPincodeContainer {
  border: 1px solid @DividerStokes;
  line-height: 140%;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  @media @mobile {
    padding: 16px;
    margin-top: 18px;
    border-width: 1px 0;
  }
  .pinCodeDetailsContainer {
    .pincodeHeading {
      font-size: 14px;
      font-weight: 400;
      color: @TextBody;
      font-family: "Helvetica Medium";
      @media @tablet {
        font-size: 12px;
      }
    }
    .pinCode {
      font-size: 14px;
      font-weight: 600;
      color: @TextHeading;
      font-family: "Helvetica Bold";
      @media @tablet {
        font-size: 12px;
      }
    }
  }
  .changePinCodeButton {
    font-size: 12px;
    font-weight: 600;
    padding: 12px 16px;
    cursor: pointer;
    border: 1px solid @ButtonPrimary;
    border-radius: @ButtonRadius;
    background: @ButtonSecondary;
    color: @ButtonPrimary;
    height: 100%;
    text-transform: uppercase;
    line-height: 140%;
    font-family: "Helvetica Bold";
    @media @mobile {
      padding: 9px;
    }
  }
}

.pincodeModal {
  width: 400px;

  @media @tablet {
    width: unset;
    border-top-left-radius: 4px !important;
    border-top-right-radius: 4px !important;
  }

  .modalHeader {
    padding: 16px;

    .modalHeading {
      color: @TextHeading;
      line-height: 140%;
      font-size: 16px;
      font-weight: 600;
      font-family: "Helvetica Bold" !important;
    }
    .modalCloseIcon {
      position: absolute;
      top: 24px;
      inset-inline-end: 24px;
      cursor: pointer;
      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .modalBody {
    padding: 24px 16px;

    .modalPincodeContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .errorText {
        flex-basis: 100%;
        color: @ErrorText;
        line-height: 140%;
        font-size: 12px;
        font-weight: 400;
        margin-top: 8px;
        font-family: "Helvetica Medium";
      }

      .modalPincodeInput {
        flex: 1;
        input {
          box-sizing: border-box;
          border: 1px solid var(--dividerStokes);
          border-radius: 4px;
          color: var(--textLabel);
          padding: 16px;
          font-size: 14px;
          font-weight: 400;
          width: 100%;
          font-family: "Helvetica Medium" !important;
          &:focus-visible {
            outline: none;
          }
        }
      }
      .modalChangePinCodeButton {
        font-size: 14px;
        font-weight: 600;
        padding: 14px 24px;
        margin-inline-start: 16px;
        background: @ButtonPrimary;
        border-radius: @ButtonRadius;
        color: @ButtonSecondary;
        height: 100%;
        text-transform: uppercase;
        cursor: pointer;
        text-align: center;
        font-family: "Helvetica Bold" !important;

        @media @tablet {
          order: 2;
          margin-top: 24px;
          width: 100%;
          margin-inline-start: 0;
          box-sizing: border-box;
        }
      }
    }
  }
}

.addressModal {
  .modalHeader {
    border-bottom: 1px solid @DividerStokes;
    position: relative;
    padding: 16px 24px;
    display: flex;
    align-items: center;

    .modalHeading {
      color: @TextHeading;
      line-height: 140%;
      font-size: 16px;
      font-weight: 600;
      font-family: "Helvetica Bold" !important;
    }
    .modalCloseIcon {
      position: absolute;
      top: 24px;
      inset-inline-end: 24px;
      cursor: pointer;
      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .modalBody {
    display: flex;
    flex-direction: column;
    padding: 24px;
    background: @DialogBackground;

    @media @mobile {
      padding: 0px;
    }

    input[type="radio"] {
      display: none;
      &:checked {
        color: @SuccessText;
      }
    }

    .pincodeBox {
      padding: 24px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      border-radius: 12px;
      background-color: @PageBackground;
      .errorText {
        color: @ErrorText;
        line-height: 140%;
        font-size: 12px;
        font-weight: 400;
        margin-top: 8px;
        flex-basis: 100%;
        font-family: "Helvetica Medium";
      }
      .modalPincodeInput {
        flex: 1;
        input {
          box-sizing: border-box;
          border: 1px solid var(--dividerStokes);
          border-radius: 4px;
          color: var(--textLabel);
          padding: 16px;
          font-size: 14px;
          font-weight: 400;
          width: 100%;
          font-family: "Helvetica Medium" !important;
          &:focus-visible {
            outline: none;
          }
        }
      }
      .modalChangePinCodeButton {
        font-size: 14px;
        font-weight: 600;
        padding: 14px 24px;
        margin-inline-start: 16px;
        background: @ButtonPrimary;
        border-radius: @ButtonRadius;
        color: @ButtonSecondary;
        height: 100%;
        text-transform: uppercase;
        text-align: center;
        box-sizing: border-box;
        font-family: "Helvetica Bold" !important;
      }
    }
  }
  .addressFormWrapper {
    @media @mobile {
      padding: 12px;
    }
  }
}

.addressItemContainer {
  @media @tablet {
    padding: 0;
    border: 1px solid var(--dividerStokes);
    border-width: 1px 0;
  }

  .customAddressItem {
    margin-bottom: 0;
    padding: 16px !important;
    border-bottom: none !important;

    @media @tablet {
      border-width: 0 0 1px !important;
    }
  }

  .customAddressItem:last-child {
    border-bottom: 1px solid @DividerStokes !important;

    @media @tablet {
      border-bottom: none !important;
    }
  }
}

.addAddress {
  margin-top: 32px;

  @media @tablet {
    margin-top: 24px;
    padding: 0 16px;
  }

  .addCta {
    .btnSecondary();
    padding: 20px 24px;
    width: 100%;
    text-transform: uppercase;
    font-family: "Helvetica Bold" !important;
  }
}

.stickyContainer {
  position: sticky;
  bottom: 0;
  background-color: @DialogBackground;
  border-top: 1px solid @DividerStokes;
  padding: 24px;
  margin-top: 32px;

  @media @tablet {
    padding: 16px;
    margin-top: 24px;
  }

  .selectCta {
    .btnPrimary();
    padding: 20px 24px;
    width: 100%;
    text-transform: uppercase;
    font-family: "Helvetica Bold" !important;
  }
}
.heading {
  margin: 32px 0 12px;
  font-size: 14px;
  font-weight: 600;
  line-height: 140%;
  font-family: "Helvetica Bold";
  @media @tablet {
    padding: 0 16px;
    margin-top: 24px;
  }
}
.addrErrText {
  margin-inline-start: 25px;
  margin-top: 8px;
  color: @ErrorText;
  font-weight: 400;
  font-size: 12px;
  margin-top: 8px;
  line-height: 140%;
  font-family: "Helvetica Medium";
}
