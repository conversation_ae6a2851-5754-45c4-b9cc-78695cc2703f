@import "../../../../styles/main.less";

.commentOuterBox {
  border: 1px solid @DividerStokes;
  box-sizing: border-box;
  line-height: 17px;
  display: flex;
  flex-direction: column;
  padding: 24px;

  @media @mobile {
    padding: 16px;
    border-width: 1px 0;
  }
  .addCommentHeader {
    line-height: 140%;
    color: @TextHeading;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    padding-bottom: 4px;
    font-family: "Helvetica Medium";
    @media @tablet {
      display: none;
    }
  }
  .commentBoxMobile {
    display: flex;
    gap: 14px;
    @media @desktop {
      display: none;
    }
    .commentIconMobile {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex: 0 0 16px;
    }
    .commentText {
      display: block;
      font-size: 12px;
      font-weight: 500;
      margin-inline-end: 8px;
      max-height: 38px;
      width: calc(100% - 40px);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      font-family: "Helvetica Medium";
    }
    .addCommentLabel {
      display: flex;
      flex-direction: column;
      gap: 4px;
      color: @TextLabel;
      font-size: 12px;
      font-weight: 400;
      font-family: "Helvetica Medium";
      .addCommentTitle {
        font-weight: 600;
        color: @TextHeading;
      }

      .body {
        color: @TextBody;
      }
    }
    .addBtn {
      margin-inline-start: auto;
      color: @ButtonPrimary;
      text-align: end;
      width: 40px;
      display: flex;
      justify-content: flex-end;
      font-weight: 600;
      font-size: 12px;
      white-space: nowrap;
      font-family: "Helvetica Bold";
    }
  }
  .inputBox {
    margin-top: 16px;
    display: flex;
    border: 1px dashed @DividerStokes;
    border-radius: 8px;
    flex-direction: column;
    background-color: @PageBackground;
    @media @tablet {
      display: none;
    }
    .commentBox {
      display: flex;
      align-items: center;
      position: relative;
      .commentNoteIcon {
        position: absolute;
        top: 50%;
        inset-inline-start: 10px;
        transform: translateY(-50%);
      }
      input {
        flex: 1;
        background: transparent;
        padding-top: 12px;
        padding-inline-end: 12px;
        padding-bottom: 12px;
        padding-inline-start: 40px;
        font-size: 12px;
        font-weight: 400;
        font-family: "Helvetica Medium";
        width: 100%;
        border-radius: 8px 0 0 8px;
        border: none;
        line-height: 140%;
        color: @TextLabel;
        &:focus-visible {
          outline: none;
        }
      }
      .commentLength {
        border-radius: 0 8px 8px 0;
        padding: 12px;
        color: @TextLabel;
        font-weight: 400;
        font-size: 10px;
        font-family: "Helvetica Medium";
      }
    }
    .commentError {
      font-weight: 400;
      font-size: 10px;
      color: @ErrorText;
      padding: 12px 12px 0px 12px;
      font-family: "Helvetica Medium";
    }
  }
}

.modelHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid @DividerStokes;
  background: @DialogBackground;
  padding: 16px;
  color: @TextHeading;
  line-height: 140%;
  font-size: 16px;
  font-weight: 600;
  font-family: "Helvetica Bold";
  @media @tablet {
    font-size: 14px;
  }
}
.modalContainer {
  @media @tablet {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
}
.modalContent {
  padding: 24px 16px;
  .modalTextarea {
    padding: 16px;
    font-size: 14px;
    font-weight: 400;
    font-family: "Helvetica Medium";
    width: 100%;
    min-height: 115px;
    border: 1px solid @DividerStokes;
    border-radius: 4px;
    line-height: 140%;
    color: @TextBody;
    box-sizing: border-box;
    resize: none;
    background-color: @PageBackground;
    vertical-align: middle;

    &:focus-visible {
      outline: none;
    }

    &::placeholder {
      color: @TextLabel;
    }
  }
  .modalErrorWrapper {
    display: flex;
  }
  .modalCommentError {
    font-weight: 400;
    font-size: 10px;
    color: var(--errorText);
    padding: 12px 12px 0;
    font-family: "Helvetica Medium";
  }
  .modalCommentLength {
    color: @TextLabel;
    font-weight: 400;
    width: max-content;
    margin-left: auto;
    font-size: 12px;
    line-height: 140%; /* 16.8px */
    margin-top: 8px;
    font-family: "Helvetica Medium";
  }
  .modalActionBtn {
    background: var(--buttonPrimary);
    border-radius: 4px;
    text-transform: uppercase;
    color: var(--buttonSecondary);
    width: 100%;
    border: none;
    height: 48px;
    line-height: 140%;
    margin-top: 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: "Helvetica Bold";
  }
}
