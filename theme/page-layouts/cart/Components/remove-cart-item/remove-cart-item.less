@import "../../../../styles/main.less";

.header {
  align-items: unset;
  padding: 24px;

  @media @tablet {
    padding: 16px;
  }
}

.subTitle {
  font-weight: 400;
}

.modalContainer {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.removeModalBody {
  line-height: 140%;
  padding: 24px;

  @media @tablet {
    padding: 16px;
  }

  .itemDetails {
    display: flex;
    gap: 16px;
    .itemImg {
      border-radius: 4px;
      flex: 0 0 18%;
      width: 18%;
      display: flex;

      @media @mobile {
        flex: 0 0 25%;
        width: 25%;
      }
      img {
        width: 100%;
        height: auto;
      }
    }

    .itemBrand {
      font-size: 14px;
      font-weight: 600;
      color: @TextHeading;
    }

    .itemName {
      font-size: 14px;
      font-weight: 400;
      color: @TextBody;
      margin-top: 4px;
    }
  }
}

.removeModalFooter {
  display: flex;
  padding: 24px;

  @media @tablet {
    padding: 16px;
  }
  .removeBtn {
    padding-top: 14px;
    padding-bottom: 14px;
    cursor: pointer;
    text-align: center;
    border-radius: @ButtonRadius;
    text-transform: capitalize;
    color: @ButtonPrimary;
    width: 50%;
    background-color: @ButtonSecondary;
    border: 1px solid @TextHeading;
    @media @tablet {
      width: 100%;
    }
  }
  .wishlistBtn {
    padding-top: 14px;
    padding-bottom: 14px;
    margin-left: 16px;
    text-align: center;
    cursor: pointer;
    color: @ButtonSecondary;
    border-radius: @ButtonRadius;
    width: 50%;
    text-transform: capitalize;
    background-color: @ButtonPrimary;

    @media @tablet {
      width: 100%;
    }
  }
}
