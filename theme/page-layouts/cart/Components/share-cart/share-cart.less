@import "../../../../styles/main.less";

.cartSharePopup {
  position: relative;
  .cartShare {
    .nccCartShare {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: @ButtonLink;
      font-family: "Helvetica Medium";
      .shareCartIconGreen {
        display: flex;
        align-items: center;

        svg {
          path {
            fill: @ButtonLink;
          }
        }
      }
      .shareBagBtn {
        font-size: 12px;
        font-weight: 600;
        margin-inline-start: 8px;
        font-family: "Helvetica Medium";
      }
    }
    .shareCartBox {
      border: 1px solid @DividerStokes;
      line-height: 140%;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      font-size: 12px;
      font-weight: 600;
      background: @PageBackground;
      font-family: "Helvetica Medium";

      @media @mobile {
        border-width: 1px 0;
      }

      .leftPart {
        color: @TextHeading;
        display: flex;
        font-family: "Helvetica Medium";

        .shareCartIcon {
          display: flex;
          align-items: center;
          margin-inline-end: 12px;
        }
      }
      .rightPart {
        color: @TextHeading;
        cursor: pointer;
        font-family: "Helvetica Medium";
      }
    }
  }
}
