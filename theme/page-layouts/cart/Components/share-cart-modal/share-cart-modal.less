@import "../../../../styles/main.less";

.sharePopup {
  position: absolute;
  inset-inline-end: 0;
  background-color: white;
  box-shadow: 0px 0px 3px 0px #00000042;
  bottom: 50px;
  width: 250px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: auto;
  font-weight: normal;
  text-transform: initial;
  z-index: 3;
  @media @tablet {
    position: fixed;
    top: 0;
    left: 0;
    justify-content: center;
    width: 100vw;
    height: 100dvh;
    z-index: 999;
  }

  .popupTitle {
    font-size: 12px;
    padding: 25px 10px 0;
    line-height: 15px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
    font-family: "Helvetica Medium" !important;
    @media @tablet {
      font-size: 14px;
      padding: 25px;
      line-height: 20px;
    }
  }
  .nccMb10 {
    margin-bottom: 10px;
    font-family: "Helvetica Medium" !important;
  }
  .qrCode {
    width: 250px;
  }
  .close {
    display: none;
    @media @tablet {
      position: absolute;
      top: 24px;
      right: 16px;
      width: 22px;
      height: 22px;
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
  }

  .icons {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    gap: 15px;
    .shareIcon {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 25px;
      height: 25px;
      @media @tablet {
        width: 35px;
        height: 35px;
      }
    }
  }
}
