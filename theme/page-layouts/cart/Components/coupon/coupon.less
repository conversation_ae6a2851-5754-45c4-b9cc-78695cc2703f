@import "../../../../styles/main.less";

.couponBoxContainer {
  padding: 24px;
  border: 1px solid @DividerStokes;
  @media @mobile {
    padding: 16px;
    border-width: 1px 0;
  }
  .couponBoxTitle {
    font-weight: 500;
    font-size: 12px;
    line-height: 140%;
    color: @TextHeading;
    font-family: "Helvetica Medium";
  }
  .couponApplyBox {
    display: flex;
    align-items: center;
    flex-direction: row;
    margin-top: 16px;
    padding: 12px;
    cursor: pointer;
    background: @SuccessBackground;
    border-radius: 8px;
    gap: 16px;
    cursor: pointer;
    .removeIcon,
    .arrowIcon {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-inline-start: auto;
      background: none;
      border: none;
      width: 2rem;
      height: 2rem;
      [dir="rtl"] & {
        transform: rotate(180deg);
      }
    }
  }
  .couponIcon {
    width: 40px;
  }
  .couponApplyTitle {
    min-width: 0;
    flex: 1;
    .applyTxt {
      line-height: 140%;
      color: @TextHeading;
      font-weight: 600;
      font-size: 14px;
      word-wrap: break-word;
      font-family: "Helvetica Medium";
    }
    .couponAppliedSubtitles {
      border-radius: 4px;
      color: @TextBody;
      margin-top: 4px;
      font-size: 12px;
      font-family: "Helvetica Medium";
    }
    .couponMetaDesc {
      color: @TextBody;
      line-height: 140%;
      font-size: 12px;
      font-weight: 500;
      margin-top: 4px;
      font-family: "Helvetica Bold";
    }
  }
}

.modalHeader {
  @media @mobile {
    line-height: 140%;
  }
}

.modalContent {
  .modalBody {
    display: flex;
    flex-direction: column;
    padding: 24px;
    @media @mobile {
      padding: 0;
      margin-top: 16px;
    }

    .cartErrorContainer {
      margin-bottom: 16px;
      padding-top: 12px;
      padding-bottom: 12px;
      padding-inline-start: 24px;
      display: flex;
      align-items: center;
      line-height: 140%;
      background-color: @ErrorBackground;
      border-radius: 8px;
      position: sticky;
      top: 0px;
      z-index: 4;
      @media @tablet {
        border-radius: 0;
      }
      .colorErrorNormal {
        font-size: 12px;
        font-weight: 600;
        font-family: "Helvetica Bold";
        margin-inline-start: 14px;
        color: @ErrorText;
      }
    }
    .couponInputBox {
      position: relative;
      border-radius: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      font-family: "Helvetica Medium" !important;

      @media @mobile {
        padding: 16px;
        border-radius: 0;
      }

      input {
        width: 100%;
        border: 1px solid @DividerStokes;
        border-radius: 4px;
        background: @DialogBackground;
        color: @TextBody;
        font-size: 12px;
        font-weight: 400;
        padding: 16px;
        line-height: 140%;
        font-family: "Helvetica Medium" !important;

        &:focus-visible {
          outline: none;
        }

        &::placeholder {
          color: @TextLabel;
        }
      }
      .checkBtn {
        // border: 1px solid @ButtonPrimary;
        // border-radius: @ButtonRadius;
        color: @ButtonPrimary;
        text-transform: uppercase;
        font-size: 14px;
        font-weight: 600;
        padding: 0px 13px;
        cursor: pointer;
        position: absolute;
        inset-inline-end: 5%;
        font-family: "Helvetica Bold" !important;
      }
      .checkBtn:disabled {
        opacity: 0.5;
      }
    }
    .couponListTitle {
      color: @TextLabel;
      line-height: 140%;
      margin-top: 32px;
      margin-bottom: 16px;
      font-size: 12px;
      font-weight: 500;
      font-family: "Helvetica Medium";
      @media @mobile {
        margin-top: 24px;
        margin-bottom: 12px;
        padding-inline-start: 16px;
      }
    }
    .couponList {
      display: flex;
      flex-direction: column;
      gap: 24px;

      @media @tablet {
        gap: 12px;
      }
    }
    .couponItem {
      border: 1px solid @DividerStokes;
      border-radius: 12px;
      padding: 24px;
      display: flex;
      @media @mobile {
        border-radius: 0;
      }
      &.opacity02 {
        opacity: 0.2;
      }
      .couponCode {
        background-color: @SuccessBackground;
        border: 1px solid @SuccessText;
        line-height: 140%;
        color: @SuccessText;
        position: relative;
        padding: 10px 15px;
        display: inline-block;
        font-size: 12px;
        font-weight: 600;
        font-family: "Helvetica Medium";
        &:before,
        &:after {
          content: "";
          position: absolute;
          top: 50%;
          inset-inline-start: 0;
          transform: translate(-50%, -50%) rotate(45deg);
          [dir="rtl"] & {
            transform: translate(50%, -50%) rotate(-45deg);
          }
          width: 16px;
          aspect-ratio: 1;
          background: @DialogBackground;
          border: 1px solid @SuccessText;
          border-radius: 50%;
          border-color: @SuccessText @SuccessText transparent transparent;
        }
        &:after {
          inset-inline-end: 0;
          inset-inline-start: auto;
          transform: translate(50%, -50%) rotate(-135deg);
          [dir="rtl"] & {
            transform: translate(-50%, -50%) rotate(135deg);
          }
        }
      }
      .couponTitle {
        line-height: 140%;
        color: @TextHeading;
        font-size: 14px;
        font-weight: 600;
        margin-top: 16px;
        font-family: "Helvetica Bold";
      }
      .couponMessage,
      .couponExpire {
        line-height: 140%;
        color: @TextBody;
        font-size: 12px;
        font-weight: 400;
        margin-top: 4px;
        font-family: "Helvetica Medium";
      }
      .couponApplyBtn {
        border: 1px solid @ButtonPrimary;
        border-radius: @ButtonRadius;
        color: @ButtonPrimary;
        height: 100%;
        text-transform: uppercase;
        background: @ButtonSecondary;
        display: inline-block;
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        margin-inline-start: auto;
      }
    }
    .noCouponsAvailable {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 0 16px;
      .iconContainer {
        padding: 24px 0px;
        svg {
          g > path {
            stroke: @ButtonPrimary;
          }
          g > path:nth-of-type(4) {
            fill: @ButtonPrimary;
          }
          g > path:nth-of-type(2) {
            fill: @ButtonPrimary;
          }
          g > path:nth-of-type(5) {
            fill: @PageBackground;
          }
          g > path:nth-of-type(6) {
            fill: @PageBackground;
          }
          g > path:nth-of-type(8) {
            fill: @ButtonPrimary;
          }
        }
      }
      .textContainer {
        display: flex;
        flex-direction: column;
        text-align: center;
        gap: 8px;
        h3 {
          color: @TextHeading;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 140%;
          font-family: "Helvetica Bold";
        }
        p {
          color: @TextLabel;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%;
          font-family: "Helvetica Medium";
        }
      }
    }
  }
}
.couponSuccessModalContainer {
  overflow: visible !important;
}
.couponSuccessModalContent {
  display: flex;
  align-items: center;
  justify-content: center;
  @media @desktop {
    min-width: 400px;
  }
  background-color: @DialogBackground;
  min-width: 300px;
  border: 1px solid @DividerStokes;
  border-radius: 8px;
  min-height: 275px;
  max-height: 720px;
  // overflow: hidden;
  // position: relative;
  .couponSuccessGif {
    position: absolute;
    height: 100%;
    width: 100%;
  }
  .couponSuccessIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0px;
    & > span {
      position: absolute;
    }
  }
  .modalBody {
    padding-top: 45px;
    // padding-bottom: 20px;
    text-align: center;
    .couponHeading {
      color: @TextLabel;
      line-height: 140%;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      font-family: "Helvetica Bold";
    }
    .couponValueSubheading {
      color: @TextBody;
      padding-top: 8px;
      font-weight: 600;
      color: @ButtonPrimary;
      font-family: "Helvetica Bold";
    }
    .couponValue {
      font-size: 42px;
      color: @ButtonPrimary;
      padding-top: 8px;
      font-weight: 700;
      font-family: "Helvetica Bold";
    }
    .bodyFooterBtn {
      color: @ButtonLink;
      background: @DialogBackground;
      text-transform: capitalize;
      border-top: 1px solid @DividerStokes;
      position: relative;
      padding-top: 20px;
      margin-top: 20px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      border: none;
      font-family: "Helvetica Bold";
    }
  }
}
