@import "../../../../styles/main.less";

.modal {
  position: fixed;
  inset: 0;
  z-index: @header;
  overflow-y: auto;
  overflow-x: hidden;
  .flex-center();
  @media @tablet {
    align-items: flex-end;
  }
  &:before {
    content: "";
    background-color: @Overlay;
    opacity: 0.6;
    inset: 0;
    position: fixed;
    z-index: -1;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

.modalContainer {
  min-width: 300px;
  background-color: @DialogBackground;
  border-radius: @border-radius;
  min-height: 100px;
  max-width: 720px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow:
    rgba(50, 50, 93, 0.25) 0px 50px 100px -20px,
    rgba(0, 0, 0, 0.3) 0px 30px 60px -30px;
  transform: translateZ(0);
  -webkit-transform: translateZ(0); /* Safari */
  -moz-transform: translateZ(0); /* Firefox */
  -ms-transform: translateZ(0); /* IE/Edge */
  -o-transform: translateZ(0); /* Opera */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  @media @tablet {
    width: 100%;
    max-width: unset;
  }
  &--borders {
    @media @tablet {
      border: none;
      border-radius: 0;
      border-start-start-radius: 8px;
      border-start-end-radius: 8px;
    }
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid @DividerStokes;
  background: @DialogBackground;
  z-index: 4;
  padding: 8px;

  .crossIcon {
    width: 21px;
    height: 21px;
    cursor: pointer;
  }
}

.modalBody {
  flex: 1;
  max-height: 90vh;
  overflow-y: auto;
}

.rightModal {
  justify-content: flex-end;
  .modalContainer {
    height: 100%;
    width: 40%;
    border-radius: 0;

    .modalHeader {
      padding: 24px;

      @media @tablet {
        padding: 16px;
      }

      .modalTitle {
        font-size: 16px;
        line-height: 18px;
        font-family: "Helvetica Bold" !important;
      }
    }

    @media @tablet {
      width: 100%;
      border-radius: 0;
    }
  }
}
.centerModal {
  @media @tablet {
    align-items: center;
  }
  .modalContainer {
    border-radius: 8px;
    @media @tablet {
      width: unset;
      max-width: 720px;
    }

    .modalHeader {
      border-radius: 8px 8px 0px 0px;
    }
  }
}

.modalTitle {
  font-weight: 600;
  color: @TextHeading;
  line-height: 140%;
  font-size: 16px;
  font-family: "Helvetica Bold" !important;
}
.modalSubTitle {
  line-height: 140%;
  font-size: 14px;
  margin-top: 8px;
  font-weight: 500;
  color: @TextBody;
  font-family: "Helvetica Medium";
}

.disableScroll {
  overflow-y: hidden;
}
