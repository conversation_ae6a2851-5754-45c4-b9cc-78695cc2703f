@import "../../../../styles/main.less";

.stickyFooter {
  display: none;
  position: sticky;
  position: -webkit-sticky;
  width: 100%;
  line-height: 140%;
  z-index: 1;
  bottom: 0;
  box-sizing: border-box;
  @media @tablet {
    display: block;
    margin-top: 12px;
  }
  .nccStickyBtn {
    display: flex;
    padding: 12px 16px;
    justify-content: space-between;
  }
  .billContainer {
    background-color: @PageBackground;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    .getTotalPrice {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      font-family: "Helvetica Bold";
      .nccPrice {
        font-size: 12px;
        font-weight: 500;
        font-family: "Helvetica Medium";
      }
      .nccTotalPrice {
        font-size: 14px;
        font-weight: 600;
        margin-inline-start: 4px;
        font-family: "Helvetica Bold";
      }
    }

    input[type="checkbox"] {
      accent-color: @ButtonPrimary;
      width: 20px;
      height: 20px;
      border-radius: 4px;
      padding: 0;
      margin: 0;
    }
    .rewardDiv {
      margin-inline-start: 12px;
      display: flex;
      align-items: center;
      .rewardPoi {
        font-size: 12px;
        font-weight: 500;
        display: flex;
        margin-inline-end: 12px;
        font-family: "Helvetica Medium";
      }
    }
  }
  .nccStickyBtn {
    display: flex;
    padding: 12px 16px;
    justify-content: space-between;
    align-items: flex-end;
  }
  .cartCheckoutBtn3 {
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: "Helvetica Bold";
    @media @tablet {
      margin-inline-start: 16px;
    }
  }

  .billContainer2 {
    justify-content: space-between;
    .getTotalPrice {
      margin-bottom: 0;
      display: flex;
      align-items: center;
    }
  }
  .viewPriceBtn {
    color: @ButtonLink;
  }
  .nccViewBtn {
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    font-family: "Helvetica Bold";
  }
  .viewPBtn {
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    width: 100%;
    font-family: "Helvetica Bold";
  }
  .stickyBtnContainer {
    background-color: @PageBackground;
    border: 1px solid @DividerStokes;
  }
  .stickyBtnContainer1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;

    @media @mobile {
      border-width: 1px 0;
    }
  }
  .cartCheckoutBtn3 {
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: "Helvetica Bold";
    @media @tablet {
      margin-inline-start: 16px;
    }
  }
  .secondaryCheckoutBtn {
    background: @ButtonPrimary;
    border-radius: @ButtonRadius;
    line-height: 140%;
    text-transform: uppercase;
    color: @ButtonSecondary;
    width: 100%;
    border: none;
    height: 48px;
    line-height: 140%;
    margin-top: 16px;
    cursor: pointer;
    font-size: 14;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: "Helvetica Bold";
    &.width40 {
      width: 40%;
    }
  }
  .checkoutButton,
  .secondaryCheckoutBtn {
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: "Helvetica Bold";
  }
  .checkoutButton {
    gap: 12px;
  }
  .cartCheckoutBtn {
    background: @ButtonPrimary;
    border-radius: @ButtonRadius;
    text-transform: uppercase;
    color: @ButtonSecondary;
    width: 100%;
    height: 48px;
    border: none;
    line-height: 140%;
    font-family: "Helvetica Bold" !important;
    &[disabled] {
      color: #898a93;
      cursor: default;
    }
  }
}

.priceContainerMobile {
  display: flex;
  flex-direction: column;
  width: 50%;
  @media @mobile {
    width: auto;
  }
  .totalPrice {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    font-family: "Helvetica Bold";
  }

  &CheckoutBtn {
    width: 100% !important;
    @media @mobile {
      width: 50% !important;
    }
  }
}
