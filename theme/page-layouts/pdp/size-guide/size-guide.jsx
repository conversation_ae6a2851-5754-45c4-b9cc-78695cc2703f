import React, { useState, useEffect, useMemo } from "react";
import { FDKLink } from "fdk-core/components";
import PropTypes from "prop-types";
import styles from "./size-guide.less";
import FyImage from "../../../components/core/fy-image/fy-image";
import FyHTMLRenderer from "../../../components/core/fy-html-renderer/fy-html-renderer";
import Modal from "../pdp-modal/pdp-modal"
import { useMobile } from "../../../helper/hooks";
import SvgWrapper from "../../../components/core/svgWrapper/SvgWrapper";

function SizeGuide({ isOpen, productMeta, onCloseDialog }) {
  const [previewSelectedMetric, setPreviewSelectedMetric] = useState("cm");
  const [selectedMetric, setSelectedMetric] = useState("cm");
  const [activeTab, setActiveTab] = useState("size_guide");
  const [touched, setTouched] = useState(false);
  const isMobile = useMobile();

  const values = {
    in: "inch",
    cm: "cm",
  };

  const headers = Object.entries(productMeta?.size_chart?.headers ?? {}).filter(
    ([key, val]) => !key?.includes("__") && val !== null
  );

  useEffect(() => {
    if (productMeta?.size_chart?.unit) {
      setPreviewSelectedMetric(productMeta?.size_chart.unit);
      setSelectedMetric(productMeta?.size_chart.unit);
    }
  }, [productMeta]);

  const changeSelectedMetric = (val) => {
    setPreviewSelectedMetric(val);

    if (selectedMetric === val) {
      setTouched(false);
    } else {
      setTouched(true);
    }
  };

  const isSizeChartAvailable = () => {
    const sizeChartHeader = productMeta?.size_chart?.headers || {};
    return Object.keys(sizeChartHeader).length > 0;
  };

  const convertMetrics = (val) => {
    if (previewSelectedMetric === "cm" && touched) {
      let finalVal = "";
      val = val.split("-");
      for (let i = 0; i < val.length; i += 1) {
        if (i !== 0 && i < val.length) {
          finalVal += "-";
        }
        if (!Number.isNaN(Number(val[i]))) {
          finalVal += (Number(val[i]) * 2.54).toFixed(1); // inches to cm
        } else {
          finalVal += val[i];
        }
      }
      return finalVal;
    }

    if (previewSelectedMetric === "in" && touched) {
      let finalVal = "";
      val = val.split("-");
      for (let i = 0; i < val.length; i += 1) {
        if (i !== 0 && i < val.length) {
          finalVal += "-";
        }
        if (!Number.isNaN(Number(val[i]))) {
          finalVal += (Number(val[i]) / 2.54).toFixed(1); // cm to inches
        } else {
          finalVal += val[i];
        }
      }
      return finalVal;
    }

    return val;
  };

  const displayStyle = useMemo(() => {
    let displayStyle = "none";
    if (activeTab === "measure") {
      displayStyle = productMeta.size_chart.image ? "block" : "flex";
    }
    return displayStyle;
  }, [activeTab]);

  return (
    <Modal
      // modalType={isMobile ? "right-modal" : "center-modal"}
      modalClassName={styles.sizeGuideModal}
      isOpen={isOpen}
      title=""
      closeDialog={(e) => onCloseDialog(e)}
      headerClassName={styles.sidebarHeader}
      bodyClassName={styles.sizeBodyContainer}
      containerClassName={styles.sizeGuideContainer}
      hideHeader={true}
    >
      <div className={styles.crossIcon}
        onClick={onCloseDialog}>
        <SvgWrapper svgSrc="cross-black" />
      </div>

      {/* Size Guide Dialog */}
      {/* Tabs */}
      {/* <div className={styles.sizeTabs}>
        {/* Size Guide Tab */}
      {/* {isSizeChartAvailable() && (
        <button
          type="button"
          className={`b2 ${styles.tab} ${styles.tabSizeGuide} ${activeTab === "size_guide" ? styles.active : ""
            }`}
          onClick={() => setActiveTab("size_guide")}
        >
          Size guide
        </button>
      )} */}

      {/* Measure Tab */}
      {/* {productMeta?.size_chart && (
        <button
          type="button"
          className={`b2 ${styles.tab} ${styles.tabMeasure} ${activeTab === "measure" ? styles.active : ""
            }`}
          onClick={() => setActiveTab("measure")}
        >
          How to measure
        </button>
      )} */}
      {/* </div> */}

      {/* Body */}
      <div className={styles.sidebarBody}>
        <div className={styles.sidebarHeaderContainer}>

          <div className={styles.stripContainer}>
            <div className={styles.strip}></div>
          </div>

          <div className={styles.headerContainer}>
            <div className={styles.majorTapIcon}>
              <SvgWrapper svgSrc="measure-tap" />
            </div>
            <div className={styles.headerTitle}>
              Size Guide
            </div>
          </div>

        </div>

        <div className={styles.sizeGuideContainer}>

          <div
            className={`${styles.upperContainer} ${!productMeta?.size_chart?.image ? styles.cstLw : ""
              }`}
            style={{ display: activeTab === "size_guide" ? "block" : "none" }}
          >
            {/* Size Description */}
            {/* {productMeta?.size_chart && productMeta?.size_chart?.description && (
            <div className={styles.sizeDesc}>
              <FyHTMLRenderer
                htmlContent={productMeta?.size_chart?.description}
              />
            </div>
          )} */}
            <div className={styles.sizeDesc}>

              Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
            </div>

            <div className={styles.sizeChartContainer}>

              {/* Button Group */}
              <div className={styles.btnGroup}>
                {/* <h4 className="h4 fontHeader" style={{ marginBottom: "16px" }}>
              {productMeta?.size_chart?.title}
            </h4> */}
                <div className={styles.btnContainer}>
                  {isSizeChartAvailable() &&
                    Object.entries(values)?.map(([key, val], index) => (
                      <button
                        key={key}
                        type="button"
                        onClick={() => {
                          changeSelectedMetric(key);
                        }}
                        className={`h5 ${styles.unitBtn} ${styles.fontBody} ${previewSelectedMetric === key
                          ? styles.unitBtnSelected
                          : ""
                          }`}
                      >
                        {val}
                      </button>
                    ))}
                </div>
              </div>

              <div className={styles.sizeInfo}>
                <table className={styles.sizeTable}>
                  <thead>
                    <tr>
                      {headers?.map(
                        ([key, val], index) =>
                          val !== null && (
                            <th
                              key={`column${key}`}
                              style={index % 2 !== 0 ? {

                                backgroundColor: "var(--DividerStokes, #F2F2F2)",
                              } : {
                                color: "var(--Dark-85, #262626)",

                              }}
                              className={`b2 ${styles.sizeHeader}`}
                            >
                              {val?.value}
                            </th>
                          )
                      )}
                    </tr>
                  </thead>

                  <tbody>
                    {productMeta?.size_chart?.sizes?.map((row, index) => (
                      <tr key={`row_${index}`} className={styles.sizeRow}>
                        {Object.entries(row)
                          .filter(
                            ([key, val], index) => !key?.includes("__") && val !== null
                          )
                          ?.map(([key, val], index2) => (
                            <td
                              key={`cell_${key}`}
                              className={`captionNormal ${styles.sizeCell}`}
                              style={index2 % 2 !== 0 ? {
                                backgroundColor: "var(--DividerStokes, #F2F2F2)",
                              } : {
                                color: "var(--Dark-85, #262626)",

                              }}
                            >
                              {headers[index2][1]?.convertable
                                ? convertMetrics(val)
                                : val}
                            </td>
                          ))}
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* make a table for mobile in which header field is shown as a column and all other data is shown as a row in specific attribute */}
                <table className={styles.sizeTableMobile}>
                  <tbody>
                    {Object.entries(productMeta?.size_chart?.headers || {})
                      .filter(([_, meta]) => meta !== null)
                      .map(([colKey, meta]) => (
                        <tr key={colKey}>
                          <td className={styles.labelCell}>{meta?.value}</td>
                          {productMeta?.size_chart?.sizes?.map((row, idx) => (
                            <td
                              key={`${colKey}_${idx}`}
                              className={styles.valueCell}
                              style={{
                                backgroundColor: (idx) % 2 === 0 ? "#e6e6e6" : "transparent",
                              }}
                            >
                              {meta?.convertable ? convertMetrics(row[colKey]) : row[colKey]}
                            </td>
                          ))}
                        </tr>
                      ))}
                  </tbody>
                </table>




              </div>

              {!isSizeChartAvailable() && (
                <div className={styles.notAvailable}>
                  <h3 className={styles.fontHeader}>
                    Not available, contact us for more information
                  </h3>
                  <FDKLink to="/contact-us" target="_blank">
                    <button
                      type="button"
                      className={`${styles.contactUs} btnPrimary ${styles.fontBody}`}
                    >
                      CONTACT US
                    </button>
                  </FDKLink>
                </div>
              )}
            </div>

          </div>

          {/* <div
          className={styles.rightContainer}
          style={{
            display: displayStyle,
          }}
        >
          {productMeta &&
            productMeta.size_chart &&
            productMeta.size_chart.image && (
              <div className={styles.sizeGuideImage}>
                <FyImage
                  src={productMeta.size_chart.image}
                  alt={productMeta.size_chart.title}
                  sources={[{ width: 500 }]}
                  aspectRatio={0.8}
                  mobileAspectRatio={0.8}
                />
              </div>
            )}

          
          {!productMeta ||
            !productMeta.size_chart ||
            (!productMeta.size_chart.image && (
              <div className={styles.notAvailable}>
                <h3 className={styles.fontHeader}>
                  Not available, contact us for more information
                </h3>
                <FDKLink to="/contact-us" target="_blank">
                  <button
                    type="button"
                    className={`${styles.contactUs} btnPrimary ${styles.fontBody}`}
                  >
                    CONTACT US
                  </button>
                </FDKLink>
              </div>
            ))}
        </div> */}

          <div className={styles.measureContainer}>
            <div className={styles.measureContent}>

              <h4 className={styles.measureTitle}>
                How to measure
              </h4>

              <p className={styles.measureDescText}>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
              </p>

            </div>

            <div className={styles.measureImage}>
              <FyImage
                src={productMeta.size_chart.image}
                alt={productMeta.size_chart.title}
                sources={[{ width: 500 }]}
                aspectRatio={0.8}
                mobileAspectRatio={0.8}
              />
            </div>
          </div>
        </div>

      </div>

    </Modal>
  );
}

SizeGuide.propTypes = {
  isOpen: PropTypes.bool,
  productMeta: PropTypes.shape({
    sizeChart: PropTypes.shape({
      unit: PropTypes.string,
    }),
  }),
  onCloseDialog: PropTypes.func,
};

export default SizeGuide;
