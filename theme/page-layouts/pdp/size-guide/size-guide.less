@import "../../../styles/main.less";

.sidebarHeader {
  background-color: #efe7d6;
}

.sizeGuideModal {
  display: flex;
  width: 1009px;
  height: 750px;
  align-items: flex-start;
  border-radius: 30px;
  background: #fff;
  position: relative;
  overflow: visible !important;
  margin-top: 30px;
}

.crossIcon {
  position: absolute;
  top: -10%;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 9999;
  background-color: @White;
  border-radius: 50%;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 40px;
  height: 40px;

  svg {
    width: 22px;
    height: 22px;
    text-align: center;
  }

  @media @tablet {
    display: none;
  }
}

.sizeGuideContainer {
  display: flex;
  width: 1009px;
  height: 750px;
  align-items: flex-start;
  background: #fff;
  position: relative;
  overflow: visible !important;
}
.sizeBodyContainer {
  box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  width: 100%;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;

  .sizeWrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  .sizeTabs {
    width: 100%;
    display: flex;
    background-color: @ThemeAccentL3;

    .active {
      color: @ButtonPrimary !important;
      border-bottom: 1px solid @ButtonPrimary;
    }

    .tab {
      padding: 0.75rem 0;
      cursor: pointer;
      color: @TextLabel;

      &.tabSizeGuide {
        margin-left: 1.5rem;

        @media @tablet {
          margin-left: 1rem;
        }
      }

      &.tabMeasure {
        margin-left: 1.5rem;
      }
    }
  }

  .sidebarBody {
    border-radius: 30px;
    display: flex;
    flex-direction: column;
    // width: 1009px;
    justify-content: center;
    align-items: center;
    position: relative;

    @media @tablet {
      // padding: 24px;
      border-top-left-radius: 30px;
      border-top-right-radius: 30px;
    }

    .sidebarHeaderContainer {
      position: sticky;
      top: 0;
      z-index: 10;
      width: 100%;
      background: #fff;
    }
    .stripContainer {
      display: flex;
      padding: 10px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px;
      align-self: stretch;
      background: #fff;

      .strip {
        width: 50px;
        height: 4px;
        border-radius: 50px;
        background: @Dark-10;
      }
      @media (min-width: 769px) {
        display: none;
      }
    }

    .headerContainer {
      display: flex;
      padding: 30px 40px;
      justify-content: center;
      align-items: center;
      align-self: stretch;
      gap: 18px;

      background: #fff;
      border-bottom: 1.744px solid @Dark-10;

      @media @tablet {
        padding: 0.625rem 2.5rem;
        border-bottom: 1.744px solid @Dark-10;
        background: #fff;
        gap: 1.125rem;
        justify-content: flex-start;
      }

      .headerTitle {
        color: @Dark;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold";
        font-size: 1.375rem;
        font-style: normal;
        font-weight: 700;
        line-height: 115%; /* 25.3px */

        @media @tablet {
          font-family: "Helvetica Bold";
          font-size: 1rem;
          line-height: 130%; /* 20.8px */
        }
      }

      .majorTapIcon {
        width: 32px;
        height: 32px;
      }
    }

    .sizeGuideContainer {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 64px;
      @media @tablet {
        gap: 40px;
      }

      .upperContainer {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 109px 0px 109px;
        @media @tablet {
          padding: 20px;
        }

        .sizeDesc {
          // width: 762px;
          color: @Dark-60;
          leading-trim: both;
          text-edge: cap;
          font-family: "Helvetica Medium";
          font-style: normal;
          font-weight: 500;
          line-height: 140%; /* 25.2px */
          letter-spacing: 0.18px;
          margin-bottom: 32px;
          @media @tablet {
            width: 316px;
            font-family: "Helvetica Medium";
            font-size: 0.875rem;
            font-style: normal;
            font-weight: 500;
            line-height: 130%; /* 18.2px */
            letter-spacing: 0.14px;
            margin-bottom: 20px;
          }
        }
        .sizeChartContainer {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 32px;
          align-self: stretch;
          @media @tablet {
            gap: 24px;
          }

          .btnGroup {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            width: 100%;
            @media @tablet {
              justify-content: flex-start;
            }

            .btnContainer {
              display: flex;
              height: fit-content;
              .column-gap(3px);
              border-radius: 20px;
              background: @Dark-5;
            }
            .unitBtn {
              display: flex;
              width: 64px;
              padding: 16px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              color: @Dark;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Medium" !important;
              font-size: 1.125rem;
              font-style: normal;
              font-weight: 500;
              line-height: 140%; /* 25.2px */
              letter-spacing: 0.18px;
              border-radius: 20px;
              background: @Dark-5;
              @media @tablet {
                font-size: 1rem;
                padding: 0.5rem 1rem;
              }

              &.unitBtnSelected {
                color: @White;
                leading-trim: both;
                text-edge: cap;
                font-family: "Helvetica Medium" !important;
                font-size: 1.125rem;
                font-style: normal;
                font-weight: 500;
                line-height: 140%; /* 22.4px */
                letter-spacing: 0.16px;
                border-radius: @RadiusMain;
                background: @Brand;
                @media @tablet {
                  font-size: 1rem;
                }
              }
            }
          }

          .sizeInfo {
            overflow-y: auto;
            overflow-x: auto;
            width: 100%;

            &::-webkit-scrollbar {
              width: 5px;
              height: 5px;
              background-color: #ffffff;
            }
            /* Track */
            &::-webkit-scrollbar-track {
              box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.4);
              background-color: #ffffff;
            }
            /* Handle */
            &::-webkit-scrollbar-thumb {
              border-radius: 2.5px;
              background-color: #6b6b6b;
            }

            @media @tablet {
              max-height: max-content;
              overflow-x: auto;
            }
            @media @mobile {
              max-height: max-content;
              overflow-x: auto;
            }
          }

          .sizeTable {
            width: 100%;
            padding: 20px;
            border: 1px solid @Dark-10;
            border-collapse: collapse;
            width: 100%;
            @media @tablet {
              display: none;
            }

            .sizeHeader {
              border: 1px solid @Dark-10;
              margin: 5px;
              text-transform: capitalize;
              padding: 1rem;
              vertical-align: middle;
              font-weight: 700;
              font-size: 1.125rem;
              font-family: "Helvetica Bold";
            }

            .sizeRow {
              .sizeCell {
                padding: 1rem;
                text-align: center;
                border: 1px solid @Dark-10;
                min-width: 100px;
                vertical-align: middle;
                color: @Dark-85;
                leading-trim: both;
                text-edge: cap;
                font-family: "Helvetica Medium";
                font-size: 1.125rem;
                font-style: normal;
                font-weight: 500;
                line-height: 140%; /* 25.2px */
                letter-spacing: 0.18px;
                width: 113px;
                height: 45px;

                @media @mobile {
                  min-width: 58px;
                }
              }
            }
          }
          .sizeTableMobile {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid var(--Dark-10, #e6e6e6);
            @media (min-width: 769px) {
              display: none;
            }

            th,
            td {
              border: 1px solid @Dark-10;
              padding: 1rem;
              font-size: 1rem;
              text-align: center;
              border: 1px solid var(--Dark-5, #f2f2f2);
              background: #fff;
              width: 80px;
            }

            thead {
              background-color: #fafafa;
              font-weight: 600;
              font-family: "Helvetica Bold";
            }

            .labelCell {
              color: @Dark-85;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Medium";
              font-size: 0.75rem;
              font-style: normal;
              font-weight: 500;
              line-height: 140%; /* 16.8px */
              letter-spacing: 0.12px;
            }

            .valueCell {
              color: @Dark-85;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Medium" !important;
              font-size: 0.75rem;
              font-style: normal;
              font-weight: 500;
              line-height: 140%; /* 16.8px */
              letter-spacing: 0.12px;
            }

            // @media @mobile {
            //   .labelCell {
            //     min-width: 90px;
            //   }

            //   .valueCell {
            //     white-space: nowrap;
            //   }

            //   thead {
            //     position: sticky;
            //     top: 0;
            //     z-index: 1;
            //   }

            //   .sizeTableMobile {
            //     overflow-x: auto;
            //     display: block;
            //   }
            // }
          }
        }
      }

      .measureContainer {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 32px;
        align-self: stretch;
        width: 100%;
        padding: 0px 109px 0px 109px;
        @media @tablet {
          padding: 0px 20px;
        }

        .measureContent {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 26px;
          align-self: stretch;
          @media @tablet {
            gap: 16px;
          }

          .measureTitle {
            color: @Dark;
            leading-trim: both;
            text-edge: cap;
            font-family: "Helvetica Bold";
            font-size: 1.375rem;
            font-style: normal;
            font-weight: 700;
            line-height: 115%; /* 25.3px */
            @media @tablet {
              align-self: stretch;
              font-size: 1rem;
              line-height: 130%; /* 20.8px */
            }
          }

          .measureDescText {
            color: @Dark-60;
            leading-trim: both;
            text-edge: cap;
            font-family: "Helvetica Medium";
            font-size: 1.125rem;
            font-style: normal;
            font-weight: 500;
            line-height: 140%; /* 25.2px */
            letter-spacing: 0.18px;
            @media @tablet {
              font-size: 0.875rem;
              line-height: 130%; /* 18.2px */
              letter-spacing: 0.14px;
            }
          }

          .measureImage {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }

  // .rightContainer {
  //   flex: 1;
  //   align-items: center;
  //   height: 100%;
  //   display: flex;

  //   .sizeguideImage {
  //     @media @tablet {
  //       width: 100%;
  //     }
  //   }
  // }
}

.sizeDesc {
  /deep/ .inline-html {
    p {
      font-size: 14px;
      line-height: 20px;
      color: @TextHeading;
    }
  }
}

//button css

.overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(20, 19, 14, 0.6);
  opacity: 0;
  transition: all 0.4s;
  z-index: 100;
  visibility: hidden;
}

.show {
  opacity: 0.5;
  visibility: visible;
}

.contactUs {
  margin: 24px 40px 0;
  width: calc(100% - 80px);
  padding: 16px 0;
  border-radius: 4px;
}

.notAvailable {
  text-align: center;
  width: 100%;

  @media @mobile {
    max-width: 415px;
  }

  &--label {
    font-size: 24px;
    line-height: 32px;
  }
}
