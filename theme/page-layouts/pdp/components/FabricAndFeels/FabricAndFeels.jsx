import React, { useState } from 'react'
import styles from './FabricAndFeels.less'
import SvgWrapper from '../../../../components/core/svgWrapper/SvgWrapper'
import fabricImage from '../../../../assets/images/Fabric.png'
import MaterialImage from '../../../../assets/images/Material.png'
import ProductServiceBadges from '../ProductServiceBadges/ProductServiceBadges'

const FabricAndFeels = ({ fabricAndMaterialData, howItFeelsData }) => {

    const customHowItFeels = [
        {
            title: 'light-weight',
            image: fabricImage,
            value: howItFeelsData.lightWeight || 8,
            slider: [
                {
                    title: 'Light',
                    value: 'light',
                },
                {
                    title: 'Heavy',
                    value: 'heavy'
                }
            ]
        },
        {
            title: 'fit',
            image: fabricImage,
            value: howItFeelsData.fit || 8,
            slider: [
                {
                    title: 'Tight',
                    value: 'tight',
                },
                {
                    title: 'Loose',
                    value: 'loose',
                }
            ]
        },
        {
            title: 'light-weight',
            image: fabricImage,
            value: 5,
            slider: [
                {
                    title: 'Tight',
                    value: 'tight',
                },
                {
                    title: 'Loose',
                    value: 'loose',
                }
            ]
        },
        {
            title: 'light-weight',
            image: fabricImage,
            value: 5,
            slider: [
                {
                    title: 'Tight',
                    value: 'tight',
                },
                {
                    title: 'Loose',
                    value: 'loose',
                }
            ]
        },
    ]
    const [showMore, setShowMore] = useState(false);

    return (
        <div className={styles.fabricAndFeelsContainer}>
            <div className={styles.howItFeelsMainContainer}>
                <h3 className={styles.howItFeelsMainContainerTitle}>How It Feels ?</h3>
                <div className={styles.howItFeelsContainerWrapper}>
                    {customHowItFeels.map((item, index) => {
                        const shouldShow = showMore || index < 3;
                        return (
                            <div
                                className={`${styles.howItFeelsContainer} ${shouldShow ? styles.expanded : styles.collapsed}`}
                                key={index}
                            >
                                <div className={styles.howItFeelsContainerImage}>
                                    <img src={item.image} alt="fabric" />
                                </div>
                                <div className={styles.howItFeelsContainerContent}>
                                    <div className={styles.howItFeelsContainerContentHeader}>
                                        <span className={styles.howItFeelsContainerTitle}>{item.title}</span>
                                        <span className={styles.howItFeelsContainerContentIcon}>i</span>
                                    </div>
                                    <div className={styles.howItFeelsContainerContentSlider}>
                                        <div className={styles.sliderTrack}>
                                            <div
                                                className={styles.sliderFill}
                                                style={{ width: `${(item.value / 10) * 100}%` }}
                                            ></div>
                                        </div>
                                        <SvgWrapper
                                            svgSrc="fabric-vector"
                                            className={styles.fabricVector}
                                            style={{ left: `${item.value * 10}%` }}
                                        />
                                        <div className={styles.howItFeelsContainerContentSliderItem}>
                                            <span className={styles.SliderText}>{item.slider[0].title}</span>
                                            <SvgWrapper svgSrc="line-arrow" className={styles.lineArrow} />
                                            <span className={styles.SliderText}>{item.slider[1].title}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
                <button className={styles.howItFeelsContainerContentButton} onClick={() => setShowMore(!showMore)}>
                    <span className={styles.howItFeelsContainerContentButtonText}>
                        {showMore ? 'show less' : 'show more'}
                    </span>
                </button>
            </div>

            <div className={styles.lineContainer}>
                <span className={styles.line}></span>
            </div>

            <div className={styles.fabricAndMaterialContainer}>
                <div className={styles.fabricAndMaterialHeader}>
                    <span className={styles.fabricAndMaterialHeaderTitle}>Fabricmaterial</span>
                    <span className={styles.fabricAndMaterialHeaderSubtitle}>What's this?</span>
                </div>
                <div className={styles.fabricAndMaterialImageContainer}>
                    <img src={MaterialImage} alt="material" className={styles.fabricAndMaterialImage} />
                </div>
                <div className={styles.fabricAndMaterialContent}>
                    <div className={styles.fabricAndMaterialContentItem}>
                        <span className={styles.fabricAndMaterialContentItemTitle}>
                            {fabricAndMaterialData.title}
                        </span>
                    </div>
                    <span className={styles.fabricAndMaterialContentDescription}>{fabricAndMaterialData.description}</span>
                </div>
            </div>

            <div className={styles.lineContainer}>
                <span className={`${styles.line} ${styles.lineBottom}`}></span>
            </div>

            <div className={styles.productServiceBadgesContainerWrapper}>
                <ProductServiceBadges />
            </div>
        </div>
    )
}

export default FabricAndFeels
