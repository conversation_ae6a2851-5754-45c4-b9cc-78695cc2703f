import React, { useState } from 'react'
import styles from './FabricAndFeels.less'
import SvgWrapper from '../../../../components/core/svgWrapper/SvgWrapper'
import fabricImage from '../../../../assets/images/Fabric.png'
import MaterialImage from '../../../../assets/images/Material.png'
import ProductServiceBadges from '../ProductServiceBadges/ProductServiceBadges'





const FabricAndFeels = ({ fabricAndMaterialData, howItFeelsData }) => {

    const customHowItFeels = [
        {
            title: 'light-weight',
            image: fabricImage,
            value: howItFeelsData.lightWeight || 8,
            slider: [
                {
                    title: 'Light',
                    value: 'light',
                },
                {
                    title: 'Heavy',
                    value: 'heavy'
                }
            ]
        },
        {
            title: 'fit',
            image: fabricImage,
            value: howItFeelsData.fit || 8,
            slider: [
                {
                    title: 'Tight',
                    value: 'tight',
                },
                {
                    title: 'Loose',
                    value: 'loose',
                }
            ]
        },
        {
            title: 'light-weight',
            image: fabricImage,
            value: howItFeelsData.lightWeight || 8,
            slider: [
                {
                    title: 'Tight',
                    value: 'tight',
                },
                {
                    title: 'Loose',
                    value: 'loose',
                }
            ]
        },
        {
            title: 'light-weight',
            image: fabricImage,
            // only one value in it which define where to end the black line
            // I want to make it dynamic like I provide data in which I give 1-10 scale based on that line will be filled
            value: howItFeelsData.lightWeight || 8,
            slider: [
                {
                    title: 'Tight',
                    value: 'tight',
                },
                {
                    title: 'Loose',
                    value: 'loose',
                }

            ]
        },

    ]
    const [showMore, setShowMore] = useState(false);

    return (
        <div className={styles.fabricAndFeelsContainer}>
            <div className={styles.howItFeelsMainContainer}>
                <h3 className={styles.howItFeelsMainContainerTitle}>How It Feels ?</h3>
                <div className={styles.howItFeelsContainerWrapper}>
                    {customHowItFeels.slice(0, showMore ? customHowItFeels.length : 3).map((item, index) => (
                        <div className={styles.howItFeelsContainer} key={index}>
                            <div className={styles.howItFeelsContainerImage}>
                                <img src={item.image} alt="fabric" />
                            </div>
                            <div className={styles.howItFeelsContainerContent}>
                                <div className={styles.howItFeelsContainerContentHeader}>
                                    <span className={styles.howItFeelsContainerTitle}>{item.title}</span>
                                    <span className={styles.howItFeelsContainerContentIcon}>i</span>
                                </div>
                                <div className={styles.howItFeelsContainerContentSlider}>
                                    <div className={styles.sliderTrack}>
                                        <div
                                            className={styles.sliderFill}
                                            style={{ width: `${(item.value / 10) * 100}%` }}
                                        ></div>

                                    </div>
                                    <SvgWrapper svgSrc="fabric-vector" className={styles.fabricVector} style={{ left: `${item.value * 10}%` }} />


                                    <div className={styles.howItFeelsContainerContentSliderItem}>
                                        <span className={styles.SliderText}>{item.slider[0].title}</span>
                                        <SvgWrapper svgSrc="line-arrow" className={styles.lineArrow} />
                                        <span className={styles.SliderText}>{item.slider[1].title}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
                <button className={styles.howItFeelsContainerContentButton} onClick={() => setShowMore(!showMore)}>
                    <span className={styles.howItFeelsContainerContentButtonText}>
                        {showMore ? 'show less' : 'show more'}
                    </span>
                </button>
            </div>
            <div className={styles.lineContainer}>
                <span className={styles.line}></span>
            </div>

            <div className={styles.fabricAndMaterialContainer}>
                <div className={styles.fabricAndMaterialHeader}>
                    <span className={styles.fabricAndMaterialHeaderTitle}>Fabricmaterial</span>
                    <span className={styles.fabricAndMaterialHeaderSubtitle}>What's this?</span>
                </div>
                <div className={styles.fabricAndMaterialImageContainer}>
                    <img src={MaterialImage} alt="material" className={styles.fabricAndMaterialImage} />
                </div>
                <div className={styles.fabricAndMaterialContent}>
                    <div className={styles.fabricAndMaterialContentItem}>

                        <span className={styles.fabricAndMaterialContentItemTitle}>
                            {fabricAndMaterialData.title}
                        </span>


                    </div>
                    <span className={styles.fabricAndMaterialContentDescription}>{fabricAndMaterialData.description}</span>
                </div>
            </div>
            {/* add margin-bottom : 8px */}
            <div className={styles.lineContainer}>
                <span className={`${styles.line} ${styles.lineBottom}`}></span>
            </div>

            {/* <div className={styles.line}></div> */}
            {/* do not showcase in mobile view or below 769px */}
            <div className={styles.productServiceBadgesContainerWrapper}>
                <ProductServiceBadges />
            </div>



        </div >
    )
}

export default FabricAndFeels
