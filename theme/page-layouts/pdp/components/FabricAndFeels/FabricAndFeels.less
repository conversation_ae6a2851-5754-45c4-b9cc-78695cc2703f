@import "../../../../styles/main.less";

.fabricAndFeelsContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .howItFeelsMainContainer {
    display: flex;
    flex-direction: column;
    gap: 20px;

    background-color: none;
    @media (min-width: 769px) {
      background-color: #fff;
      border-radius: 20px;
      padding: 20px;
    }
    @media (max-width: 768px) {
      padding: 40px 10px;
    }

    .howItFeelsMainContainerTitle {
      color: #000;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Bold";
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 20.8px */
    }
    .howItFeelsContainerWrapper {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }
    .howItFeelsContainer {
      display: flex;
      gap: 20px;
      justify-content: space-between;
      align-items: center;
      transition: all 0.3s ease-in-out;
      max-height: 200px;
      opacity: 1;
      overflow: hidden;

      &.collapsed {
        max-height: 0;
        opacity: 0;
        margin: 0;
        padding: 0;
        pointer-events: none;
      }

      &.expanded {
        max-height: 200px;
        opacity: 1;
      }

      .howItFeelsContainerImage {
        min-width: 70px;
        min-height: 70px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .howItFeelsContainerContent {
        padding: 0px 20px 0px 5px;
        gap: 10px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: 100%;

        .howItFeelsContainerContentHeader {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .howItFeelsContainerTitle {
            color: #000;
            leading-trim: both;
            text-edge: cap;
            -webkit-text-stroke-width: 0.2px;
            -webkit-text-stroke-color: #000;
            font-family: "Helvetica Medium";
            font-size: 0.875rem;
            font-style: normal;
            font-weight: 500;
            line-height: 130%; /* 18.2px */
            letter-spacing: 0.14px;
          }
          .howItFeelsContainerContentIcon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            border-radius: 50%;
            background-color: var(--Dark-10, #e6e6e6);
            border: 0.5px solid var(--White, #fff);
            display: flex;
            justify-content: center;
            align-items: center;
            color: @Dark-80;
            text-align: center;
            leading-trim: both;
            text-edge: cap;
            font-family: "Helvetica Medium";
            font-size: 0.75rem;
            font-style: normal;
            font-weight: 500;
            line-height: 130%; /* 15.6px */
            letter-spacing: 0.12px;
          }
        }

        .howItFeelsContainerContentSlider {
          display: flex;
          position: relative;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .sliderTrack {
            position: relative;
            width: 100%;
            height: 4px;
            background-color: @Dark-10;
            border-radius: 4px;
            overflow: hidden;
          }

          .sliderFill {
            position: absolute;
            height: 100%;
            top: 0;
            left: 0;
            background-color: @Dark;
          }
          .fabricVector {
            position: absolute;
            top: -5%;
            background-color: #000;
          }

          .howItFeelsContainerContentSliderItem {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-top: 4px;

            .SliderText {
              color: #000;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Medium";
              font-size: 0.75rem;
              font-style: normal;
              font-weight: 500;
              line-height: 130%; /* 15.6px */
              letter-spacing: 0.12px;
            }

            .lineArrow {
              width: 11px;
              height: 6px;
              fill: #000;
              opacity: 0.3;
            }
          }
        }
      }
    }
    .howItFeelsContainerContentButton {
      display: flex;
      padding: 14px;
      justify-content: center;
      align-items: center;
      gap: 11px;
      align-self: stretch;
      border-radius: @RadiusSmall;
      border: 1px solid @Dark-20;

      .howItFeelsContainerContentButtonText {
        color: @Dark;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold";
        font-size: 13px;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 16.9px */
      }
    }
  }
  .fabricAndMaterialContainer {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 30px;
    background-color: #fff;
    border-radius: 20px;
    @media @tablet {
      background: none;
      padding: 30px 10px;
    }

    .fabricAndMaterialHeader {
      display: flex;
      align-items: center;
      gap: 8px;
      justify-content: space-between;

      .fabricAndMaterialHeaderTitle {
        font-size: 16px;
        font-family: "Helvetica Bold";
        font-weight: 700;
        line-height: 130%;
        letter-spacing: 0.28px;
        color: #1a1a1a;
      }
      .fabricAndMaterialHeaderSubtitle {
        font-size: 12px;
        font-family: "Helvetica Medium";
        font-weight: 500;
        line-height: 130%;
        letter-spacing: 0.28px;
        color: #1a1a1a;
        text-decoration: underline;
      }
    }
    .fabricAndMaterialImage {
      img {
        width: 100%;
        height: 100%;
      }
    }
    .fabricAndMaterialContent {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 12px;

      .fabricAndMaterialContentItem {
        font-size: 12px;
        font-family: "Helvetica Medium";
        font-weight: 500;
        line-height: 130%;
        letter-spacing: 0.28px;
        color: #1a1a1a;
        display: flex;
        justify-content: center;
        align-items: center;

        .fabricAndMaterialContentItemTitle {
          font-size: 0.875rem;
          color: #000;
          leading-trim: both;
          text-edge: cap;
          font-family: "Helvetica Medium";
          font-size: 1rem;
          font-style: normal;
          font-weight: 700;
          line-height: 130%; /* 20.8px */
        }
      }
      .fabricAndMaterialContentDescription {
        color: @Dark-40;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Medium";
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 15.6px */
        letter-spacing: 0.12px;
      }
    }
  }

  .lineContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 10px;
  }
  .line {
    width: 100%;
    height: 1px;
    background-color: @Dark-10;
    &.lineBottom {
      margin-bottom: 8px;
    }
    @media (min-width: 769px) {
      display: none;
    }
  }
}

.productServiceBadgesContainerWrapper {
  display: none;
  @media (min-width: 769px) {
    display: block;
  }
}
