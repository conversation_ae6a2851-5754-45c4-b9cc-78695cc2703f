import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FabricAndFeels from './FabricAndFeels';

// Mock the CSS modules
jest.mock('./FabricAndFeels.less', () => ({
  fabricAndFeelsContainer: 'fabricAndFeelsContainer',
  howItFeelsMainContainer: 'howItFeelsMainContainer',
  howItFeelsMainContainerTitle: 'howItFeelsMainContainerTitle',
  howItFeelsContainerWrapper: 'howItFeelsContainerWrapper',
  transitioning: 'transitioning',
  howItFeelsContainer: 'howItFeelsContainer',
  hiddenItem: 'hiddenItem',
  visibleItem: 'visibleItem',
  howItFeelsContainerImage: 'howItFeelsContainerImage',
  howItFeelsContainerContent: 'howItFeelsContainerContent',
  howItFeelsContainerContentHeader: 'howItFeelsContainerContentHeader',
  howItFeelsContainerTitle: 'howItFeelsContainerTitle',
  howItFeelsContainerContentIcon: 'howItFeelsContainerContentIcon',
  howItFeelsContainerContentSlider: 'howItFeelsContainerContentSlider',
  sliderTrack: 'sliderTrack',
  sliderFill: 'sliderFill',
  fabricVector: 'fabricVector',
  howItFeelsContainerContentSliderItem: 'howItFeelsContainerContentSliderItem',
  SliderText: 'SliderText',
  lineArrow: 'lineArrow',
  howItFeelsContainerContentButton: 'howItFeelsContainerContentButton',
  howItFeelsContainerContentButtonText: 'howItFeelsContainerContentButtonText',
  fabricAndMaterialContainer: 'fabricAndMaterialContainer',
  fabricAndMaterialHeader: 'fabricAndMaterialHeader',
  fabricAndMaterialHeaderTitle: 'fabricAndMaterialHeaderTitle',
  fabricAndMaterialHeaderSubtitle: 'fabricAndMaterialHeaderSubtitle',
  fabricAndMaterialImageContainer: 'fabricAndMaterialImageContainer',
  fabricAndMaterialImage: 'fabricAndMaterialImage',
  fabricAndMaterialContent: 'fabricAndMaterialContent',
  fabricAndMaterialContentItem: 'fabricAndMaterialContentItem',
  fabricAndMaterialContentItemTitle: 'fabricAndMaterialContentItemTitle',
  fabricAndMaterialContentDescription: 'fabricAndMaterialContentDescription',
  lineContainer: 'lineContainer',
  line: 'line',
  lineBottom: 'lineBottom',
  productServiceBadgesContainerWrapper: 'productServiceBadgesContainerWrapper'
}));

// Mock SvgWrapper component
jest.mock('../../../../components/core/svgWrapper/SvgWrapper', () => {
  return function MockSvgWrapper({ svgSrc, className, style }) {
    return <div data-testid={`svg-${svgSrc}`} className={className} style={style} />;
  };
});

// Mock ProductServiceBadges component
jest.mock('../ProductServiceBadges/ProductServiceBadges', () => {
  return function MockProductServiceBadges() {
    return <div data-testid="product-service-badges" />;
  };
});

// Mock image imports
jest.mock('../../../../assets/images/Fabric.png', () => 'fabric-image-mock');
jest.mock('../../../../assets/images/Material.png', () => 'material-image-mock');

describe('FabricAndFeels Component', () => {
  const mockProps = {
    fabricAndMaterialData: {
      title: 'Cotton Blend',
      description: 'Soft and comfortable cotton blend fabric'
    },
    howItFeelsData: {
      lightWeight: 7,
      fit: 6
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders component with initial state showing only 3 items', () => {
    render(<FabricAndFeels {...mockProps} />);
    
    expect(screen.getByText('How It Feels ?')).toBeInTheDocument();
    expect(screen.getByText('show more')).toBeInTheDocument();
    
    // Should show 3 visible items and 1 hidden item
    const containers = document.querySelectorAll('.howItFeelsContainer');
    expect(containers).toHaveLength(4);
    
    // First 3 should have visibleItem class
    expect(containers[0]).toHaveClass('visibleItem');
    expect(containers[1]).toHaveClass('visibleItem');
    expect(containers[2]).toHaveClass('visibleItem');
    
    // 4th should have hiddenItem class
    expect(containers[3]).toHaveClass('hiddenItem');
  });

  test('toggles to show all items when "show more" is clicked', async () => {
    render(<FabricAndFeels {...mockProps} />);
    
    const showMoreButton = screen.getByText('show more');
    fireEvent.click(showMoreButton);
    
    // Button text should change
    await waitFor(() => {
      expect(screen.getByText('show less')).toBeInTheDocument();
    });
    
    // All items should now be visible
    const containers = document.querySelectorAll('.howItFeelsContainer');
    containers.forEach(container => {
      expect(container).toHaveClass('visibleItem');
      expect(container).not.toHaveClass('hiddenItem');
    });
  });

  test('toggles back to show only 3 items when "show less" is clicked', async () => {
    render(<FabricAndFeels {...mockProps} />);
    
    const button = screen.getByRole('button');
    
    // First click - show more
    fireEvent.click(button);
    await waitFor(() => {
      expect(screen.getByText('show less')).toBeInTheDocument();
    });
    
    // Second click - show less
    fireEvent.click(button);
    await waitFor(() => {
      expect(screen.getByText('show more')).toBeInTheDocument();
    });
    
    // Should be back to initial state
    const containers = document.querySelectorAll('.howItFeelsContainer');
    expect(containers[0]).toHaveClass('visibleItem');
    expect(containers[1]).toHaveClass('visibleItem');
    expect(containers[2]).toHaveClass('visibleItem');
    expect(containers[3]).toHaveClass('hiddenItem');
  });

  test('applies transitioning class during animation', async () => {
    render(<FabricAndFeels {...mockProps} />);
    
    const button = screen.getByRole('button');
    const wrapper = document.querySelector('.howItFeelsContainerWrapper');
    
    fireEvent.click(button);
    
    // Should have transitioning class immediately after click
    expect(wrapper).toHaveClass('transitioning');
    
    // Should remove transitioning class after timeout
    await waitFor(() => {
      expect(wrapper).not.toHaveClass('transitioning');
    }, { timeout: 400 }); // Wait a bit longer than the 300ms timeout
  });

  test('renders fabric and material section correctly', () => {
    render(<FabricAndFeels {...mockProps} />);
    
    expect(screen.getByText('Fabricmaterial')).toBeInTheDocument();
    expect(screen.getByText("What's this?")).toBeInTheDocument();
    expect(screen.getByText('Cotton Blend')).toBeInTheDocument();
    expect(screen.getByText('Soft and comfortable cotton blend fabric')).toBeInTheDocument();
  });
});
