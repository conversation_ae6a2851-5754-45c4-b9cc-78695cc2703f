@import "../../../../styles/main.less";

.productServiceBadgesContainerWrapper {
  display: flex;
  flex-direction: column;
  gap: 30px;

  .line {
    width: 100%;
    height: 1px;
    background-color: @Dark-10;
    display: none;
    @media @tablet {
      margin-bottom: 1.875rem;
      display: block;
    }
  }
  .productServiceBadgesContainer {
    display: flex;
    height: 114px;
    align-items: center;
    align-self: stretch;
    border-radius: var(--radius-small, 10px);
    background: rgba(255, 255, 255, 0.4);
    @media (min-width: 769px) {
      background: none;
    }
    @media (max-width: 1250px) {
      flex-wrap: wrap;
    }

    .productServiceBadge {
      display: flex;
      padding: 22px 28px;
      flex-direction: column;
      align-items: center;
      gap: 18px;
      flex: 1 0 0;
      @media (min-width: 1024px) {
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
        flex-direction: row;
      }
      @media (max-width: 1024px) {
      }
    }

    .productServiceBadgeIcon {
      display: flex;
      width: 24px;
      height: 24px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
    }

    .productServiceBadgeText {
      color: @Dark;
      text-align: center;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Medium" !important;
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 18.2px */
      letter-spacing: 0.14px;
    }
  }
}
