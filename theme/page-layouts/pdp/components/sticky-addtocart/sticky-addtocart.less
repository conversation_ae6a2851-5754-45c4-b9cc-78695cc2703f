@import "../../../../styles/main.less";

.stickyAddtocart {
  &--zIndex {
    z-index: @header;
  }
  display: none;
  padding: 20px 8px;
  width: 100%;
  position: relative;
  z-index: calc(@header - 1);

  @media @tablet {
    display: block;
    position: fixed;
    bottom: 0;
    z-index: 40;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  &:not(:first-child) {
    padding-left: 0;
  }
}
.IconsContainer {
  display: flex;
  width: 50px;
  height: 50px;
  // padding: 0.75rem 1.25rem;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 999px;
  border: 1px solid #e6e6e6;
  background: #fff;
  .wishListIcon,
  .shareIcon {
    display: flex;
    width: 20px;
    height: 20px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }
}

.addToCartButtonContainer {
  display: flex;
  padding: 20px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}
.addToCartText {
  color: @Dark-5;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Bold";
  font-size: 1rem;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 20.8px */
}
.customBody {
  padding: 0;
}

.addToCartModal {
  .customMHeader {
    padding: 24px 24px 10px;
  }
  .customMBody {
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 1000;
  }
  .addToCartModalContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
  }
  .stripContainer {
    display: flex;
    width: 100%;
    padding: 10px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .strip {
      width: 50px;
      height: 4px;
      border-radius: 50px;
      background: @Dark-10;
    }
  }

  .guideCta {
    display: flex;
    padding: 10px 20px 20px 20px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-bottom: 1px solid @Dark-10;
    .selectSizeTextContainer {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .selectSizeTextTitle {
      color: @Dark;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Bold";
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 20.8px */
    }
    .rulerIcon {
      display: flex;
      width: 18px;
      height: 18px;
      justify-content: center;
      align-items: center;
    }
  }

  .product__size {
    display: flex;
    height: 40px;
    padding: 8px 12px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 6.426px;
    flex: 1 0 0;
    border-radius: 8.032px;
    border: 0.803px solid @Dark-20;

    .sizeText {
      color: @Dark-40;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Medium";
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 500;
      line-height: 120%; /* 16.8px */
      letter-spacing: -0.14px;
    }

    &--selected {
      border-radius: 8.032px;
      border: 0.803px solid @Dark-20;
      background: @Brand;
      color: white;
      .sizeText {
        color: white;
      }
    }
    &--disabled {
      text-decoration-line: line-through;
      color: @TextDisabled;
    }
    &--guide {
      display: flex;
      align-items: center;
      gap: 5px;

      .sizeGuideText {
        color: @Dark;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Medium";
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 18.2px */
        letter-spacing: 0.14px;
        text-decoration-line: underline;
        text-decoration-style: solid;
        text-decoration-skip-ink: none;
        text-decoration-thickness: auto;
        text-underline-offset: auto;
        text-underline-position: from-font;
      }
    }
  }
  .sizes {
    width: 100%;
    overflow: scroll;
    padding: 20px;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      width: 0.5em;
    }

    ul {
      display: inline-flex;
      gap: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
    }
  }

  .qtyContainer {
    height: 100%;
    justify-content: space-between;
    border-radius: @ButtonRadius;
    padding: 12px;
  }

  .inputContainer {
    width: 100%;
    border: none;
  }
}

.stickyAddtocart .button,
.addToCartModal .button {
  cursor: pointer;
  background-color: black;
  display: flex;
  height: 50px;
  padding: 0.75rem 1.875rem;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex: 1 0 0;
  border-radius: 999px;
  background: @Dark;
  .addToCartText {
    color: var(--Dark-5, #f2f2f2);
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 1rem;
    font-style: normal;
    font-weight: 700;
    line-height: 130%; /* 20.8px */
  }
}

.stickyAddtocart .cartIcon,
.addToCartModal .cartIcon {
  height: 14px;
  width: 13px;
  margin-right: 5px;
  fill: @ButtonPrimary;
}

.productPrice {
  font-size: 1rem;
  margin: 8px 0 2px;
  display: flex;
  align-items: center;

  &--effective {
    line-height: 23px;

    @media @tablet {
      line-height: 19px;
    }
  }

  &--marked {
    margin-left: 0.25rem;
    color: @TextLabel;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: -0.02em;
    text-decoration-line: line-through;
  }

  &--discount {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background-color: @SaleBadgeBackground;
    color: @SaleDiscountText;
    font-size: 12px;
    margin-left: 1rem;
    display: inline-block;

    @media @tablet {
      margin-left: 0.5rem;
    }
  }
  .mrpLabel {
    margin-right: 4px;
    margin-left: 0.25rem;
    color: var(--textLabel, #7d7676);
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: -0.02em;
  }
}
.taxLabel {
  color: @TextDisabled;
  margin-top: 2px;

  @media @desktop {
    margin-bottom: 16px;
  }
}
