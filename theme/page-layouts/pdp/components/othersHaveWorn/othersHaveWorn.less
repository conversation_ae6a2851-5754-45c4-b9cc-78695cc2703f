@import "../../../../styles/main.less";

.othersHaveWornContainer {
  display: flex;
  flex-direction: column;
  gap: 60px;
  padding: 0 40px;
  margin-bottom: 100px;
  width: 100%;
  @media @tablet {
    padding: 1.25rem 1.25rem;
    gap: 1.25rem;
  }

  .othersHaveWornHeader {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .othersHaveWornTitle {
      color: @Dark;
      font-family: "Helvetica Bold";
      font-size: 3rem;
      font-weight: 700;
      line-height: 115%;
      @media @tablet {
        font-size: 1.125rem;
        line-height: 130%;
        color: #000;
      }
    }

    .othersHaveWornSubtitle {
      color: @Dark-40;
      font-family: "Helvetica Medium";
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 130%;
      letter-spacing: 0.18px;
      @media @tablet {
        font-size: 0.8rem;
        line-height: 150%; /* 19.5px */
        letter-spacing: 0.26px;
      }
    }
  }

  .othersHaveWornItemsMainContainer {
    width: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .othersHaveWornItemsContainer {
    display: flex;
    flex-direction: row;
    gap: 20px;
    width: max-content; // Important to enable horizontal scroll
  }

  .othersHaveWornItemContainer {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 8px;
    justify-content: center;
    align-items: flex-start;
  }

  .othersHaveWornItem {
    display: flex;
    // flex: 0 0 447px;
    height: fit-content;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 30.499px;
    position: relative;

    @media @tablet {
      flex: 0 0 233px;
    }

    img {
      width: clamp(216px, 23.28vw, 447px);
      //   height: clamp(284px, 30.63vw, 588px);
      object-fit: cover;
      border-radius: 30.499px;

      @media @tablet {
        height: 284px;
      }
    }
  }

  .othersHaveWornItemName {
    .othersHaveWornItemNameText {
      color: @Dark-80;
      font-family: "Helvetica Medium";
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 130%;

      @media @tablet {
        font-size: 0.75rem;
        letter-spacing: 0.12px;
      }
    }
  }
}
