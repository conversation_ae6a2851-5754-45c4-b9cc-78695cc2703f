import React from 'react'
import styles from './othersHaveWorn.less'
import Hoodie from '../../../../assets/images/hoodie.png'

const othersHaveWorn = () => {
    const othersHaveWornList = Array(10).fill({
        image: <PERSON><PERSON>,
        name: 'Others have worn',
        description: 'See what others have worn',
    });

    return (
        <div className={styles.othersHaveWornContainer}>
            <div className={styles.othersHaveWornHeader}>
                <div className={styles.othersHaveWornTitle}>
                    How others have worn
                </div>
                <div className={styles.othersHaveWornSubtitle}>
                    Tag @superdry on Instagram for a chance to be featured.
                </div>
            </div>

            <div className={styles.othersHaveWornItemsMainContainer}>
                <div className={styles.othersHaveWornItemsContainer}>
                    {othersHaveWornList.map((item, idx) => (
                        <div className={styles.othersHaveWornItemContainer} key={idx}>
                            <div className={styles.othersHaveWornItem}>
                                <img src={item.image} alt={item.name} />
                            </div>
                            <div className={styles.othersHaveWornItemName}>
                                <h1 className={styles.othersHaveWornItemNameText}>{item.name}</h1>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}

export default othersHaveWorn
