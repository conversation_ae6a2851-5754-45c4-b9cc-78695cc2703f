@import "../../../../styles/main.less";

.moreOffersContainer {
  // overflow: hidden !important;
  // position: fixed;
  // top: 0;
  // right: 0;
  // width: 540px;
  // height: 100%;
  // z-index: 101;
  // background: @DialogBackground;
  // box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, 0.2);
  // overflow-y: auto;
  // display: flex;
  // flex-direction: column;

  @media @tablet {
    width: 100%;
  }

  .sidebarHeader {
    background-color: #efe7d6;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;

    .title {
      text-transform: capitalize;
    }

    .closeIcon {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }

  .offerCard {
    padding: 16px;
    border-radius: 16px;
    background: @PageBackground;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .offerCardHead {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .offerCardCode {
      font-size: 20px;
      font-weight: 600;
      @media @tablet {
        font-size: 14px;
      }
      @media @mobile {
        font-size: 14px;
      }
    }

    .offerCardTitle {
      font-size: 14px;
      font-weight: 500;
      @media @mobile {
        font-size: 12px;
      }
    }
  }

  .sizeTabs {
    width: 100%;
    display: flex;
    background-color: @ThemeAccentL3;

    .active {
      color: @ButtonPrimary !important;
      border-bottom: 1px solid @ButtonPrimary;
    }

    .tab {
      padding: 0.75rem 0;
      cursor: pointer;
      color: @TextLabel;
      margin-left: 1.5rem;
    }
  }

  .sidebarBody {
    height: 100%;
    position: relative;
    padding: 32px 24px;
    overflow-y: auto;
    scrollbar-width: unset !important;

    @media @mobile {
      padding: 16px;
    }

    &::-webkit-scrollbar {
      width: 4px;
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 100px;
      background-color: @DividerStokes;
    }
  }
}

.overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(20, 19, 14, 0.6);
  opacity: 0;
  transition: all 0.4s;
  z-index: 100;
  visibility: hidden;

  &__show {
    opacity: 0.5;
    visibility: visible;
  }
}
