import React from "react";
import styles from "./offers.less";
import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper";

function Offers({
  couponsList = [],
  // promotionsList = [],
  setShowMoreOffers,
  setSidebarActiveTab,
}) {
  //   useEffect(() => {
  //     const fetchCoupons = async () => {
  //       try {
  //         const response = await apiSDK.cart.getCoupons({});
  //         setCouponsList(response?.available_coupon_list || []);
  //       } catch (ex) {
  //         console.error('Error while fetching coupons:', ex);
  //       }
  //     };

  //     const fetchPromotions = async () => {
  //       try {
  //         const response = await apiSDK.cart.getPromotionOffers({
  //           slug: context.product.slug,
  //         });
  //         setPromotionsList(response?.available_promotions || []);
  //       } catch (ex) {
  //         console.error('Error while fetching promotions:', ex);
  //       }
  //     };

  //     fetchCoupons();
  //     fetchPromotions();
  //   }, [apiSDK.cart, context.product.slug]);

  const openMoreOffersSidebar = (offerType) => {
    setSidebarActiveTab(offerType);
    setShowMoreOffers(true);
  };

  // Mock data for demonstration - remove when real data is available


  console.log("couponsList", couponsList);


  const copyToClipboard = (code) => {
    navigator.clipboard.writeText(code).then(() => {
      // You can add a toast notification here
      console.log('Code copied to clipboard:', code);
    });
  };

  return (
    <>
      <div className={styles.offersWrapper}>

        {/* {(couponsList?.length > 0 || promotionsList?.length > 0) && ( */}
        {/* {mockPromotionsData.length > 0 && (
              <div className={`${styles.offersDetailsBlock} ${styles.mt16}`}>
                <div className={styles.offerCardContent}>
                  <div className={styles.offerIcon}>
                    <span className={styles.percentageSymbol}>%</span>
                  </div>

                  <div className={styles.offerText}>
                    <div className={styles.offerTitle}>
                      Get Extra <span className={styles.highlight}>{mockPromotionsData[0].discount_percentage || "20%"} off</span>
                    </div>
                    {mockPromotionsData[0].offer_text && (
                      <div className={`${styles.b4} ${styles.offersDetailsBlockTitle}`}>
                        {mockPromotionsData[0].offer_text}
                      </div>
                    )}
                  </div>

                  <div className={styles.offerCode}>
                    {mockPromotionsData[0].promotion_name && (
                      <div className={styles.codeContainer}>
                        <span className={`${styles.sh4} ${styles.offersDetailsBlockCode}`}>
                          {mockPromotionsData[0].promotion_name}
                        </span>
                        <button
                          className={styles.copyButton}
                          onClick={() => copyToClipboard(mockPromotionsData[0].promotion_name)}
                          aria-label="Copy code"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M8 4v12a2 2 0 002 2h8a2 2 0 002-2V7.242a2 2 0 00-.602-1.43L16.083 2.57A2 2 0 0014.685 2H10a2 2 0 00-2 2z" stroke="currentColor" strokeWidth="2" fill="none" />
                            <path d="M16 18v2a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h2" stroke="currentColor" strokeWidth="2" fill="none" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <button
                  type="button"
                  className={`${styles.b5} ${styles.offersDetailsBlockViewAll}`}
                  onClick={() => openMoreOffersSidebar("promotions")}
                >
                  VIEW ALL
                </button>
              </div>
            )} */}

        {/* Original code structure - commented for reference */}


        <h3 className={styles.offersTitle}>Offers for you</h3>
        <div className={styles.offersContainer}>
          {couponsList?.length > 0 && couponsList?.map((coupon, index) => (

            <div key={index} className={styles.offersDetailsBlock}>

              <div className={styles.couponTitleContainer}>
                <SvgWrapper svgSrc="discount-shape-cart" className={styles.couponIcon} />
                <span className={styles.couponTitle}>{coupon.title}</span>
              </div>
              <div className={styles.dashedLineContainer}>
                <div className={`${styles.leftRound} ${styles.circleDot}`}></div>
                <div className={styles.dashedLine}></div>
                <div className={`${styles.rightRound} ${styles.circleDot}`}></div>
              </div>
              <div className={styles.discountDescriptionWrapper}>
                {coupon.description && (
                  <div
                    className={`${styles.b4} ${styles.discountDescription}`}
                  >
                    {coupon.description}
                  </div>
                )}
                {coupon.coupon_code && (
                  <div
                    className={`${styles.sh4} ${styles.discountCode}`}
                  >
                    <span className={styles.discountCodeText}>{coupon.coupon_code}</span>
                    <SvgWrapper svgSrc="copy" className={styles.copyIcon} onClick={() => copyToClipboard(coupon.coupon_code)} />
                  </div>
                )}
              </div>




            </div>
          ))}
        </div>



        {/* {promotionsList?.length > 0 && (
              <div className={`${styles.offersDetailsBlock} ${styles.mt16}`}>
                {promotionsList[0].promotion_name && (
                  <div
                    className={`${styles.sh4} ${styles.offersDetailsBlockCode}`}
                  >
                    {promotionsList[0].promotion_name}
                  </div>
                )}
                {promotionsList[0].offer_text && (
                  <div
                    className={`${styles.b4} ${styles.offersDetailsBlockTitle}`}
                  >
                    {promotionsList[0].offer_text}
                  </div>
                )}
                <button
                  type="button"
                  className={`${styles.b5} ${styles.offersDetailsBlockViewAll}`}
                  onClick={() => openMoreOffersSidebar("promotions")}
                >
                  VIEW ALL
                </button>
              </div>
            )} */}


      </div >
      <div className={styles.line}></div>
      {/* )} */}

    </>
  );
}

export default Offers;