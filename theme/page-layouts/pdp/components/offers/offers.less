@import "../../../../styles/main.less";

.line {
  width: 100%;
  height: 1px;
  background-color: @Dark-10;
  @media @tablet {
    margin-bottom: 1.875rem;
  }
}

.offersWrapper,
.offersSection,
.offersParent {
  overflow: hidden;
}

.offersWrapper {
  // margin-top: 24px;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  gap: 30px;

  @media @tablet {
    gap: 20px;
    margin-bottom: 30px;
  }

  .offersTitle {
    color: @Dark;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 700;
    line-height: 130%; /* 20.8px */ // 20.8px
    @media @tablet {
      font-size: 1rem;
    }
  }
  .offersContainer {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    flex-wrap: nowrap;
    max-width: 100%;
    width: 100%;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .offersDetailsBlock {
    display: flex;
    flex: 0 0 auto;
    // width: 335px;
    flex-direction: column;
    align-items: flex-start;
    // gap: 10px;
    border-radius: 8.324px;
    background: #d9d9d9;

    @media (min-width: 769px) and (max-width: 1250px) {
      width: 300px;
      height: auto;
    }
    @media (min-width: 1250px) {
      width: 396px;
      height: auto;
    }

    .couponTitleContainer {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
      margin: 10px 0px 2px 17px;

      .couponIcon {
        width: 32px;
        height: 34px;
        @media @tablet {
          width: 28px;
          height: 26px;
        }
      }
      .couponTitle {
        color: @Dark;
        font-family: "Helvetica Bold";
        font-size: 19.664px;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 25.563px */
        letter-spacing: 0.393px;
        @media @tablet {
          color: #000;
          font-family: "Helvetica Bold" !important;
          font-size: 11.654px;
          letter-spacing: 0.233px;
        }
      }
    }

    .dashedLineContainer {
      position: relative;
      display: flex;
      align-items: center;
      height: 14px;
      width: 100%;
      .dashedLine {
        // flex: 1;
        border-top: 1.5px dashed #b9b9b9;
        height: 0;
        // margin: 0 7px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .circleDot {
        width: 14px;
        height: 14px;
        background-color: #f2f2f2;
        border-radius: 50%;
        // position: absolute;
        bottom: 0;
      }

      .leftRound {
        margin-left: -7px;
        // border-radius: 50%;
        // border: 2px solid #b9b9b9;
      }

      .rightRound {
        margin-right: -7px;
        // border-radius: 50%;
        // border: 2px solid #b9b9b9;
      }
    }

    .discountTitleWrapper {
      display: inline-flex;
      align-items: center;
      gap: 10px;
      margin: 1rem 0 0.625rem 0.625rem;
    }

    .discountIcon {
      width: 28px;
      height: 26px;
    }

    .discountTitle {
      color: #000;
      font-family: "Helvetica Bold";
      font-size: 11.654px;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; // 15.15px
      letter-spacing: 0.233px;
    }

    .highlight {
      color: #ff1e00;
      font-family: "Helvetica Bold";
      font-size: 11.654px;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; // 15.15px
      letter-spacing: 0.233px;
    }

    .discountLineContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 1px;
      background: #d9d9d9;
      height: 14px;

      .line {
        width: 100%;
        height: 1px;
        background: @Dark-10;
        height: 0px;
        flex: 1 0 0;
        stroke-width: 1px;
        stroke: #b9b9b9;
      }
    }

    .discountDescriptionWrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 7px 18px;
      width: 100%;

      .discountDescription {
        color: @Dark;
        font-family: "Helvetica Medium";
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; // 13px
        letter-spacing: 0.2px;
        width: 157.326px;
        opacity: 0.5;

        @media (min-width: 769px) {
          font-size: 16px;
          line-height: 130%; /* 20.8px */
        }
      }

      .discountCode {
        display: inline-flex;
        height: 23px;
        padding: 10px;
        justify-content: center;
        align-items: center;
        gap: 6px;
        flex-shrink: 0;
        border-radius: 4px;
        background: rgba(255, 30, 0, 0.1);
        @media (min-width: 769px) {
          font-size: 16px;
          line-height: 130%; /* 20.8px */
          height: auto;
        }

        .copyIcon {
          display: flex;
          width: 12px;
          height: 12px;
          justify-content: center;
          align-items: center;
        }

        .discountCodeText {
          color: #ff1e00;
          font-family: "Helvetica Bold";
          font-size: 10px;
          font-style: normal;
          font-weight: 700;
          line-height: 130%; // 13px
          letter-spacing: 0.1px;
          @media (min-width: 769px) {
            font-size: 16px;
            line-height: 130%; /* 20.8px */
          }
        }
      }
    }
  }
}

// Commented unused/legacy styles below
/*
.mt16 {
  @media @mobile {
    margin-top: 16px;
  }
}

.b4 {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  color: @TextBody;
  letter-spacing: -0.28px;
}

.b5 {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.24px;
  color: @ButtonLink;
}

.sh4 {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: @TextHeading;
}

.offersDetailsBlockCode {
  margin-bottom: 8px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.offersDetailsBlockTitle {
  margin-bottom: 8px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.offersDetailsBlockViewAll {
  border-bottom: 1px solid @ButtonLink;
  cursor: pointer;
  padding-bottom: 2px;
}
*/
