@import "../.././../../styles/main.less";

.productDetailsPage {
  max-width: @page-width;
}

.wishlist {
  position: absolute;
  top: 25px;
  right: 24px;
  cursor: pointer;
  width: 25px;
  height: 23px;
  z-index: 2;

  .wishlistIcon {
    &:hover {
      /deep/ svg path {
        fill: @TextHeading;
        fill-opacity: 1;
      }
    }
  }

  .active {
    /deep/ svg path {
      fill: @TextHeading;
      fill-opacity: 1;
    }
  }
}

.badge {
  position: absolute;
  text-align: center;
  bottom: 8px !important;
  left: 8px !important;
  top: unset !important;
  padding: 4px var(--scale-8-px, 8px);
  height: fit-content;
  width: fit-content;
}

.b4 {
  bottom: 8px !important;
  left: 8px !important;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.28px;
  border-radius: 24px;
  background-color: @PageBackground;
  color: @TextBody;
  border: 1px solid @DividerStokes;
}

.galleryBox {
  .imageBox {
    position: relative;
    width: 100%;
    @media (max-width: 1300px) and (min-width: 769px) {
    }
  }

  .imageItem {
    overflow: hidden;
  }

  .loader {
    min-height: 100%;
  }

  .flexAlign {
    display: flex;
  }

  .mouseCover {
    position: fixed;
    width: 100px;
    height: 100px;
    background-color: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    pointer-events: none;
    display: none;
  }

  .imageGallery {
    display: flex;
    flex-direction: row;
    gap: 2.5rem;
    height: 100%;
    position: relative;

    @media @tablet {
      display: none;
    }

    img {
      z-index: unset;
    }

    &__main {
      max-width: 100%;
      cursor: pointer;
    }

    .thumbSlider {
      position: relative;
      display: flex;
      justify-content: center;
      height: clamp(480px, 46.25vw, 888px);
    }

    .thumbWrapper {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .btnNavGallery {
      background-color: #fff;
      cursor: pointer;
      display: flex;
      width: 40px;
      height: 40px;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      border: 1px solid @DividerStokes;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;
      position: absolute;
      left: 50%;
      z-index: 10;

      &:hover {
        background-color: @ButtonPrimary;
        color: white;
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .prevBtn {
      top: -20px;
      transform: translate(-50%, 0);

      &:hover {
        transform: translate(-50%, 0) scale(1.05);
      }

      &:active {
        transform: translate(-50%, 0) scale(0.95);
      }
    }

    .nextBtn {
      bottom: -20px;
      transform: translate(-50%, 0);

      &:hover {
        transform: translate(-50%, 0) scale(1.05);
      }

      &:active {
        transform: translate(-50%, 0) scale(0.95);
      }
    }
  }

  .thumbnailList {
    display: flex;
    flex-direction: column;
    gap: 24px;
    height: 100%;
    overflow-y: auto;
    flex: 1;
    margin: 0 0 30px 0;

    /* Custom scrollbar styling */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: @DividerStokes;
      border-radius: 2px;
      transition: background 0.2s ease;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: @TextHeading;
    }

    /* Hide scrollbar for Firefox */
    scrollbar-width: thin;
    scrollbar-color: @DividerStokes transparent;

    .thumbnail {
      border-radius: @ImageRadius;
      // height: 200px;
      // width: 150px;
      height: fit-content;
      width: 100%;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      flex-shrink: 0; // Prevent thumbnails from shrinking
      border: 2px solid transparent;
      transition:
        border-color 0.2s ease,
        transform 0.2s ease;

      // &:hover {
      //   border-color: @ButtonPrimary;
      //   transform: scale(1.02);
      // }

      // &.active {
      //   border-color: @ButtonPrimary;
      //   box-shadow: 0 0 0 1px @ButtonPrimary;
      // }
    }

    &--item {
      width: clamp(100px, 7.81vw, 150px);
      height: auto;
      object-fit: cover;
    }

    .videoPlayIcon,
    .modelIcon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: var(--icon-color, white);
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      padding: 8px;
    }

    .modelIcon {
      width: 24px;
      height: 24px;
    }

    .videoPlayIcon {
      width: 28px;
      height: 28px;
    }
  }

  // Hide scrollbar completely when needed
  .scrollbarHidden {
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.mobile {
  display: none;

  @media @tablet {
    display: block;
  }
}

.disableArrow {
  cursor: default;
  pointer-events: none;
  opacity: 0.5;
}

.navArrowIcon {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease;
}

.carouselArrow {
  width: 48px;
  height: 48px;
  cursor: pointer;

  rect:nth-of-type(1) {
    fill: @PageBackground !important;
  }

  path {
    fill: none;
    stroke: @TextHeading;
  }

  &--left {
    transform: rotate(180deg);
  }
}

.removeWidth {
  width: unset !important;
}

.flexAlignCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
