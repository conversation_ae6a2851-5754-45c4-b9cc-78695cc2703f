// Updated React Component with Enhanced Scroll Functionality

import React, { useState, useEffect, useRef, Suspense } from "react";
import <PERSON><PERSON><PERSON>oom from "../pic-zoom/pic-zoom";
import FyImage from "../../../../components/core/fy-image/fy-image";
import { getProductImgAspectRatio } from "../../../../helper/utils";
import styles from "./image-gallery.less";
import MobileSlider from "../mobile-slider/mobile-slider";
import VideoPlayIcon from "../../../../assets/images/video-play.svg";
import ThreeDIcon from "../../../../assets/images/3D.svg";
import CarouselNavArrowIcon from "../../../../assets/images/carousel-nav-arrow.svg";
import ArrowLeftIcon from "../../../../assets/images/arrow-left.svg";
import ArrowRightIcon from "../../../../assets/images/arrow-right.svg";
import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper"

const LightboxImage = React.lazy(
  () => import("../lightbox-image/lightbox-image")
);

function PdpImageGallery({
  images,
  displayThumbnail = true,
  isCustomOrder = false,
  iconColor = "",
  globalConfig = {},
  followed,
  removeFromWishlist,
  addToWishList,
  hiddenDots = false,
  slideTabCentreNone = false,
  hideImagePreview = false,
  handleShare,
  showShareIcon = true,
  imgSources = [],
}) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [enableLightBox, setEnableLightBox] = useState(false);
  const [resumeVideo, setResumeVideo] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);
  const [canScrollUp, setCanScrollUp] = useState(false);

  const itemWrapperRef = useRef(null);

  const currentMedia = {
    src: images?.[currentImageIndex]?.url || "",
    type: images?.[currentImageIndex]?.type || "",
    alt: images?.[currentImageIndex]?.alt || "",
  };

  // Check scroll availability
  const checkScrollAvailability = () => {
    const container = itemWrapperRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    
    // Add a small buffer (1px) to account for rounding errors
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;
    const isAtTop = scrollTop <= 0;

    // Only show scroll buttons if there are enough images to scroll
    const hasEnoughImages = images?.length > 3;
    
    setCanScrollUp(!isAtTop && hasEnoughImages);
    setCanScrollDown(!isAtBottom && hasEnoughImages);
  };

  useEffect(() => {
    if (typeof document !== "undefined") {
      const classList = document.body?.classList;

      if (enableLightBox && classList) {
        classList.add("remove-scroll");
      } else {
        classList.remove("remove-scroll");
      }
    }
  }, [enableLightBox]);

  useEffect(() => {
    setCurrentImageIndex(0);
    // Reset scroll position when images change
    if (itemWrapperRef.current) {
      itemWrapperRef.current.scrollTop = 0;
    }
  }, [images]);

  useEffect(() => {
    // Check scroll availability on mount and when images change
    const timer = setTimeout(() => {
      checkScrollAvailability();
    }, 100); // Small delay to ensure DOM is rendered

    // Also check after a longer delay to handle any dynamic content loading
    const timer2 = setTimeout(() => {
      checkScrollAvailability();
    }, 500);

    return () => {
      clearTimeout(timer);
      clearTimeout(timer2);
    };
  }, [images]);

  const setMainImage = (e, index) => {
    e.preventDefault();
    if (index >= 0) {
      setCurrentImageIndex(index);
    }
  };

  const getImageURL = (srcUrl) =>
    `http://img.youtube.com/vi/${srcUrl?.substr(srcUrl?.lastIndexOf("/") + 1)}/0.jpg`;

  const prevSlide = () => {
    if (currentImageIndex === 0) {
      return;
    }
    if (!hiddenDots) {
      itemWrapperRef.current.scrollLeft -= 75;
    }
    setCurrentImageIndex((prevIndex) => prevIndex - 1);
  };

  const nextSlide = () => {
    if (currentImageIndex === images.length - 1) {
      return;
    }
    if (!hiddenDots) {
      itemWrapperRef.current.scrollLeft += 75;
    }
    setCurrentImageIndex((prevIndex) => prevIndex + 1);
  };

  const openGallery = () => {
    setEnableLightBox(true);
  };

  const scrollThumbnails = (direction = "down") => {
    const container = itemWrapperRef.current;
    if (!container) return;

    // Get container and thumbnail dimensions
    const containerHeight = container.clientHeight;
    const firstThumbnail = container.querySelector(`.${styles.thumbnail}`);
    const thumbnailHeight = firstThumbnail?.offsetHeight || 120; // fallback height
    const gap = 8; // gap between thumbnails from CSS

    // Calculate scroll amount (show 2-3 thumbnails at a time)
    const scrollAmount = (thumbnailHeight + gap) * 2;

    // Perform smooth scroll
    const currentScroll = container.scrollTop;
    const newScroll = direction === "down"
      ? currentScroll + scrollAmount
      : currentScroll - scrollAmount;

    // Smooth scroll to new position
    container.scrollTo({
      top: Math.max(0, newScroll), // Prevent negative scroll
      behavior: 'smooth'
    });

    // Update scroll availability after animation completes
    setTimeout(() => {
      checkScrollAvailability();
    }, 300);

    // Also check immediately for better responsiveness
    setTimeout(() => {
      checkScrollAvailability();
    }, 50);
  };

  // Handle scroll event to update button states
  const handleScroll = () => {
    // Use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      checkScrollAvailability();
    });
  };

  return (
    <div className={styles.galleryBox}>
      <div className={`${styles.imageGallery} ${styles.desktop}`}>
        {!hiddenDots && (
          <div className={`${styles.thumbSlider} ${displayThumbnail ? "" : styles.hidden}`}>
            <div className={styles.thumbWrapper}>
              {/* Up scroll button */}
              {/* {canScrollUp && (
                <button
                  type="button"
                  className={`${styles.prevBtn} ${styles.btnNavGallery}`}
                  onClick={() => scrollThumbnails("up")}
                  aria-label="Scroll Up"
                >
                  <SvgWrapper
                    svgSrc="ArrowUp"
                    className={styles.navArrowIcon}
                  />
                </button>
              )} */}

              <ul
                ref={itemWrapperRef}
                className={`${styles.thumbnailList} ${styles.scrollbarHidden} ${images && images?.length < 5 ? styles.fitContent : ""
                  }`}
                onScroll={handleScroll}
                style={{
                  overflowY: images?.length > 3 ? 'scroll' : 'visible',
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none'
                }}
              >
                {images.map((item, index) => (
                  <li
                    key={index}
                    onClick={(e) => setMainImage(e, index)}
                    className={`${styles.thumbnail} ${item.type === "video" ? styles.flexAlign : ""
                      } ${currentImageIndex === index ? styles.active : ""}`}
                    style={{ "--icon-color": iconColor }}
                  >
                    {item.type === "image" && (
                      <FyImage
                        customClass={`${styles["thumbnailList--item"]}`}
                        src={item?.url}
                        alt={item?.alt}
                        aspectRatio={0.75} // getProductImgAspectRatio(globalConfig)
                        sources={[{ width: 100 }]}
                        globalConfig={globalConfig}
                        isImageCover={true}
                      />
                    )}
                    {item.type === "video" && (
                      <>
                        {item.url.includes("youtube") ? (
                          <img
                            className={`${styles["thumbnailList--item"]} ${styles.videoThumbnail}`}
                            src={getImageURL(item.url)}
                            alt={item.alt}
                          />
                        ) : (
                          <video
                            className={`${styles["thumbnailList--item"]} ${styles.videoThumbnail}`}
                            src={item?.url}
                          />
                        )}
                        <VideoPlayIcon className={styles.videoPlayIcon} />
                      </>
                    )}
                    {item.type === "3d_model" && (
                      <ThreeDIcon className={styles.modelIcon} />
                    )}
                  </li>
                ))}
              </ul>

              {/* Down scroll button */}
              {canScrollDown && images?.length > 3 && (
                <button
                  type="button"
                  className={`${styles.nextBtn} ${styles.btnNavGallery}`}
                  onClick={() => scrollThumbnails("down")}
                  aria-label="Scroll Down"
                >
                  <SvgWrapper
                    svgSrc="ArrowDown"
                    className={styles.navArrowIcon}
                  />
                </button>
              )}
            </div>
          </div>
        )}

        <div className={styles.flexAlignCenter}>
          <div className={styles.imageBox}>
            <PicZoom
              customClass={styles.imageItem}
              source={currentMedia.src}
              type={currentMedia.type}
              alt={currentMedia.alt}
              currentIndex={currentImageIndex}
              sources={imgSources}
              onClickImage={() => openGallery()}
              resumeVideo={resumeVideo}
              globalConfig={globalConfig}
              followed={followed}
              removeFromWishlist={removeFromWishlist}
              addToWishList={addToWishList}
              hideImagePreview={hideImagePreview}
            />
            {isCustomOrder && (
              <div className={`${styles.badge} ${styles.b4}`}>
                Made to Order
              </div>
            )}
          </div>
        </div>
      </div>

      <div className={styles.mobile}>
        <MobileSlider
          images={images}
          onImageClick={() => openGallery()}
          isCustomOrder={isCustomOrder}
          resumeVideo={resumeVideo}
          globalConfig={globalConfig}
          followed={followed}
          sources={imgSources}
          removeFromWishlist={removeFromWishlist}
          addToWishList={addToWishList}
          setCurrentImageIndex={setCurrentImageIndex}
          slideTabCentreNone={slideTabCentreNone}
          handleShare={handleShare}
          showShareIcon={showShareIcon}
        />
      </div>

      {enableLightBox && (
        <Suspense fallback={<div />}>
          <LightboxImage
            images={images}
            showCaption={false}
            showLightBox={enableLightBox}
            iconColor={iconColor}
            toggleResumeVideo={() => setResumeVideo((prev) => !prev)}
            globalConfig={globalConfig}
            closeGallery={() => setEnableLightBox(false)}
            currentIndex={currentImageIndex}
          />
        </Suspense>
      )}
    </div>
  );
}

export default PdpImageGallery;