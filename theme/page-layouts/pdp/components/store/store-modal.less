@import "../../../../styles/main.less";

@iconWidth: 14px;
@productNameMargin: 16px;

.data {
  margin-top: 24px;
}

.overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(20, 19, 14, 0.6);
  opacity: 0;
  transition: all 0.4s;
  z-index: 100;
  visibility: hidden;
}

.show {
  opacity: 0.5;
  visibility: visible;
}

.sidebarContainer {
  position: fixed;
  top: 0;
  right: 0;
  width: 504px;
  height: 100%;
  z-index: 101;
  background: @DialogBackground;
  box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, 0.2);
  overflow-y: auto;

  @media @tablet {
    width: 100%;
  }

  .sidebarHeader {
    background-color: #efe7d6;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .sellerLabel {
      color: @TextHeading;
      font-size: 28px;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.56px;
    }

    .closeIcon {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }

  .sidebarBody {
    padding: 32px 24px 24px;

    @media @mobile {
      padding: 24px 16px;
    }
  }
}

.customSelect {
  padding: 18px 22px 18px 16px;
  width: 100%;
  height: unset;
  border-radius: 4px;
}

.storeCounts {
  margin-top: 48px;

  @media @tablet {
    margin-top: 32px;
  }
}

.sortWrapper {
  position: relative;

  .sortButton {
    padding: 16px;
    border: 1px solid #d4d1d1;
    border-radius: 4px;
    background-color: @White;
    width: 100%;
    height: 100%;

    .selectedOption {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      width: calc(100% - 24px);
      text-align: left;
    }

    .dropdownArrow {
      height: 24px;
      width: 24px;
    }

    .rotateArrow {
      transform: rotate(180deg);
    }
  }
  .sortDropdown {
    position: absolute;
    background-color: @DialogBackground;
    width: 100%;
    top: calc(100% + 5px);
    border: 1px solid #d4d1d1;
    box-shadow:
      0px 4px 4px rgba(0, 0, 0, 0.15),
      0px 12px 16px rgba(0, 0, 0, 0.16);
    border-radius: 8px;
    padding: 0.5rem;
    z-index: 1;

    li {
      padding: 8px 12px;

      cursor: pointer;

      &:hover {
        background-color: @HighlightColor;
      }
    }

    .selectedOption {
      background-color: @ThemeAccentL3;
    }
  }
}

.viewMoreWrapper {
  margin-top: 32px;

  .viewMoreBtn {
    color: @ButtonPrimary;
    background: none;
    border: none;
    font-weight: 500;
    letter-spacing: -0.02em;
    font-size: 14px;
    line-height: 16px;

    @media @mobile {
      font-size: 12px;
      line-height: 14px;
    }
  }
}
