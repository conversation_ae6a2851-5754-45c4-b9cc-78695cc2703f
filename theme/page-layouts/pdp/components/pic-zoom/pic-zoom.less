@import "../../../../styles/main.less";

.loadImage {
  cursor: zoom-in;
}
.bgopacity {
  opacity: 0.5;
}
.pdpImage {
  background-image: @ContainerBGImage;
  border-radius: @ImageRadius;
  width: clamp(320px, 34.58vw, 664px); /* 1920 * 0.3458 ≈ 664 */
  height: clamp(480px, 46.25vw, 888px); /* 1920 * 0.4625 ≈ 888 */
  img {
    width: 7.185vw;
    height: 100%;
    object-fit: contain; /* or cover, depending on your fill preference */
  }
}
.originalImg {
  left: 0;
  position: absolute;
  top: 0;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  max-width: 720px;
}

.wishlistIcon {
  position: absolute;
  top: 25px;
  right: 25px;
  z-index: 1;
  cursor: pointer;
  color: @ButtonPrimary;

  &:hover {
    svg {
      path {
        fill: @ButtonPrimary;
        fill-opacity: 1;
      }
    }
  }
}

.videoContainer {
  height: 100%;
  display: flex;
  align-items: center;

  .originalVideo {
    width: 100%;
    height: 50%;
    cursor: pointer;
  }
}
.type3dModel {
  position: relative;
  width: 100%;
  .aspect-ratio(@ProductImgAspectRatio);
  svg {
    rect {
      fill: @PageBackground;
    }
    path {
      stroke: @TextBody;
    }
  }
  /deep/ canvas {
    width: 100%;
    height: 100%;
  }
  .expandBtn {
    position: absolute;
    bottom: 10px;
    right: 10px;
    top: unset;
    left: unset;
    width: 40px;
    height: 40px;
    cursor: pointer;
  }
}
#loader {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 93%;
}

.videoPlayerWrapper {
  position: relative;
  max-height: 100%;
  display: flex;
  align-items: center;

  .playerIcon {
    width: 40px;
    height: 40px;
    cursor: pointer;
  }

  .playerMute {
    position: absolute;
    bottom: 10px;
    left: 10px;
  }

  .playerExpand {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }

  .playerReplay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
