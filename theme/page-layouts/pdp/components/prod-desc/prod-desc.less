@import "../../../../styles/main.less";

.descContainerDesktop {
  display: none;
  margin: 3rem 0;

  @media @desktop {
    display: block;
  }

  .tabsContainer {
    display: flex;

    .tabs {
      padding: 8px 32px;
      color: @TextLabel;
      cursor: pointer;
    }

    .active {
      background-color: @ThemeAccentL2;
      color: @TextHeading;
      border: 1px solid @DividerStokes;
      border-radius: 12px 12px 0 0;
      border-bottom: 1px solid @ThemeAccentL2;
      margin-bottom: -1px;
      font-weight: 600;
    }
  }
  .details {
    padding: 24px 32px 24px 16px;
    background: @ThemeAccentL2;
    border: 1px solid @DividerStokes;
    border-radius: 0px 12px 12px 12px;

    .items {
      list-style: outside;
      padding-left: 1rem;
    }
  }
}
.uktLinks {
  font-weight: 500;
}
.return {
  font-size: 14px;
  font-weight: 500;
}

.descContainerMobile {
  padding: 0 1rem;
  margin: 1rem 0;
  @media @desktop {
    padding: 0;
  }

  &.isDesktopHidden {
    @media @desktop {
      display: none;
    }
  }

  .section-text {
    margin: 10px 0px;
  }

  .items {
    list-style: outside;
    padding-left: 1rem;
  }
}
.pdpDetail {
  width: 100%;
  list-style: outside;
  padding: 0 0 14px;
  :global {
    html {
      box-sizing: content-box;
    }
    body {
      margin: 8px;
      line-height: normal;
    }
    button {
      all: unset; /* Ensures all inherited or default styles are reset */
      display: inline-block; /* Ensures it behaves like a button */
      padding: 0.1rem 0.1rem; /* Adds some default padding */
      border: 1px solid ButtonText; /* Default button border color */
      background-color: ButtonFace; /* Default button background */
      color: ButtonText; /* Default button text color */
      cursor: pointer; /* Adds a pointer cursor for interactivity */
      font: inherit; /* Inherits font-family and size from parent */
      text-align: center; /* Centers the text */
    }

    button:focus {
      outline: 1px dotted; /* Default focus style for accessibility */
    }

    button:active {
      background-color: Highlight; /* Slightly darker on click */
    }

    button:hover {
      background-color: ButtonHighlight; /* Default hover background */
    }

    a {
      color: revert;
    }
    blockquote,
    figure {
      margin: 1em 40px;
    }
    q {
      quotes: '"' '"' "" " " "";

      &:before {
        content: open-quote;
      }

      &:after {
        content: close-quote;
      }
    }
    hr {
      border: 1px inset;
      box-sizing: border-box;
      margin: 0.5em autl;
    }
    h1 {
      font-size: 2em;
      font-weight: bold;
      margin: 0.67em 0;
    }
    h2 {
      font-size: 1.5em;
      font-weight: bold;
      margin: 0.83em 0;
    }
    h3 {
      font-size: 1.17em;
      font-weight: bold;
      margin: 1em 0;
    }
    h4 {
      font-size: 1em;
      font-weight: bold;
      margin: 1.33em 0;
    }
    h5 {
      font-size: 0.83em;
      font-weight: bold;
      margin: 1.67em 0;
    }
    h6 {
      font-size: 0.67em;
      font-weight: bold;
      margin: 0.83em 0;
    }
    article,
    aside,
    nav,
    section {
      h1 {
        font-size: 1.17em;
        font-weight: bold;
        margin: 1em 0;
      }
      article,
      aside,
      nav,
      section {
        h1 {
          font-size: 1em;
          font-weight: bold;
          margin: 1.33em 0;
        }
        article,
        aside,
        nav,
        section {
          h1 {
            font-size: 0.83em;
            font-weight: bold;
            margin: 1.67em 0;
          }

          article,
          aside,
          nav,
          section {
            h1 {
              font-size: 0.67em;
              font-weight: bold;
              margin: 2.33em 0;
            }

            article,
            aside,
            nav,
            section {
              h1 {
                font-size: 0.67em;
                font-weight: bold;
                margin: 2.33em 0;
              }
            }
          }
        }
      }
    }
    table {
      border-collapse: separate;
      border-spacing: 2px;
      border-color: gray;
    }
    thead,
    tbody,
    tfoot,
    tr {
      border-color: inherit;
      vertical-align: middle;
    }
    td,
    th {
      padding: 1px;
      vertical-align: inherit;
    }
    th {
      font-weight: bold;
    }
    caption {
      text-align: center;
    }
    ul,
    menu {
      list-style-type: disc;
      margin: 0;
      padding: 0 0 0 16px;
    }
    ol {
      list-style-type: decimal;
      margin: 0;
      padding: 0 0 0 16px;
    }
    ul,
    ol {
      ul {
        list-style-type: circle;
      }

      ul,
      ol {
        ul {
          list-style-type: square;
        }
      }
    }
    dd {
      margin: 0 0 0 40px;
    }
    dl {
      margin: 1em 0;
    }
    ul,
    ol,
    menu,
    dl {
      ul,
      ol,
      menu,
      dl {
        margin: 0;
      }
    }
    legend {
      padding: 0 2px;
    }
    fieldset {
      border-style: groove;
      border-width: 2px;
      border: 2px groove ThreeDFace;
      margin: 0 2px;
      padding: 0 2px 3px;
      -webkit-padding-before: 0.35em;
      -webkit-padding-start: 0.75em;
      -webkit-padding-end: 0.75em;
      -webkit-padding-after: 0.625em;
    }
    ins {
      background-color: transparent;
      font-weight: inherit;
      text-decoration: underline;
    }
    b,
    strong {
      font-weight: bold;
    }
    i,
    cite,
    em,
    var,
    address,
    dfn {
      font-style: italic;
      font-weight: inherit;
    }
    abbr[title],
    dfn[title] {
      border-bottom: 0;
      cursor: default;
      font-weight: inherit;
    }
    tt,
    code,
    kbd,
    samp {
      font-family: monospace;
      font-weight: inherit;
    }
    pre {
      font-family: monospace;
      margin: 1em 0;
      white-space: pre;
    }
    mark {
      background-color: yellow;
      color: black;
      font-style: normal;
      font-weight: inherit;
    }
    big {
      font-size: larger;
      font-weight: inherit;
    }
    small {
      font-size: smaller;
      font-weight: inherit;
    }
    sub,
    sup {
      font-weight: inherit;
      line-height: inherit;
      position: static;
    }
    sub {
      font-size: smaller;
      bottom: 0;
      vertical-align: sub;
    }
    sup {
      font-size: smaller;
      top: 0;
      vertical-align: super;
    }
    ruby {
      > rt {
        font-size: 50%;
      }
    }
    iframe {
      border: 2px inset;
    }
  }
}

.bulletSpacing {
  padding-left: 16px;
  list-style: disc;
}

.removeBullets {
  list-style-type: none;
  padding-left: 0 !important;
}

.noDataPlaceholder {
  color: gray;
  text-align: center;
  padding: 1rem;
}

.productLongDescription {
  & > :first-child {
    margin-top: 0;
  }
  & > :last-child {
    margin-bottom: 0;
  }
}

.productDetailsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  @media @tablet {
    padding: 0.625rem 0;
  }

  .smoothAccordion {
    transition:
      height 0.3s ease-in-out,
      opacity 0.3s ease-in-out;
    overflow: hidden;
    &.isOpen {
      height: auto;
      opacity: 1;
    }
    &.isClosed {
      height: 0;
      opacity: 0;
    }
  }
}

.productDetailsTitle {
  color: #000;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Bold";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%;
}

.productDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 30px 20px;
  width: 100%;

  .productDetailsLeft {
    width: calc(50% - 10px);
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: flex-start;
  }
  .productDetailsRight {
    width: calc(50% - 10px);
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: flex-start;
  }

  // Two columns
  > .detailItem {
    width: calc(50% - 10px);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .label {
    color: @Dark-40;
    font-family: "Helvetica Medium";
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 130%;
    letter-spacing: 0.12px;
    margin-bottom: 2px;
  }

  .value {
    color: @Dark-80;
    font-family: "Helvetica Bold";
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
    letter-spacing: 0.28px;
    white-space: pre-line;
  }
}
.returnExchangeContent {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
  width: 100%;

  .label {
    color: @Dark-80;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 700;
    line-height: 130%; /* 18.2px */
    letter-spacing: 0.28px;
  }
}

.productLongDescription {
  align-self: stretch;
  color: @Dark-80;
  font-family: "Helvetica Medium";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 150%; /* 24px */
  letter-spacing: 0.32px;
}

.cleaningAndCareContainer {
  width: 100%;
  margin-top: 12px;
  margin-bottom: 12px;
}

.cleaningAndCareTitle {
  color: #000;
  font-family: "Helvetica Bold";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%;
  margin-bottom: 20px;
}

.cleaningAndCareItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
}
.cleaningAndCareGrid {
  display: flex;
  flex-wrap: wrap;
  width: 660px;
  padding-bottom: 5px;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .cleaningAndCareItem {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .cleaningAndCareText {
    padding: 0 1rem;
  }
}

.detailItem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 20px;
}

.cleaningAndCareLogos {
  display: flex;
  width: 28px;
  height: 28px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5.6px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
  }
}

.cleaningAndCareText {
  color: @Dark;
  text-align: center;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Medium";
  font-size: clamp(14px, 0.833vw, 16px);
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 20.8px */
  letter-spacing: 0.32px;
  // display: flex;
  // flex-wrap: wrap;
}

// Apply the same styling to additional info section

.additionalInfoList {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  gap: 10px;
  list-style-type: disc; /* bullet point */
  padding-left: 20px;
}

.additionalInfoLabel {
  color: @Dark-80;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Medium";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 20.8px */
}

.line {
  height: 1px;
  background: @Dark-10;
  margin-top: 1.875rem;
  margin-bottom: 3.125rem;
  @media (max-width: 768px) {
    margin-top: 1.875rem;
    margin-bottom: 0;
  }
}
