import React, { useEffect } from "react";
import { FDKLink } from "fdk-core/components";
import FyImage from "../../../../components/core/fy-image/fy-image";
import styles from "./product-variants.less";
import { isRunningOnClient } from "../../../../helper/utils";
import CheckIcon from "../../../../assets/images/check.svg";

function ProductVariants({
  variants,
  product,
  currentSlug,
  globalConfig,
  preventRedirect = false,
  setSlug,
}) {
  const isProductSet = product.is_set;
  const getVariantSetText = () => (isProductSet ? "Size" : "Set");
  const getProductLink = (item) => {
    return `/product/${item.slug}`;
  };

  const getImageURL = (item) => {
    if (Array.isArray(item.medias) && item.medias.length > 0) {
      return item.medias[0].url;
    }
    return "";
  };

  const isVariantSelected = (item) => {
    if (currentSlug) {
      return currentSlug?.includes(item.slug);
    } else if (isRunningOnClient()) {
      return window?.location?.pathname.includes(item.slug);
    }
  };

  const getSelectedVariantLabel = (item) => {
    const selectedVariant =
      item?.items?.find((variant) => isVariantSelected(variant)) || {};
    let selectedValue = "";

    if (["color", "image"].includes(item.display_type)) {
      selectedValue = selectedVariant?.color_name;
    } else {
      selectedValue = selectedVariant?.value;
    }

    return `${item.header ? `${item.header}: ` : ""}${selectedValue ? `${selectedValue} (selected)` : ""
      }`;
  };
  console.log(" {getSelectedVariantLabel(item)}", getSelectedVariantLabel(variants[0]));

  return (
    <div className={styles.productVariants}>
      {variants?.length > 0 &&
        variants.map((item, type) => (
          <div key={item.header + type} className={styles.variantWrapper}>
            {item.key === "is_set" ? (
              item?.items.map((variant, index) =>
                isProductSet !== variant.value ? (
                  <div key={variant.slug + index}>
                    Interested in {getVariantSetText()}?
                    <button
                      type="button"
                      className={styles.uktLinks}
                      onClick={() => getProductLink(variant)}
                    >
                      Pick {getVariantSetText()}
                    </button>
                  </div>
                ) : null
              )
            ) : (
              <>
                {getSelectedVariantLabel(item) && (
                  <div className={`captionNormal ${styles.variantTitle}`}>
                    <span className={styles.variantTitle}>Color : </span>
                    <span className={styles.variantSubTitle}>{getSelectedVariantLabel(item).split(" ")[2]}</span>
                  </div>
                )}
                {item.display_type === "image" ? (
                  <div className={styles.variantContainer}>
                    {item?.items.map((variant, index) => (
                      <div
                        key={variant.slug + index}
                        className={`${styles.variantItemImage} ${isVariantSelected(variant) ? styles.selected : ""
                          } ${!variant.is_available ? styles.unavailable : ""}`}
                      >
                        <FDKLink to={getProductLink(variant)}>
                          <FyImage
                            src={getImageURL(variant)}
                            alt={variant?.name}
                            globalConfig={globalConfig}
                          />
                        </FDKLink>
                        {/* <div
                          className={`${isVariantSelected(variant)
                            ? styles.selectedOverlay
                            : styles.overlay
                            }`}
                        ></div>
                        <span>
                          <CheckIcon className={styles.selectedIcon} />
                        </span> */}
                        {/* add red line below the selected variant at gap : 10px from above image */}
                        <div className={`${styles.Line} ${isVariantSelected(variant) ? styles.selectedLine : ""}`}></div>
                      </div>
                    ))}
                  </div>
                ) : null}
                {item?.display_type === "color" ? (
                  <div className={styles.variantContainer}>
                    {item?.items.map((variant) => (
                      <div
                        key={variant?.slug}
                        className={`${styles.variantItemColor} ${isVariantSelected(variant) ? styles.selected : ""
                          }`}
                      >
                        <FDKLink
                          to={getProductLink(variant)}
                          title={variant.color_name}
                        >
                          <div
                            style={{
                              background: `#${variant.color}`,
                            }}
                            className={`${styles.color} ${isVariantSelected(variant) ? styles.selected : ""
                              } ${!variant.is_available ? styles.unavailable : ""
                              }`}
                          >
                            <div
                              className={`${isVariantSelected(variant)
                                ? styles.selectedOverlay
                                : styles.overlay
                                }`}
                            >
                              <span />
                            </div>
                            <CheckIcon className={styles.selectedIcon} />
                          </div>
                        </FDKLink>
                      </div>
                    ))}
                  </div>
                ) : null}
                {item?.display_type === "text" ? (
                  <div className={styles.variantContainer}>
                    {item?.items.map((variant, index) => (
                      <div
                        key={variant.slug + index}
                        className={`${styles.variantItemText} b2 ${isVariantSelected(variant) ? styles.selected : ""
                          } ${!variant.is_available ? styles.unavailable : ""}`}
                      >
                        {!preventRedirect ? (
                          <FDKLink to={getProductLink(variant)}>
                            <div>{variant?.value}</div>
                          </FDKLink>
                        ) : (
                          <div onClick={() => setSlug(variant?.slug)}>
                            {variant?.value}
                          </div>
                        )}
                        <span />
                      </div>
                    ))}
                  </div>
                ) : null}
              </>
            )}
          </div>
        ))}
    </div>
  );
}

export default ProductVariants;
