@import "../../../../styles/main.less";

.variantTitle {
  color: @Dark;
  leading-trim: both;
  text-edge: cap;

  /* H5 */
  font-family: "Helvetica Bold";
  font-size: 1.125rem;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 23.4px */
  @media @tablet {
    display: none;
  }
}
.variantSubTitle {
  color: @Dark;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Bold";
  font-size: 1.125rem;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 23.4px */
  opacity: 0.5;
  @media @tablet {
    display: none;
  }
}

.variantWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20px;
  @media @tablet {
    gap: 0px;
    margin-bottom: 30px;
  }
}

.variantContainer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  gap: 12px;
  @media @tablet {
    gap: 8px;
  }
  &::-webkit-scrollbar {
    display: none;
  }
  .variantItemImage,
  .variantItemColor .color {
    width: 60px;
    height: auto;
    border-radius: 4px;
    position: relative;
    overflow: hidden;

    &:not(.selected) {
      .overlay,
      .selectedIcon {
        display: none;
      }
    }
    &:is(.unavailable) {
      .overlay {
        display: block;
        background: rgba(255, 255, 255, 0.7);
      }
    }
    &:hover {
      .overlay {
        display: block;
      }
    }
    .overlay {
      background: rgba(255, 255, 255, 0.4);
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      pointer-events: none;
    }
    .selectedOverlay {
      display: none;
    }
    .selectedIcon {
      height: 24px;
      width: 24px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      fill: @ButtonSecondary;
    }
  }
  .Line {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    justify-self: center;
    width: calc(100% - 20px);
    height: 4px;
    background-color: transparent;
    margin-top: 10px;
    border-radius: 10px;
  }
  .selectedLine {
    background-color: @Brand;
  }
  .variantItemImage {
    display: inline-block;
    cursor: pointer;
  }
  .variantItemColor {
    .color {
      width: 40px;
      height: 40px;
      border: 2px solid transparent;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }

      &.selected {
        border-color: @ButtonPrimary;
        box-shadow:
          0 0 0 2px @PageBackground,
          0 0 0 4px @ButtonPrimary;
      }

      &.unavailable .overlay > span {
        position: absolute;
        height: 80px;
        width: 80px;
        bottom: 0;
        border-left: 1px solid @ButtonSecondary;
        transform: rotate(45deg);
        transform-origin: bottom left;
      }
    }
  }
  .variantItemText {
    display: inline-block;
    border-radius: 4px;
    border: 1px solid @DividerStokes;
    padding: 4px 12px;
    color: @TextHeading;
    cursor: pointer;
    position: relative;
    &:not(.unavailable):hover {
      background-color: @ThemeAccentL2;
    }
    &.selected {
      background-color: @ThemeAccent;
    }
    &.unavailable {
      color: @TextDisabled;
      span {
        position: absolute;
        .inset(0);
        background-color: @DividerStokes;
        clip-path: polygon(
          calc(100% + 1px) 0,
          100% 0,
          0 100%,
          0 calc(100% + 1px)
        );
      }
    }
  }
}
