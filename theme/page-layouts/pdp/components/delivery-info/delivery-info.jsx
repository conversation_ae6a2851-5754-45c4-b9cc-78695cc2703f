import React, { useEffect, useState } from "react";
import { convertUTCDateToLocalDate } from "../../../../helper/utils";
import { useHyperlocalTat, useSyncedState } from "../../../../helper/hooks";
import styles from "./delivery-info.less"; // Import the module CSS
import DeliveryIcon from "../../../../assets/images/delivery.svg";
import LocationIcon from "../../../../assets/images/location-on.svg";
import FyndLogoIcon from "../../../../assets/images/fynd-logo.svg";
import SvgWrapper from "../../../../components/core/svgWrapper/SvgWrapper";
import { LOCALITY } from "../../../../queries/localityQuery";

function DeliveryInfo({
  className,
  selectPincodeError,
  deliveryPromise,
  pincode,
  pincodeErrorMessage,
  checkPincode,
  setPincodeErrorMessage,
  pincodeInput,
  // setCurrentPincode,
  isValidDeliveryLocation,
  deliveryLocation,
  isServiceabilityPincodeOnly,
  fpi,
  showLogo = false,
  city,
  state,
}) {
  const [postCode, setPostCode] = useSyncedState(pincode || "");
  const [tatMessage, setTatMessage] = useState("");

  const { isHyperlocal, convertUTCToHyperlocalTat } = useHyperlocalTat({ fpi });
  const { displayName, maxLength, validatePincode } = pincodeInput;

  useEffect(() => {
    if (isValidDeliveryLocation) {
      getDeliveryDate();
    }
  }, [deliveryPromise, isValidDeliveryLocation]);

  function changePostCode(pincode) {
    setPostCode(pincode);
    // setCurrentPincode(pincode); // Sync with parent state
    setTatMessage("");
    setPincodeErrorMessage("");
    if (validatePincode(pincode) === true) {
      checkPincode(pincode);
    }
  }

  const handlePincodeSubmit = (pincode) => {
    const result = validatePincode(pincode);
    if (result !== true) {
      setPincodeErrorMessage(result);
      return;
    }
    setPincodeErrorMessage("");
    checkPincode(pincode);
  };

  const getDeliveryDate = () => {
    const options = {
      weekday: "short",
      month: "short",
      day: "numeric",
    };
    const { min, max } = deliveryPromise || {};

    if (!min) {
      return false;
    }

    if (isHyperlocal) {
      setTatMessage(convertUTCToHyperlocalTat(min));
      return;
    }

    const minDate = convertUTCDateToLocalDate(min, options);
    const maxDate = convertUTCDateToLocalDate(max, options);
    setTimeout(() => {
      setTatMessage(
        `Delivery ${min === max ? `on ${minDate}` : `between ${minDate} - ${maxDate}`}`
      );
    }, 1000);
  };

  const openInternationalDropdown = () => {
    fpi.custom.setValue("isI18ModalOpen", true);
  };

  const deliveryLocForIntlShipping = () => {
    return (
      <>
        {!isValidDeliveryLocation ? (
          <h4
            className={`${styles.deliveryLabel} b2 ${styles.cursor}`}
            onClick={openInternationalDropdown}
          >
            Select delivery location
          </h4>
        ) : (
          <span className={`${styles.flexAlignCenter}`}>
            <span className={styles.deliveryLocation}>
              <span className={styles.deliveryLocation__bold}>
                Delivery at{" "}
              </span>
              <span
                onClick={openInternationalDropdown}
                className={styles.deliveryLocation__addrs}
              >
                {deliveryLocation}
              </span>
            </span>
          </span>
        )}
      </>
    );
  };

  const deliveryLoc = () => {
    return (
      <>
        <h4 className={`${styles.deliveryLabel} b2`}>
          Check estimated delivery
        </h4>
        <div className={styles.delivery}
          onClick={() => handlePincodeSubmit(postCode)}
        >
          <div className={styles.deliveryInput}>

            <span
              className={`${styles.flexAlignCenter}`}
            >
              <SvgWrapper svgSrc="locationPdp" className={styles.locationPdp} />
            </span>

            <input
              autoComplete="off"
              value={postCode}
              placeholder="Enter pincode"
              className={`b2 ${styles.pincodeInput} ${styles.fontBody}`}
              type="text"
              maxLength={maxLength}
              onChange={(e) => changePostCode(e?.target?.value)}
            />
          </div>

          {/* Show city and state if available */}
          {(city || state) && (
            <div className={styles.cityStateInfo}>
              {/* {city && <span>{city}</span>} */}
              {state && <span>{state}</span>}
            </div>
          )}
        </div>

        {selectPincodeError && !pincodeErrorMessage?.length && (
          <div className={`captionNormal ${styles.emptyPincode}`}>
            {`Please enter valid ${displayName} before Add to cart/ Buy now`}
          </div>
        )}
      </>
    );
  };

  return (
    <div className={`${styles.deliveryInfo} ${className}`}>
      <div
        className={!isServiceabilityPincodeOnly ? styles.deliveryWrapper : ""}
      >
        {!isServiceabilityPincodeOnly && <LocationIcon />}
        <div className={styles.deliveryInfoWrapper}>
          {isServiceabilityPincodeOnly
            ? deliveryLoc()
            : deliveryLocForIntlShipping()}
        </div>
        {!pincodeErrorMessage && !selectPincodeError && (
          <div
            className={`${styles.deliveryDate} ${styles.dateInfoContainer}`}
          >
            {isValidDeliveryLocation && tatMessage?.length > 0 && (
              <>
                {isServiceabilityPincodeOnly && (
                  <DeliveryIcon className={`${styles.deliveryIcon}`} />
                )}
                <div className={`${styles.deliveryText} captionNormal`}>
                  {tatMessage}
                  {showLogo && (
                    <div className={styles.fyndLogo}>
                      <span>with</span>
                      <FyndLogoIcon style={{ marginLeft: "2px" }} />
                      <span className={styles.fyndText}>Fynd</span>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        )}
      </div>

      {pincodeErrorMessage && (
        <div className={`captionNormal ${styles.error}`}>
          {pincodeErrorMessage}
        </div>
      )}
      <div className={styles.line}></div>
    </div>
  );
}

export default DeliveryInfo;
