@import "../../../../styles/main.less";

.delivery {
  display: flex;
  height: 50px;
  padding: 10px 20px 10px 14px;
  align-items: center;
  align-self: stretch;
  border-radius: @ButtonRadius-small;
  border: 1px solid @Dark-10;
  background: @White;
  gap: 0.75rem;
  justify-content: space-between;
  align-items: center;
}
.deliveryInput {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
}
.deliveryInfo {
  @media @tablet {
    margin-bottom: 1.875rem;
  }
  .deliveryLabel {
    color: @Dark;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 700;
    line-height: 130%; /* 23.4px */
    @media @tablet {
      font-size: 16px !important;
    }
  }
  .uktLinks {
    .user-select-none();
    font-size: 15px;
  }
  .cursor {
    cursor: pointer;
  }
  @media @tablet {
    margin-bottom: 1.875rem;
  }
  .line {
    width: 100%;
    height: 1px;
    background-color: @Dark-10;
    margin-top: 1.875rem;
    @media @tablet {
      display: none;
    }
  }
}

.deliveryWrapper {
  display: flex;
  gap: 8px;
  padding: 16px;
  align-items: flex-start;
  border-radius: 16px;
  border: 1px solid @DividerStokes;
}
.deliveryInfoWrapper {
  width: 100%;
  color: @Dark;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Bold";
  font-size: 1.125rem;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 23.4px */
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 0.875rem;
}
.deliveryDate {
  &.dateInfoContainer {
    display: flex;
    align-items: flex-start;
    gap: 4px;

    .deliveryText {
      color: @Dark-80;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Medium";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 15.6px */
      letter-spacing: 0.12px;
    }

    .fyndLogo {
      display: inline-flex;
      align-items: center;
      gap: 2px;
      margin-left: 4px;

      .fyndText {
        color: @TextBody;
        font-size: 12px;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.24px;
      }
    }
  }
  color: @SuccessText;
  display: flex;
  align-items: center;

  .deliveryIcon {
    width: 14px;
    height: 12px;
    margin: 1px 0;
    flex: 0 0 14px;

    /deep/ svg {
      path {
        fill: @SuccessText;
      }
    }
  }
}
.deliveryLocation {
  padding-bottom: 4px;
  .text-line-clamp();
  &__bold {
    color: @TextBody;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: -0.32px;
    @media @tablet {
      font-size: 14px;
    }
  }
  &__addrs {
    color: @ButtonLink;
    font-size: 14px;
    @media @tablet {
      font-size: 12px;
    }
    font-weight: 500;
    letter-spacing: -0.28px;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    text-transform: uppercase;
    cursor: pointer;
  }
}
.error {
  color: @ErrorText;
  margin-top: 6px;
}
.button {
  font-weight: 500;
  letter-spacing: -0.02em;
  line-height: 16px;
  @media @desktop {
    font-size: 14px;
  }
  @media @tablet {
    font-size: 12px;
    line-height: 14px;
  }
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  .flexAlignCenter {
    border-bottom: 1px solid @ButtonPrimary;
    color: @Dark;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 130%; /* 20.8px */
    letter-spacing: 0.16px;
  }
  background-color: unset;
  border: none;
  padding-right: 12px;
  cursor: pointer;
  text-transform: uppercase;
  color: @ButtonPrimary;
  // & > span {
  //   border-bottom: 1px solid @ButtonPrimary;
  // }
  .locationPdp {
    display: block;
    width: 24px;
    height: 24px;
    flex-shrink: 0;

    @media @tablet {
      display: none;
    }
  }
}

.pincodeInput {
  color: @Dark;
  leading-trim: both;
  text-edge: cap;
  font-family: "Helvetica Medium" !important;
  font-size: 16px !important;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 20.8px */
  letter-spacing: 0.16px;
  width: 100px;

  @media @tablet {
    font-size: 14px !important;
  }
}

.error {
  color: @ErrorText;
}
.emptyPincode {
  color: @InformationText;
}
