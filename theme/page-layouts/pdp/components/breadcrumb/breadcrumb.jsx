import React from "react";
import { FDKLink } from "fdk-core/components";
import { convertActionToUrl } from "@gofynd/fdk-client-javascript/sdk/common/Utility";
import styles from "./breadcrumb.less";

function BreadCrumb({ breadcrumb, config, customClass }) {
  const getBrand = () => productData?.brand || {};

  const getCategory = () => {
    const category = productData?.categories?.[0] || {};
    const updatedCategory = {};
    const categorySlug = category?.action?.page?.query?.category?.[0] || "";
    updatedCategory.name = category.name || "";
    updatedCategory.url = categorySlug && `/products/?category=${categorySlug}`;

    return updatedCategory;
  };

  return (
    <div className={`${styles.breadcrumbs} captionNormal ${customClass}`}>
      {breadcrumb.map((item, index) => (
        <React.Fragment key={index}>
          {item.isActive ? (
            <span className={styles.active}>{item.label}</span>
          ) : (
            <span>
              <FDKLink to={item.url}>{item.label}</FDKLink>
              {index < breadcrumb.length - 1 && <>&nbsp;/&nbsp;</>}
            </span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}

export default BreadCrumb;
