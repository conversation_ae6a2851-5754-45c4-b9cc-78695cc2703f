@import "../../../../styles/main.less";

.occassionsToWearContainer {
  display: flex;
  flex-direction: column;
  padding: 100px 40px;
  justify-content: center;
  align-items: flex-start;
  gap: 60px;
  background: @Dark-10;
  width: 100%;
  overflow: hidden;
  @media @tablet {
    padding: 30px 10px;
    gap: 20px;
  }

  .occassionsToWearHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
  }

  .occassionsToWearTitle {
    color: @Dark;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 3rem;
    font-style: normal;
    font-weight: 700;
    line-height: 115%; /* 55.2px */
    @media @tablet {
      color: #000;
      font-family: "Helvetica Bold";
      font-size: 1rem;
      line-height: 130%; /* 20.8px */
    }
  }

  .occassionsToWearSubtitle {
    color: #000;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Medium";
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 500;
    line-height: 130%; /* 23.4px */
    letter-spacing: 0.18px;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    @media @tablet {
      color: #000;
      font-family: "Helvetica Medium";
      font-size: 0.875rem;
      line-height: 130%; /* 18.2px */
      letter-spacing: 0.14px;
    }
  }

  .occassionsToWearItemsContainer {
    display: flex;
    flex-direction: row;
    gap: 20px;
    width: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    overflow-y: hidden;
    @media @tablet {
      flex-direction: row;
    }
  }

  .occassionsToWearItems {
    display: flex;
    flex: 0 0 clamp(233px, 23.28vw, 447px);
    height: clamp(300px, 30.63vw, 574px);
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 30.499px;
    position: relative;
    overflow: hidden;
    // @media @tablet {
    //   flex: 0 0 233px;
    // }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 30.499px;
      // @media @tablet {
      //   height: 100%;
      //   object-fit: cover;
      // }
    }

    .occassionsToWearItemsTitle {
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 25px 30px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      @media @tablet {
        display: flex;
        padding: 0.625rem 0.875rem;
        justify-content: space-between;
        align-items: center;
        flex: 1 0 0;
      }

      .occassionsToWearItemsTitleText {
        color: white;
        font-family: "Helvetica Bold";
        font-size: 1.5rem;
        font-style: normal;
        @media @tablet {
          font-size: 1rem;
          font-style: normal;
          line-height: 130%; /* 20.8px */
        }
      }
    }
  }
}
@import "../../../../styles/main.less";

.occassionsToWearContainer {
  display: flex;
  flex-direction: column;
  padding: 100px 40px;
  justify-content: center;
  align-items: flex-start;
  gap: 60px;
  background: @Dark-10;
  width: 100%;
  overflow: hidden;
  @media @tablet {
    padding: 30px 10px;
    gap: 20px;
  }

  .occassionsToWearHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
  }

  .occassionsToWearTitle {
    color: @Dark;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: 3rem;
    font-style: normal;
    font-weight: 700;
    line-height: 115%; /* 55.2px */
    @media @tablet {
      color: #000;
      font-family: "Helvetica Bold";
      font-size: 1rem;
      line-height: 130%; /* 20.8px */
    }
  }

  .occassionsToWearSubtitle {
    color: #000;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Medium";
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 500;
    line-height: 130%; /* 23.4px */
    letter-spacing: 0.18px;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    @media @tablet {
      color: #000;
      font-family: "Helvetica Medium";
      font-size: 0.875rem;
      line-height: 130%; /* 18.2px */
      letter-spacing: 0.14px;
    }
  }

  .occassionsToWearItemsContainer {
    display: flex;
    flex-direction: row;
    gap: 20px;
    width: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    overflow-y: hidden;
    @media @tablet {
      flex-direction: row;
    }
  }

  .occassionsToWearItems {
    display: flex;
    flex: 0 0 clamp(233px, 23.28vw, 447px);
    height: clamp(300px, 30.63vw, 574px);
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 30.499px;
    position: relative;
    overflow: hidden;
    // @media @tablet {
    //   flex: 0 0 233px;
    // }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 30.499px;
      // @media @tablet {
      //   height: 100%;
      //   object-fit: cover;
      // }
    }

    .occassionsToWearItemsTitle {
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 25px 30px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      @media @tablet {
        display: flex;
        padding: 0.625rem 0.875rem;
        justify-content: space-between;
        align-items: center;
        flex: 1 0 0;
      }

      .occassionsToWearItemsTitleText {
        color: white;
        font-family: "Helvetica Bold";
        font-size: 1.5rem;
        font-style: normal;
        @media @tablet {
          font-size: 1rem;
          font-style: normal;
          line-height: 130%; /* 20.8px */
        }
      }
    }
  }
}
