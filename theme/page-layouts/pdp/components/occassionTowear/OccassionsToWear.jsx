import React from 'react'
import styles from './occassionsToWear.less'
import Hoodie from '../../../../assets/images/hoodie.png'
const OccassionsToWear = () => {
    const occassionsToWear = [
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },
        {
            title: "Casual",
            // image: <PERSON><PERSON>,
        },

    ]
    return (
        <div className={styles.occassionsToWearContainer}>
            <div className={styles.occassionsToWearHeader}>
                <h2 className={styles.occassionsToWearTitle}>Occasion to Wear</h2>
                <h4 className={styles.occassionsToWearSubtitle}>what's this ?</h4>
            </div>
            <div className={styles.occassionsToWearItemsContainer}>
                {
                    occassionsToWear.map((item, index) => {
                        return (
                            <div className={styles.occassionsToWearItems} key={index}>
                                <img src={Hoodie} alt="hoodie" />
                                <div className={styles.occassionsToWearItemsTitle}>
                                    <h1 className={styles.occassionsToWearItemsTitleText}>{item.title}</h1>
                                </div>
                            </div>
                        )
                    })
                }
            </div>
        </div>
    )
}

export default OccassionsToWear
