import React from 'react'
import styles from './ProductDetails.module.less'
const ProductDetails = () => {
    return (
        <>
            {/* product name */}
            <div className={styles.titleBlock}>
                {getBlockConfigValue(block, "show_brand") && (
                    <h3 className={`${styles.productBrand} fontHeader`}>
                        {brand?.name || ""}
                    </h3>
                )}
                <div className={styles.productTitleWrapper}>
                    <div className={styles.productTitleContainer}>
                        <div className={styles.bestSellerContainer}>
                            <span className={styles.bestSellerText}>Best seller</span>
                        </div>
                        <h1
                            className={`${styles.productTitle} ${styles.fontHeader} fontHeader h3`}
                        >
                            {name}
                        </h1>
                    </div>
                    <div
                        className={styles.heartIcon}
                        onClick={() => toggleWishlist({ product: productDetails, isFollowed })}
                    >
                        <WishlistIcon isActive={isFollowed} />
                    </div>
                    <>
                        {/* <span
                      className={styles.shareIcon}
                      onClick={() => handleShare()}
                    >
                      <ShareDesktopIcon />
                    </span> */}
                        {/* {showSocialLinks && (
                      <ShareItem
                        setShowSocialLinks={setShowSocialLinks}
                        handleShare={() => handleShare()}
                        description={`Check out this amazing product on ${application?.name}`}
                      />
                    )} */}
                    </>

                </div>
            </div>

            {/* product price */}
            <>
                {show_price && (
                    <div className={styles.product__price}>
                        {!isLoading && productMeta?.sellable && (
                            <div className={styles.priceContainer}>
                                <div className={styles.mrpStrike}>
                                    {getProductPrice("marked") &&
                                        getBlockConfigValue(block, "mrp_label") &&
                                        getProductPrice("effective") !==
                                        getProductPrice("marked") && (
                                            <span
                                                className={`${styles.mrpLabel} ${styles["mrpLabel--marked"]}`}
                                            >
                                                MRP:
                                            </span>
                                        )}
                                    {getProductPrice("effective") !==
                                        getProductPrice("marked") && (
                                            <span
                                                className={
                                                    styles["product__price--marked"]
                                                }
                                            >
                                                {getProductPrice("marked") || "2000"}
                                            </span>
                                        )}
                                </div>

                                <h4
                                    className={
                                        styles["product__price--effective"]
                                    }
                                >
                                    {getProductPrice("effective")}
                                </h4>

                                {discountLabel && (
                                    <span
                                        className={
                                            styles["product__price--discount"]
                                        }
                                    >
                                        {discountLabel || "40 % off"}
                                    </span>
                                )}
                            </div>
                        )}
                    </div>
                )}
            </>


            {/* product tax label */}
            <>
                {getBlockConfigValue(block, "tax_label") &&
                    productMeta?.sellable && (
                        <div
                            className={`captionNormal ${styles.taxLabel}`}
                        >
                            (
                            {getBlockConfigValue(block, "tax_label")}
                            )
                        </div>
                    )}

                {/* remove when block is added */}
                <div className={styles.fitTypeContainer}>
                    {
                        fitType.length > 0 && (
                            fitType.map((fit) => (
                                <div className={styles.fitTypeTextContainer}>
                                    <p className={styles.fitTypeText}>{fit}</p>
                                </div>
                            ))
                        )
                    }
                </div>
            </>


            {/* fit type */}
            <div className={styles.fitTypeContainer}>
                {
                    fitType.length > 0 && (
                        fitType.map((fit) => (
                            <div className={styles.fitTypeTextContainer}>
                                <p className={styles.fitTypeText}>{fit}</p>
                            </div>
                        ))
                    )
                }
            </div>

        </>

    )

}

export default ProductDetails
