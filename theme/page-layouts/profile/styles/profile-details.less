@import "../../../styles/main.less";

.profileDetailsContainer {
  flex: 1;

  .formContainer {
    position: relative;
    padding: 24px 0;
    font-weight: 300;
    font-size: 12px;

    @media @tablet {
      padding: 16px;
    }

    .form {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .radioInputContainer {
        margin-bottom: 11px;
        margin-right: 25px;
        .formLabel {
          .required {
            color: @ErrorText;
          }
        }
        .radioContent {
          margin: 4px 0 0;

          .radioInput {
            display: inline-flex;
            align-items: center;
            padding: 5px;
            cursor: pointer;

            .formRadioInput {
              accent-color: @ButtonPrimary;
              height: 15px;
              aspect-ratio: 1;
              cursor: pointer;
            }

            .radioLabel {
              margin-inline-start: 10px;
              font-weight: 300;
              font-size: 14px;
              color: @Ma<PERSON>;
              cursor: pointer;
            }
          }
        }
      }

      .inputContainer {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .actionContainer {
        .submitBtn {
          border-radius: @ButtonRadius;
          margin-top: 4px;
          width: 100%;
        }
      }
    }
  }
}
