import React from "react";
import { Section<PERSON>enderer } from "fdk-core/components";
import { useGlobalStore } from "fdk-core/utils";
import { useThemeConfig } from "../helper/hooks";
import Card from "../components/plp-card/card";
import useProductListing from "../hooks/useProductListing";


const ProductListing = ({ fpi }) => {
  const page = useGlobalStore(fpi.getters.PAGE) || {};
  const { globalConfig } = useThemeConfig({ fpi });
  const { sections = [] } = page || {};

  const { productList, loading, onLoadMore, pageInfo } =
    useProductListing({ fpi });

  if (loading && productList.length === 0) {
    console.log("loading");
    return <div>Loading…</div>;
  }

  return (
    <>

      {

        page?.value === "product-listing" && (
          <SectionRenderer
            sections={sections}
            fpi={fpi}
            globalConfig={globalConfig}
          />
        )}

      {/* <div className="w-10 h-10 bg-red-500">HIIIIII</div>HI */}


      <div>


        {/* <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {productList.length > 0 && (
            productList.map((product) => (
              <Card
                key={product.id}
                product={product}
                fpi={fpi}
                globalConfig={globalConfig}
              />
            ))
          )}
        </div> */}

        {/* {pageInfo.has_next && (
        <button onClick={onLoadMore}>Load More</button>
      )} */}
      </div>
    </>


  );
};

export const sections = JSON.stringify([
  {
    attributes: {
      page: "product-listing",
    },
  },
]);

export default ProductListing;
