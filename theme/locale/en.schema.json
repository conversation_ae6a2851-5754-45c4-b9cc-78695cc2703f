{"resource": {"common": {"link_text": "Link text", "hover_link_text": "Hover Link Text", "hotspot_hover_image": "Hotspot Hover Image", "height": "Height", "width": "width", "show_clickable_area": "Show Clickable Area", "pointer": "Pointer", "box": "Box", "pointer_type": "Pointer Type", "header": "Header", "description": "Description", "container_background_color": "Container Background Color", "image_container_bg_color": "This color will be used as the container background color of the Product/Collection/Category/Brand images wherever applicable", "fit_image_to_container": "Fit image to the container", "clip_image_to_fit_container": "If the image aspect ratio is different from the container, the image will be clipped to fit the container. The aspect ratio of the image will be maintained", "heading": "Heading", "section_heading_text": "Heading text of the section", "section_description_text": "Description text of the section", "button_text": "Button Text", "auto_play_slides": "Auto Play Slides", "center": "Center", "left": "Left", "right": "Right", "change_slides_every": "Change slides every", "image_container": "Image Container", "stack": "<PERSON><PERSON>", "horizontal_scroll": "Horizontal scroll", "mobile_layout": "Mobile Layout", "alignment_of_content": "Alignment of content", "desktop_layout": "Desktop Layout", "horizontal": "Horizontal", "not_applicable_for_mobile": "It'll not work for mobile layout", "start": "Start", "end": "End", "show_add_to_cart": "Show Add to Cart", "not_applicable_international_websites": "Not Applicable for International Websites", "show_wish_list_icon": "Show Wish List Icon", "select_a_product": "Select a Product", "product_item_display": "Product Item to be displayed", "show_seller": "Show Seller", "show_size_guide": "Show Size Guide", "size_selection_style": "Size selection style", "dropdown_style": "Dropdown style", "block_style": "Block style", "hide_single_size": "Hide single size", "price_tax_label_text": "Price tax label text", "redirect_link": "Redirect Link", "image_card": "Image card", "image": "Image", "redirect": "Redirect", "desktop_image": "Desktop Image", "mobile_image": "Mobile Image", "text_alignment_desktop": "Text Alignment (Desktop)", "text_alignment_mobile": "Text Alignment (Mobile)", "hotspot_desktop": "Hotspot Desktop", "vertical_position": "Vertical Position", "horizontal_position": "Horizontal Position", "product": "Product", "hotspot_mobile": "Hotspot Mobile", "navigation": "Navigation", "mandatory_delivery_check": "Mandatory Delivery check", "preselect_size": "Preselect size", "applicable_for_multiple_size_products": "Applicable only for multiple-size products", "custom_button": "Custom Button", "custom_button_text": "Custom Button text", "custom_button_link": "Custom Button link", "custom_button_icon": "Custom Button Icon", "autoplay_slides": "AutoPlay Slides", "icon": "Icon", "category_name_placement_info": "Place the category name inside or outside the image", "infinite_loading": "Infinite Loading", "pagination": "Pagination", "loading_options": "Loading Options", "infinity_scroll": "Infinity Scroll", "infinite_scroll_info": "If it is enabled, view more button will not be shown, only on scroll products will be displayed", "back_to_top": "Back to top", "show_logo_of_brands": "Show Logo of brands", "show_category_name": "Show category name", "show_product_numbers": "Show product numbers", "view_more": "View More", "show_back_to_top": "Show back to top button", "open_product_in_new_tab": "Open product in new tab", "open_product_in_new_tab_desktop": "Open product in new tab for desktop", "hide_brand_name": "Hide Brand Name", "hide_brand_name_info": "Check to hide Brand name", "four_cards": "4 Cards", "two_cards": "2 Cards", "default_grid_layout_desktop": "Default grid layout desktop", "three_cards": "3 Cards", "default_grid_layout_tablet": "Default grid layout tablet", "one_card": "1 Card", "default_grid_layout_mobile": "Default grid layout mobile", "no_banner": "No Banner", "right_banner": "Right Banner", "left_banner": "Left Banner", "image_layout": "Image Layout", "image_banner": "Image Banner", "infinite_scroll": "Infinite Scroll", "register": "Register", "login": "<PERSON><PERSON>", "hide_single_size_info": "Hide single size"}, "settings_schema": {"common": {"typography": "Typography", "algolia_configuration": "Algolia Configuration", "footer": "Footer", "cart_and_button_configuration": "Cart & Button Configuration", "product_card_configuration": "Product Card Configuration", "other_page_configuration": "Other Page Configuration", "google_maps": "Google Maps", "mobile_logo_max_height": "Mobile Logo Max Height"}, "typography": {"font_header": "<PERSON><PERSON>", "font_body": "Font Body", "title_font": "Title Font", "body_font": "Body Font"}, "header": {"layout": "Layout", "single_row_navigation": "Single Row Navigation", "double_row_navigation": "Double Row Navigation", "always_on_search": "Always on Search", "one_click_search_info": "It provides one click search in header, this option will only be functional with a \"Double Row Navigation\" Layout configuration", "desktop_logo_menu_alignment": "Desktop Logo & Menu Alignment", "logo_left_menu_center": "Lo<PERSON> left, Menu centre", "logo_left_menu_left": "<PERSON><PERSON> left, <PERSON><PERSON> left", "logo_left_menu_right": "<PERSON><PERSON> left, <PERSON><PERSON> right", "logo_centre": "Logo Centre", "switch_to_mega_menu": "Switch to Mega Menu", "mega_menu_double_row_required": "The mega menu will only be displayed with a \"Double Row Navigation\" Layout configuration", "show_border_on_desktop": "Show border on desktop", "show_border_on_desktop_info": "It adds a border below header on desktop devices", "use_full_width_mega_menu": "Use full width Mega Menu", "use_full_width_mega_menu_info": "The mega menu will be full width", "navigation_font_weight": "Navigation font weight", "serviceability_check_in_header": "Serviceability check in header", "mandatory_serviceability_check": "Mandatory serviceability check", "minutes": "Minutes", "show_delivery_promise_in_minutes": "Show delivery promise in minutes", "set_minute_threshold_for_promise": "Set minute threshold for promise", "hours": "Hours", "show_delivery_promise_in_hours": "Show delivery promise in hours", "set_hour_threshold_for_promise": "Set hour threshold for promise", "today_tomorrow": "Today / Tomorrow", "today_tomorrow_info": "Show delivery promise as today/tomorrow", "date_range": "Date Range", "date_range_info": "Show delivery promise in date range", "bold": "Bold", "regular": "Regular", "semibold": "Semibold"}, "algolia_configuration": {"enable_algolia": "Enable Algolia"}, "footer": {"logo": "Logo", "bottom_bar_image": "Bottom Bar Image", "enable_footer_image": "Enable Footer Image", "desktop": "Desktop", "mobile_tablet": "Mobile/Tablet", "show_footer_contact_details_background": "Show Footer Contact Details Background", "social_media_text": "Social media text label", "social_media_text_info": "Show Label text for social media", "footer_logo_max_height_mobile": "Footer Logo <PERSON> (Mobile)", "footer_logo_max_height_desktop": "Footer Logo <PERSON> Height (Desktop)"}, "cart_and_button_configuration": {"cart_options": "Cart Options", "disable_cart": "Disable Cart", "disables_cart_and_checkout": "Disables Cart and Checkout", "show_price": "Show Price", "applies_to_product_pdp_featured_section": "Applies to Product Card, PDP and Featured Product Section", "buy_button_configurations": "Buy Button Configurations", "button_options": "Button Options", "applicable_pdp_featured_product": "Applicable for PDP and Featured Product Section", "add_to_cart_buy_now": "Add to cart & Buy now", "add_to_cart_custom_button": "Add to cart & Custom Button", "buy_now_custom_button": "Buy now & Custom Button", "add_to_cart": "Add to cart", "buy_now": "Buy now", "all_three": "All three", "none": "None", "show_quantity_control": "Show Quantity Control", "display_in_place_of_add_to_cart": "Displays in place of Add to Cart when enabled"}, "product_card_configuration": {"product_card_aspect_ratio": "Product Card Aspect Ratio", "width_in_px": "Width (in px)", "default_aspect_ratio_limit": "Default aspect ratio is 0.8. User can update between 0.6 to 1. For more than 1 it will be set to default 0.8. Applicable for lisiting pages, product page & featured collection section", "height_in_px": "Height (in px)", "display_sale_badge": "Display Sale Badge", "hide_sale_badge": "Uncheck to hide sale badge", "image_border_radius": "Image Border Radius", "border_radius_for_image": "Border radius for Image", "show_image_on_hover": "Show image on hover", "hover_image_display": "This option controls whether an additional image is displayed when hovering over the product card", "badge_border_radius": "Badge Border Radius", "badge_border_radius_info": "Border radius for Badge"}, "other_page_configuration": {"improve_image_quality": "Improve Image Quality", "upscale_images": "Upscale Images", "home_page_performance_impact": "This may affect your page performance. Applicable for home-page", "section_margins": "Section Margins", "bottom_margin": "Bottom Margin", "bottom_margin_for_section": "Bottom margin for section", "border_radius": "Border Radius", "button_border_radius": "Button Border Radius", "border_radius_for_button": "Border radius for Button", "use_original_images": "Use Original Images"}, "google_maps": {"enable_google_maps": "Enable Google Maps", "google_maps_api_key": "Google Maps API Key", "google_maps_api_key_info": "This API key helps connect your site to Google Maps so you can display interactive maps and location details"}}, "sections": {"application_banner": {"application_banner": "Application Banner", "box_pointer_only": "Only applicable for box pointer type", "circular_pointer_only": "Only applicable for circular pointer type", "enable_hover_effect_on_banner": "Enable hover effect on Banner"}, "brand_listing": {"brands_listing": "Brands Listing", "brands_per_row_desktop": "Display brands per row (desktop)", "logo_only": "Logo Only", "align_brands": "Align brands", "brand_alignment": "Align brand cards to the left, right or centre of the page in desktop", "our_top_brands": "Our Top Brands", "all_is_unique": "All is unique no matter how you put it", "brand_item": "Brand Item", "select_brand": "Select Brand", "only_logo": "Only Logo"}, "cart_landing": {"cart_landing": "Cart Landing", "coupon": "Coupon", "comment": "Comment", "gst_card": "GST Card", "orders_india_only": "Applicable only for orders in India", "price_breakup": "Price Breakup", "behalf_of_customer": "Behalf of customer", "login_checkout_buttons": "Log-In/Checkout <PERSON>", "share_cart": "Share Cart"}, "categories_listing": {"categories_listing": "Categories Listing", "category_name_placement": "Category name placement", "inside_the_image": "Inside the image", "outside_the_image": "Outside the image", "top": "Top", "bottom": "Bottom", "category_name_position": "Category name position", "category_name_alignment": "Display category name at top, bottom or center", "category_name_text_alignment": "Category name text alignment", "align_category_name": "Align category name left, right or center", "items_per_row_desktop": "Items per row(Desktop)", "max_items_per_row_horizontal": "Maximum items allowed per row for Horizontal view, for gallery max 5 are viewable and only 5 blocks are required", "category_item": "Category Item", "select_department": "Select Department", "desktop_layout": "Desktop Layout"}, "collections_listing": {"collections_listing": "Collections Listing", "collection_product_grid": "Collection Product Grid", "all_collections": "All Collections", "layout_mobile": "Layout(Mobile)", "layout_desktop": "Layout(Desktop)", "collections_per_row_desktop": "Display collections per row (desktop)", "collection_item": "Collection Item", "select_collection": "Select Collection", "collection_1": "Collection 1", "collection_2": "Collection 2", "collection_3": "Collection 3", "select_collection_info": "Select a collection to display its products. Applicable when the section is used on pages other than the collection listing page", "button_link_info": "Select the destination link where users will be redirected when they click on the banner image", "product_number": "Show Product Numbers", "product_number_info": "Show the number of products in the listing", "loading_options_info": "Choose how products load on the page based on user interaction. Infinite Scroll continuously loads more products as users scroll. Pagination organises products into separate pages with navigation controls. View More loads additional products only when users click a button", "show_size_guide_info": "Applicable for Add to Cart popup", "heading_info": "Set the heading text for the collections page", "description_info": "Add a description for the collections page", "collection_title": "Collection Title & Button Placement", "collection_title_info": "Place collection title and button inside or outside the image"}, "featured_collection": {"featured_collection": "Featured Collection", "collection": "Collection", "select_collection_for_products": "Select a collection to display its products", "banner_horizontal_carousel": "Banner with horizontal carousel", "layout_desktop": "Layout (Desktop)", "desktop_content_alignment": "Alignment of content in desktop", "banner_horizontal_scroll": "Banner with horizontal scroll", "banner_with_stack": "Banner with <PERSON>ack", "layout_mobile": "Layout (Mobile)", "content_alignment_mobile": "Alignment of content in mobile", "text_alignment": "Text Alignment", "alignment_of_text_content": "Alignment of text content", "small": "Small", "medium": "Medium", "large": "Large", "title_size": "Title size", "select_title_size": "Select title size", "products_per_row_desktop": "Products per row (Desktop)", "max_items_per_row_horizontal_scroll": "Maximum items allowed per row in horizontal scroll", "products_per_row_mobile": "Products per row (Mobile)", "maximum_products_to_show": "Maximum products to show", "max_products_horizontal_scroll": "Maximum products to show in horizontal scroll", "show_badge": "Show Badge", "show_view_all_button": "Show View All Button", "button_link": "Button Link", "product_text_alignment": "Product Text Alignment", "product_text_alignment_info": "Alignment of product text content"}, "featured_products": "Featured Products", "hero_image": {"text_placement_mobile": "Text Placement (Mobile)", "mobile_tablet_banner": "Mobile/Tablet Banner", "text_placement_desktop": "Text Placement (Desktop)", "bottom_end": "Bottom-End", "bottom_center": "Bottom-Center", "bottom_start": "Bottom-Start", "center_end": "Center-End", "center_center": "Center-Center", "center_start": "Center-Start", "center_left": "Center-Left", "center_right": "Center-Right", "top_end": "Top-End", "top_center": "Top-Center", "top_start": "Top-Start", "desktop_banner": "Desktop Banner", "primary_button_inverted_color": "Inverted color of the primary button", "invert_button_color": "Invert Button Color (Toggle)", "hero_image": "Hero Image", "no_overlay": "No Overlay", "white_overlay": "White Overlay", "black_overlay": "Black Overlay", "overlay_option": "Overlay Option", "image_overlay_opacity": "Use this option to add a black or white semi-transparent overlay on top of the image. This will help you to stand out your text content"}, "hero_video": {"hero_video": "Hero Video", "primary_video": "Primary Video", "video_url": "Video URL", "video_support_mp4_youtube": "Supports MP4 Video & Youtube Video URL", "autoplay": "Autoplay", "enable_autoplay_muted": "Check to enable autoplay (Video will be muted if autoplay is active)", "hide_video_controls": "Hide Video Controls", "disable_video_controls": "check to disable video controls", "play_video_loop": "Play Video in Loop", "disable_video_loop": "check to disable Loop", "thumbnail_image": "Thumbnail Image", "display_pause_on_hover": "Display pause on hover", "display_pause_on_hover_info": "Show pause button on video hover on desktop"}, "image_gallery": {"image_gallery": "Image Gallery", "card_radius": "<PERSON>", "items_per_row_limit_for_scroll": "Items per row should be less than number of blocks to show horizontal scroll", "items_per_row_desktop": "Items per row (Desktop)", "items_per_row_mobile": "Items per row (Mobile)", "search_link_type": "Search Link Type"}, "image_slideshow": {"image_slideshow": "Image Slideshow", "slide_link": "Slide Link", "check_to_autoplay_slides": "Check to autoplay slides", "autoplay_slide_duration": "Autoplay slide duration", "top_margin": "Top Margin", "top_margin_info": "Top margin for section", "open_links_in_new_tab": "Open Links in New Tab", "open_links_in_new_tab_info": "Enable to open slide links in a new tab"}, "link": {"link_label": "Link Label", "link_label_info": "Label to show for link", "url": "URL", "url_for_link": "URL for link", "link_target": "Link Target", "html_target": "HTML target attribute for link"}, "media_with_text": {"media_with_text": "Media with Text", "mobile_image": "mobile Image", "top_start": "Top Start", "top_center": "Top Center", "top_end": "Top End", "center_center": "Center Center", "center_start": "Center Start", "center_end": "Center End", "bottom_start": "Bottom Start", "bottom_end": "Bottom End", "bottom_center": "Bottom Center", "text_align_desktop": "Set text alignment for desktop", "text_align_mobile": "Set text alignment for mobile devices", "invert_section": "Invert Section", "reverse_section_desktop": "Reverse the section on desktop"}, "multi_collection_product_list": {"multi_collection_product_list": "Multi Collection Product List", "products_per_row": "Products per row", "max_products_per_row": "Maximum products allowed per row", "header_position": "Header Position", "show_view_all": "Show View All", "view_all_requires_heading": "\"View All\" will be visible only if a \"Heading\" is provided", "icon_or_navigation_name_mandatory": "Icon or Navigation Name is mandatory", "navigation_name": "Navigation Name", "button_link": "Button Link"}, "order_details": {"order_details": "Order Details", "order_header": "Order Header", "shipment_items": "Shipment Items", "shipment_tracking": "Shipment Tracking", "shipment_address": "Shipment Address", "payment_details_card": "Payment Details Card", "shipment_breakup": "Shipment Breakup", "shipment_medias": "Shipment Medias"}, "product_description": {"product_description": "Product Description", "enable_buy_now": "Enable Buy now", "enable_buy_now_feature": "Enable buy now feature", "zoom_in": "Zoom product image", "zoom_in_info": "Zoom product image on cursor hover", "show_bullets_in_product_details": "Show Bullets in Product Details", "play_video_icon_color": "Play video icon color", "product_detail_postion": "Product Detail Postion", "accordion_style": "Accordion style", "tab_style": "Tab style", "show_products_breadcrumb": "Show Products breadcrumb", "show_category_breadcrumb": "Show Category breadcrumb", "show_brand_breadcrumb": "Show Brand breadcrumb", "first_accordian_open": "First Accordian Open", "product_name": "Product Name", "display_brand_name": "Display Brand name", "product_price": "Product Price", "display_mrp_label_text": "Display MRP label text", "product_tax_label": "Product Tax Label", "short_description": "Short Description", "product_variants": "Product Variants", "seller_details": "<PERSON><PERSON>", "size_container_with_action_buttons": "<PERSON><PERSON> Container with Action Buttons", "size_guide": "Size Guide", "applicable_for_pdp_section": "Applicable for PDP Section", "pincode": "Pincode", "show_brand_logo": "Show brand logo", "show_brand_logo_name_in_pincode_section": "The pincode section will show the brand logo and name", "add_to_compare": "Add to Compare", "offers": "Offers", "show_offers": "Show Offers", "prod_meta": "Prod Meta", "return": "Return", "show_item_code": "Show Item code", "trust_markers": "Trust Markers", "badge_logo_1": "Badge logo 1", "badge_label_1": "Badge label 1", "badge_url_1": "Badge URL 1", "badge_logo_2": "Badge logo 2", "badge_label_2": "Badge label 2", "badge_url_2": "Badge URL 2", "badge_logo_3": "Badge logo 3", "badge_label_3": "Badge label 3", "badge_url_3": "Badge URL 3", "badge_logo_4": "Badge logo 4", "badge_label_4": "Badge label 4", "badge_url_4": "Badge URL 4", "badge_logo_5": "Badge logo 5", "badge_label_5": "Badge label 5", "badge_url_5": "Badge URL 5"}, "raw_html": {"custom_html": "Custom HTML", "your_code_here": "Your Code Here", "custom_html_code_editor": "Add Your custom HTML Code below. You can also use the full screen icon to open a code editor and add your code"}, "testimonial": {"testimonial": "Testimonial", "text_for_testimonial": "Text for testimonial", "text": "Text", "author_name": "Author Name", "author_description": "Author Description"}, "trust_marker": {"trust_marker": "Trust Marker", "card_background_color": "Card Background Color", "card_background_color_info": "This color will be used as card background", "desktop_tablet_layout": "Desktop/Tablet Layout", "columns_per_row_desktop_tablet": "Display column per row (desktop/Tablet)", "columns_per_row_mobile": "Display column per row (Mobile)", "not_applicable_desktop": "It'll not work for desktop layout"}, "blog": {"blog": "Blog", "feature_blog": "Feature Blog", "show_blog_slideshow": "Show Blog Slide Show", "filter_by_tags": "Filter By Tags", "filter_by_tags_info": "Blog tags are case-sensitive. Enter the identical tag used on the Fynd platform, separated by commas, to display the blog post in the slideshow.", "blog_tags_info": "Blog tags are case-sensitive. Enter the identical tag used on the Fynd platform, separated by commas, to display the blog post in the slideshow", "show_tags": "Show Tags", "show_search_bar": "Show Search Bar", "show_recently_published": "Show Recently Published", "recently_published_info": "The Recently Published section will display the latest five published blogs", "recently_published_blogs": "Recently Published Blogs", "show_top_viewed": "Show Top Viewed", "top_viewed_info": "The Top Viewed section will display the latest five published blogs tagged with the 'top5' value", "top_viewed_blogs": "Top Viewed Blogs", "show_filters": "Show Filters", "button_label": "Button Label", "fallback_image": "Fallback Image", "change_slides_every_info": "Set speed at which slides to be autoplayed in blog slideshow", "loading_options_info": "Choose how blogs load on the page based on user interaction. Infinite Scroll continuously loads more blogs as users scroll. Pagination organises blogs into separate pages with navigation controls", "button_position": "Button Position", "button_position_info": "Applicable for only desktop view", "below_products": "Below products", "below_description": "Below description"}, "contact_us": {"banner_alignment": "Banner alignment", "description_alignment": "Description Alignment", "description_alignment_info": "Select the alignment for the description", "banner_alignment_info": "Select the alignment for the banner", "upload_banner": "Upload banner", "upload_banner_info": "Upload banner image (Only for desktop)", "overlay_banner_opacity": "Overlay Banner opacity", "address": "Address", "show_address": "Show Address", "description": "Description", "show_description": "Show Description", "below_header": "Below Header", "above_footer": "Above Footer", "phone": "Phone", "show_phone": "Show Phone", "email": "Email", "show_email": "Show Email", "social_media_icons": "Social media icons", "show_icons": "Show Icons", "working_hours": "Working Hours", "show_working_hours": "Show Working Hours", "contact_us": "Contact Us"}, "products_listing": {"desktop_banner_image": "Desktop Banner Image", "mobile_banner_image": "Mobile Banner Image", "desktop_banner_info": "Upload an image to be displayed as a banner on desktop devices", "mobile_banner_info": "Upload an image to be displayed as a banner on mobile devices", "product_listing": "Product Listing", "page_loading_options": "Page Loading Options", "description_info": "Add a description to be displayed at the bottom of the product listing page", "show_size_guide_info": "Show size guide in add to cart popup. Not applicable for international websites", "tax_label_info": "Set the text for the price tax label displayed in the 'Add to Cart' popup. Not applicable for international websites", "size_selection_style_info": "Select the size display format in Add to Cart popup. Not applicable for international websites", "back_top": "Show back to Top button", "image_size_for_tablet_desktop": "Image size for Tablet/Desktop", "products_per_page": "Products per Page", "enable_sales_badge": "Enable Badge", "image_size_for_mobile": "Image size for Mobile"}, "custom_html": {"custom_html": "Custom HTML"}, "brand_landing": {"brands_landing": "Brands Landing", "back_to_top_info": "Enable a 'Back to Top' button to help users quickly return to the top of the page.", "only_logo_info": "Display only brand logos", "heading_info": "Set the heading text for the brands page", "description_info": "Add a description for the brands page"}, "categories": {"categories": "Categories", "heading_info": "Set the heading text for the categories page", "description_info": "Add a description for the categories page", "back_top": "Back to Top button", "show_category_name": "Show Category Name", "show_category_name_info": "Show the category name on the category cards", "category_name_placement": "Category Name Placement", "category_name_placement_info": "Place the category name on the inside or outside the category card", "category_name_text_alignment": "Category Name Text Alignment", "category_name_text_alignment_info": "Select the alignment of the category name - left, center or right", "bottom": "Category Name Position", "bottom_info": "Display category name at the top, center or bottom of the category card", "top_padding": "Top padding", "top_padding_for_section": "Top padding for section", "bottom_padding": "Bottom padding", "bottom_padding_for_section": "Bottom padding for section", "item_count_mobile": "Items per row (Mobile)", "item_count_mobile_info": "Maximum items allowed per row for Horizontal view on mobile"}}, "pages": {"wishlist": {"show_add_to_cart": "Show Add to <PERSON><PERSON> button", "mandatory_delivery_check_info": "Mandatory delivery check in Add to Cart popup. Not applicable for international websites", "preselect_size_info": "Preselect size in Add to Cart popup. Applicable only for multi-sized products. Not applicable for international websites", "hide_single_size_info": "Hide single size in Add to Cart popup. Not applicable for international websites"}}, "default_values": {"read_more": "Read More", "the_unparalleled_shopping_experience": "The Unparalleled Shopping Experience", "blog_description": "Everything you need for that ultimate stylish wardrobe, <PERSON><PERSON><PERSON> has got it!", "shop_now": "Shop Now", "view_all_caps": "VIEW ALL", "view_all": "View all", "enquire_now": "Enquire now", "a_true_style": "A True Style", "cta_text": "Be exclusive, Be Divine, Be yourself", "tax_label": "Price inclusive of all tax", "collects_listing_heading": "Explore Our Collections", "collects_listing_description": "Organize your products into these collections to help customers easily find what they're looking for. Each category can showcase a different aspect of your store's offerings", "feature_blog_description": "Chique is a fast-growing indowestern womenswear brand having several stores pan India. Simple, innovative and progressive", "featured_collection_heading": "New Arrivals", "featured_collection_description": "Showcase your top collections here! Whether it's new arrivals, trending items, or special promotions, use this space to draw attention to what's most important in your store", "hero_image_heading": "Welcome to Your New Store", "hero_image_description": "Begin your journey by adding unique images and banners. This is your chance to create a captivating first impression. Customize it to reflect your brand's personality and style!", "image_gallery_title": "Customize Your Style", "image_gallery_description": "This flexible gallery lets you highlight key products and promotions, guiding customers to the right places", "link_label": "Section Link", "link_url": "/", "product_listing_tax_label": "Tax inclusive of all GST", "testimonial_title": "What People Are Saying About Us", "testimonial_textarea": "Add customer reviews and testimonials to showcase your store's happy customers", "trust_maker_title": "Title", "add_description": "Add description", "free_delivery": "Free Delivery", "marker_description": "Don`t love it? Don`t worry. Return delivery is free"}}}