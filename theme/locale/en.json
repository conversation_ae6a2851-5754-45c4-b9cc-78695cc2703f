{"resource": {"dynamic_label": {"total_mrp": "Total MRP", "subtotal": "Subtotal", "sub_total": "Sub Total", "reward_points": "Reward Points", "promotion": "Promotion", "cashback_applied": "Cashback Applied", "delivery_charges": "Delivery Charges", "cod_charge": "COD charge", "cod_charges": "COD Charges", "total_charge": "Total charge", "gift_price": "Gift Price", "value_of_good": "Value of Good", "mop_total": "MOP total", "total_mop": "Total MOP", "credit_note": "Credit note", "gst_charge": "GST charge", "mrp_total": "MRP total", "gift_card": "Gift card", "total": "Total", "convenience_fee": "Convenience Fee", "discount": "Discount", "coupon": "Coupon", "delivery_charge": "Delivery Charge", "best_price_fast_delivery": "Best price & fast delivery", "best_price": "Best Price", "fastest_delivery": "Fastest Delivery", "flat_no_house_no": "Flat No/House No", "building_name_street": "Building Name/street", "locality_landmark": "Locality/Landmark", "pincode": "Pincode", "city": "City", "state": "State", "full_name": "Full Name", "mobile_number": "Mobile Number", "email": "Email", "pay_later": "Pay Later", "card": "Card", "net_banking": "Net Banking", "wallet": "Wallet", "upi": "UPI", "upi_qr": "UPI QR", "cash_on_delivery": "Cash On Delivery", "partner_pay": "Partner Pay", "fynd_cash": "<PERSON><PERSON><PERSON>", "pay_at_counter": "Pay at Counter", "jioonepay": "JioOnePay", "pay_using_ccavenue": "Pay using Ccavenue", "card_on_delivery": "Card On Delivery", "rupify": "Rupify", "cash_at_store": "Cash at Store", "card_swiped_at_store": "Card Swiped at Store", "cardless_emi": "Cardless EMI", "payment_link": "Payment Link", "upi_at_store": "UPI at Store", "store_credits": "Store Credits", "payzapp": "Payzapp", "simpl": "Simpl", "freecharge_pay_later_wallet": "Freecharge Pay Later Wallet", "paytm": "Paytm", "mobikwik": "Mobikwik", "ola_money": "<PERSON><PERSON>", "sbi_buddy": "SBI Buddy", "vodafone_mpesa": "Vodafone mPesa", "airtel_money": "Airtel Money", "amazon_pay": "Amazon Pay", "paypal": "PayPal", "credpay": "<PERSON><PERSON><PERSON><PERSON>", "phonepe": "PhonePe", "apple_pay": "Apple Pay", "google_pay": "Google Pay", "lakshmi_vilas_bank_retail_banking": "Lakshmi Vilas Bank - Retail Banking", "bank_of_bahrein_and_kuwait": "Bank of Bahrein and Kuwait", "airtel_payments_bank": "Airtel Payments Bank", "punjab_national_bank_retail_banking": "Punjab National Bank - Retail Banking", "bank_of_india": "Bank of India", "state_bank_of_mysore": "State Bank of Mysore", "indian_bank": "Indian Bank", "indian_overseas_bank": "Indian Overseas Bank", "oriental_bank_of_commerce": "Oriental Bank of Commerce", "dhanlaxmi_bank": "Dhanlaxmi Bank", "syndicate_bank": "Syndicate Bank", "saraswat_cooperative_bank": "Saraswat Co-operative Bank", "andhra_bank": "Andhra Bank", "bank_of_baroda_retail_banking": "Bank of Baroda - Retail Banking", "punjab_sind_bank": "Punjab & Sind Bank", "kotak_mahindra_bank": "Kotak Mahindra Bank", "indusind_bank": "Indusind Bank", "janata_sahakari_bank_pune": "Janata Sahakari Bank (Pune)", "central_bank_of_india": "Central Bank Of India", "citi_bank_netbanking": "Citi Bank NetBanking", "nkgsb_cooperative_bank": "NKGSB Co-operative Bank", "dhanalaxmi_bank_duplicate": "Dhanalaxmi Bank", "development_bank_of_singapore": "Development Bank of Singapore", "corporation_bank": "Corporation Bank", "city_union_bank": "City Union Bank", "idfc_bank": "IDFC Bank", "punjab_maharashtra_cooperative_bank": "Punjab & Maharashtra Co-operative Bank", "idbi": "IDBI", "standard_chartered_bank": "Standard Chartered Bank", "shamrao_vithal_cooperative_bank": "Shamrao Vithal Co-operative Bank", "punjab_national_bank_corporate_banking": "Punjab National Bank - Corporate Banking", "axis_bank": "Axis Bank", "canara_bank": "Canara Bank", "rbl_bank": "RBL Bank", "catholic_syrian_bank": "Catholic Syrian Bank", "allahabad_bank": "Allahabad Bank", "uco_bank": "UCO Bank", "federal_bank": "Federal Bank", "tamilnadu_mercantile_bank": "Tamilnadu Mercantile Bank", "state_bank_of_india": "State Bank of India", "jammu_and_kashmir_bank": "Jammu and Kashmir Bank", "cosmos_cooperative_bank": "Cosmos Co-operative Bank", "lakshmi_vilas_bank_corporate_banking": "Lakshmi Vilas Bank - Corporate Banking", "state_bank_of_travancore": "State Bank of Travancore", "bank_of_maharashtra": "Bank of Maharashtra", "hdfc_bank": "HDFC Bank", "karnataka_bank": "Karnataka Bank", "ing_vysya_bank": "ING Vysya Bank", "karur_vysya_bank": "Karur Vysya Bank", "state_bank_of_bikaner_and_jaipur": "State Bank of Bikaner and Jaipur", "yes_bank": "Yes Bank", "vijaya_bank": "Vijaya Bank", "united_bank_of_india": "United Bank of India", "industrial_development_bank_of_india": "Industrial Development Bank of India", "dcb_bank": "DCB Bank", "development_credit_bank": "Development Credit Bank", "dena_bank": "Dena Bank", "tamilnadu_state_apex_cooperative_bank": "Tamilnadu State Apex Co-operative Bank", "state_bank_of_patiala": "State Bank of Patiala", "south_indian_bank": "South Indian Bank", "icici_bank": "ICICI Bank", "union_bank_of_india": "Union Bank of India", "state_bank_of_hyderabad": "State Bank of Hyderabad", "deutsche_bank": "Deutsche Bank", "au_small_finance_bank": "AU Small Finance Bank", "andhra_pragathi_grameena_bank": "Andhra Pragathi Grameena Bank", "axis_corporate_netbanking": "Axis Corporate Netbanking", "bandhan_bank": "Bandhan Bank", "bandhan_bank_corporate": "Bandhan Bank Corporate", "bank_of_baharin_and_kuwait": "Bank of Baharin and Kuwait", "bank_of_baroda": "Bank of Baroda", "bank_of_baroda_corporate": "Bank of Baroda Corporate", "bassein_catholic_bank": "Bassein Catholic Bank", "bharat_bank": "Bharat Bank", "cosmos_bank": "COSMOS Bank", "dcb_bank_business": "DCB BANK Business", "dhanlaxmi_bank_corporate": "Dhanlaxmi Bank Corporate", "dbs_bank_ltd": "DBS Bank Ltd", "esaf_small_finance_bank": "ESAF Small Finance Bank", "equitas_small_finance_bank": "Equitas small finance bank", "fincare_bank": "Fincare Bank", "gopinath_parsik_bank": "Gopinath Parsik bank", "hsbc_bank": "HSBC BANK", "icici_netbanking": "ICICI Netbanking", "icici_bank_corporate": "ICICI Bank Corporate", "idbi_bank_corporate": "IDBI Bank Corporate", "jana_small_finance_bank": "Jana Small Finance Bank", "janata_sahakari_bank": "Janata Sahakari Bank", "jio_payments_bank": "Jio Payments Bank", "kalupur_bank": "Kalupur Bank", "kalyan_janata_sahakari_bank": "Kalyan Janata Sahakari Bank", "karnataka_gramin_bank": "Karnataka Gramin Bank", "karnataka_vikas_grameena_bank": "Karnataka Vikas Grameena Bank", "karur_vysya_corporate_banking": "Karur Vysya Corporate Banking", "karur_vysya": "<PERSON><PERSON><PERSON>", "kotak_bank": "Kotak Bank", "lakshmi_vilas_bank_retail": "Lakshmi Vilas Bank Retail", "lakshmi_vilas_bank_corporate": "Lakshmi Vilas Bank Corporate", "mehsana_urban_co_op_bank": "Mehsana Urban Co. Op. Bank", "nkgsb_bank": "NKGSB Bank", "nkgsb_co_op_bank": "NKGSB Co:op Bank", "nsdl_payments_bank": "NSDL Payments Bank", "north_east_small_finance_bank": "North East Small Finance Bank", "pnb_erstwhile_oriental_bank_of_commerce": "PNB (Erstwhile :Oriental Bank of Commerce)", "pnb_erstwhile_oriental_bank_of_commerce_dup": "PNB (Erstwhile:Oriental Bank of Commerce)", "pnb_erstwhile_united_bank_of_india": "PNB (Erstwhile:United Bank of India)", "paytm_payments_bank_limited": "Paytm Payments Bank Limited", "punjab_national_bank_retail": "Punjab National Bank Retail", "punjab_national_bank_corporate": "Punjab National Bank Corporate", "rbl_corporate": "RBL : Corporate", "royal_bank_of_scotland": "Royal Bank of Scotland", "svc_co_operating_pvt_ltd": "SVC co:operating Pvt Ltd", "svc_cooperative_bank_corporate_banking": "SVC Co:Operative Bank Ltd. : Corporate Banking", "saraswat_bank": "Saraswat Bank", "shamrao_vithal_coop_bank": "Shamrao Vithal Coop Bank", "shamrao_vithal_coop_bank_corporate": "Shamrao Vithal Coop Bank : Corporate", "shivalik_mercantile_cooperative_bank_ltd": "Shivalik Mercantile Cooperative Bank Ltd", "suryoday_small_finance_bank": "Suryoday Small Finance Bank", "tjsb_sahakari_bank": "TJSB Sahakari Bank", "tamil_nadu_state_cooperative_bank": "Tamil Nadu State Co:operative Bank", "tamilnad_mercantile_bank": "Tamilnad Mercantile Bank", "thane_bharat_sahakari_bank": "Thane Bharat Sahakari Bank", "ujjivan_small_finance_bank": "Ujjivan Small Finance Bank", "union_bank_corporate_banking": "Union Bank Corporate Banking", "varachha_cooperative_bank_limited": "Varachha Co:operative Bank Limited", "yes_bank_corporate": "Yes Bank Corporate", "zoroastrian_cooperative_bank_limited": "Zoroastrian Co:operative Bank Limited", "icici_bank_paylater": "Icici Bank Paylater", "flexipay_by_hdfc_bank": "Flexipay By Hdfc Bank", "rupifipg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashe_by_potlee": "Cashe By Potlee", "earlysalary": "earlysalary", "product_has_been_added_to_cart": "Product has been added to cart", "product_is_not_deliverable_at_your_location": "Product is not deliverable at your location", "product_is_out_of_stock": "Product is out of stock", "item_updated_to_cart": "Item updated to cart", "item_removed_from_cart": "Item removed from cart", "incorrect_email_address": "Incorrect email address", "login": "LOGIN", "out_of_stock": "Out of stock", "product_is_not_serviceable_at_given_pincode": "Product is not serviceable at given pincode", "the_selected_address_does_not_match_with_the_shipping_country": "The selected address does not match with the shipping country", "your_payment_has_been_cancelled_try_again_or_complete_the_payment_later": "Your payment has been cancelled. Try again or complete the payment later", "please_try_again_or_use_a_different_payment_method_to_complete_payment": "Please try again or use a different payment method to complete payment", "entered_otp_is_invalid_please_retry": "Entered OTP is invalid, please retry", "password_or_username_is_incorrect": "Password or username is incorrect", "friends_family": "Friends & Family", "this_account_has_been_disabled": "This account has been disabled", "work": "Work", "home": "Home", "read_more": "Read More", "last_30_days": "Last 30 days", "last_6_months": "Last 6 months", "last_12_months": "Last 12 months", "last_24_months": "Last 24 months", "house_no_is_required": "House No. is required", "house_no_can_only_contain_letters_numbers_comma_period_hyphen_and_slash": "House No can only contain letters, numbers, comma, period, hyphen, and slash", "can_not_exceed_80_characters": "Can not exceed 80 characters", "building_name__street": "Building Name/ Street", "building_name_or_street_is_required": "Building name or street is required", "address_can_only_contain_letters_numbers_comma_period_hyphen_and_slash": "address can only contain letters, numbers, comma, period, hyphen, and slash", "locality__landmark": "Locality/ Landmark", "pincode_is_required": "Pincode is required", "invalid_pincode": "Invalid pincode", "can_not_exceed_6_digits": "Can not exceed 6 digits", "city_is_required": "City is required", "city_can_only_contain_letters": "City can only contain letters", "city_cannot_exceed_50_characters": "City cannot exceed 50 characters", "state_is_required": "State is required", "state_can_only_contain_letters": "State can only contain letters", "state_cannot_exceed_50_characters": "State cannot exceed 50 characters", "name_is_required": "Name is required", "name_can_only_contain_letters": "Name can only contain letters", "name_cannot_exceed_50_characters": "Name cannot exceed 50 characters", "mobile_number_is_required": "Mobile number is required", "invalid_mobile_number": "Invalid Mobile Number", "invalid_email_address": "Invalid email address", "invalid_email": "Invalid email", "email_cannot_exceed_50_characters": "Email cannot exceed 50 characters", "other": "Other", "product_is_not_serviceable_at_given_locality": "Product is not serviceable at given locality", "no_items_in_cart": "No items in cart", "street_name": "Street name", "building_no__apartment": "Building No./ Apartment", "area": "Area", "landmark": "Landmark", "sorry_item_is_out_of_stock": "Sorry, item is out of stock", "added_only_those_products_that_are_in_stock": "Added only those products that are in stock", "max_50_items_allowed_in_cart": "Max 50 items allowed in cart", "nothing_updated": "Nothing Updated", "item_updated": "Item updated", "item_not_updated": "Item not updated", "invalid_address": "Invalid address"}, "auth": {"account_deletion_notice": "As per your request, your account will be deleted soon. If you wish to restore your account, please contact on below support email id", "account_locked_message": "Your Account is locked", "alt_brand_banner": "brand banner", "back_to_login": "Back to login", "click_link_to_verify_email": "Please click on the link that has been sent to your email account to verify your email and continue with the registration process.", "confirm_new_password": "Confirm New Password", "confirm_password": "Confirm Password", "create_new_password": "Create New Password", "email_optional": "Email (optional)", "hide_confirm_password": "Hide confirm password", "login": {"agree_to_terms_prompt": "By continuing, I agree to the", "desktop_logo_alt": "Logo Image Desktop", "email_or_phone": "Email or Phone", "forgot_password": "Forgot Password?", "get_otp": "GET OTP", "go_to_login": "GO TO LOGIN", "hide_password": "Hide Password", "login": "<PERSON><PERSON>", "login_caps": "LOGIN", "login_to_shop": "Login to Shop", "login_with_caps": "LOGIN WITH", "mobile_logo_alt": "Logo Image Mobile", "otp": "OTP", "password": "Password", "password_caps": "PASSWORD", "please_login_first": "Please Login first.", "privacy_policy": "Privacy Policy", "show_password": "Show Password", "terms_of_service": "Terms of Service", "agree_to_the": "I agree to the", "and_symbol": "&"}, "new_password": "New Password", "password_does_not_match": "Password does not match", "password_requirements": "Password must be at least 8 characters and contain at least 1 letter, 1 number and 1 special character.", "resend_email": "RESEND EMAIL", "reset_password_caps": "RESET PASSWORD", "reset_your_password": "Reset Your Password", "set_password": "Set Password", "show_confirm_password": "Show confirm password", "verification_link_sent_to": "A verification link has been sent to", "verify_email": "<PERSON><PERSON><PERSON>", "verify_mobile": "Verify Mobile", "please_enter_a_valid_password": "Please enter a valid password", "password_didnt_match_try_again": "Password didn’t match. Try again", "continue_shopping_caps": "CONTINUE SHOPPING", "terms_and_condition": "To continue, please accept our Terms of Service & Privacy Policy", "terms_and_privacy": "Accept Terms and Privacy Policy"}, "common": {"fynd": "<PERSON><PERSON><PERSON>", "fynd_trusted_gateway": "Fynd Trusted Gateway", "amount": "AMOUNT", "delivery_custom_fees_notice": "Local taxes, duties or custom clearance fees may apply on delivery", "password_reset_successful": "Password reset successful", "saved": "Saved", "result_for": "Results for", "loading": "Loading...", "see_all": "SEE ALL", "shipment_id": "Shipment ID", "no_product_found": "No Product Found", "select_payment_option": "Select a payment option to place order", "error_numbers_not_allowed": "Numbers are not allowed", "error_validating_ifsc": "Error while validating IFSC Code", "invalid_ifsc_code": "Invalid IFSC Code", "deliver_to": "Deliver To", "minutes": "minutes", "empty_state": "No results found", "maximum_30_characters_allowed": "Maximum 30 characters allowed", "why": "WHY?", "card": "Card", "enter": "Enter", "delivery": "Delivery", "sorry_no_results_found": "Sorry, we couldn't find any results", "retry_checkout_or_other_payment_option": "You can retry checkout or take another option for payment", "oops_payment_failed": "Oops! Your payment failed!", "retry_caps": "RETRY", "enquiry_submitted": "Enquiry Submitted", "invalid_input": "Invalid input", "friends_&_family": "Friends & Family", "work": "Work", "home": "Home", "breadcrumb": {"home": "Home", "brands": "Brands", "categories": "Categories", "collections": "Collections", "products": "Products", "wishlist": "Wishlist", "blog": "Blog"}, "email_cannot_exceed_50_characters": "Email cannot exceed 50 characters", "invalid_mobile_number": "Invalid Mobile Number", "mobile_number_required": "Mobile number is required", "name_cannot_exceed_50_characters": "Name cannot exceed 50 characters", "name_can_only_contain_letters": "Name can only contain letters", "name_is_required": "Name is required", "state_cannot_exceed_50_characters": "State cannot exceed 50 characters", "state_can_only_contain_letters": "State can only contain letters", "state_is_required": "State is required", "state": "State", "city_cannot_exceed_50_characters": "City cannot exceed 50 characters", "city_can_only_contain_letters": "City can only contain letters", "city_is_required": "City is required", "city": "City", "cannot_exceed_6_digits": "Can not exceed 6 digits", "invalid_pincode": "Invalid pincode", "pincode_is_required": "Pincode is required", "pincode": "Pincode", "locality_landmark": "Locality/ Landmark", "address_validation_msg": "address can only contain letters, numbers, comma, period, hyphen, and slash", "building_name_street_required": "Building name or street is required", "building_name_street": "Building Name/ Street", "cannot_exceed_80_characters": "Can not exceed 80 characters", "house_no_validation_msg": "House No can only contain letters, numbers, comma, period, hyphen, and slash", "house_number_required": "House No. is required", "house_flat_number": "Flat No/House No", "yes": "Yes", "no": "No", "confirm": "Confirm", "social_accounts": {"continue_as": "Continue as", "login_with_facebook": "Login with Facebook", "youtube": "Youtube"}, "address": {"no_address_added": "No Address Added", "add_address": "Add Address", "pincode_verification_failure": "Pincode verification failed", "is_required": "is required", "address_selection_failure": "Failed to select an address", "address_addition_failure": "Failed to add an address", "address_addition_success": "Address added successfully", "new_address_creation_failure": "Failed to create new address", "address_update_success": "Address updated successfully", "address_update_failure": "Failed to update an address", "address_deletion_success": "Address deleted successfully", "address_deletion_failure": "Failed to delete an address", "update_address": "Update Address", "update_address_caps": "UPDATE ADDRESS", "my_address": "MY ADDRESSES", "add_new_address": "Add New Address", "add_new_address_caps": "ADD NEW ADDRESS", "no_address_available": "No address available", "address_not_found": "Address not found!", "return_to_my_address": "RETURN TO MY ADDRESS", "edit_address": "Edit Address", "address_caps": "ADDRESS", "select_delivery_location": "Select delivery location", "pincode_delivery_time": "Please enter pincode to check delivery time", "valid_six_digit_pincode": "Please enter a valid 6-digit pincode", "pincode_six_digits_required": "Pincode must be exactly 6 digits long", "invalid_phone_number": "Invalid Phone Number", "enter_pincode": "<PERSON><PERSON>", "default_address": "<PERSON><PERSON><PERSON> Address", "other_address": "Other Address", "address_type": "Address Type", "make_this_my_default_address": "Make this my default address"}, "continue_shopping": "CONTINUE SHOPPING", "error_occurred": "Error Occured !", "error_message": "Something went wrong", "invalid": "Invalid", "invalid_regex": "Invalid regex pattern", "invalid_gstin": "Invalid gstin number", "page_not_found": "Page Not Found", "no_data_found": "No Data Found", "add_to_cart": "Add To Cart", "product_not_available": "PRODUCT NOT AVAILABLE", "buy_now_caps": "BUY NOW", "buy_now": "Buy Now", "not_available_contact_for_info": "Not available, contact us for more information", "contact_us_caps": "CONTACT US", "removed_success": "removed successfully", "updated_success": "Updated Successfully", "ticket_success": "Ticket created successfully", "error_occurred_submitting_form": "An error occurred while submitting the form", "other": "Other", "other_plural": "Others", "no_match_found": "No match found", "size_guide": "SIZE GUIDE", "select_size_caps": "SELECT SIZE", "view_more_details": "View more details", "invalid_block": "Invalid block", "no_options": "No options", "add_to_cart_success": "Added to <PERSON><PERSON>", "add_cart_failure": "Failed to add to cart", "wishlist_add_success": "Added to wishlist", "wishlist_remove_success": "Removed from Wishlist", "delivery_by": "Delivery by {{date}}", "brand": "Brand", "buy_again": "BUY AGAIN", "payment_mode": "PAYMENT MODE", "enter_reason": "Enter reason", "billing_caps": "BILLING", "track": "TRACK", "need_help": "NEED HELP", "download_invoice": "DOWNLOAD INVOICE", "shipment": "Shipment", "awb": "AWB", "max_quantity": "Max quantity", "min_quantity": "Min quantity", "single_piece": "Piece", "multiple_piece": "Pieces", "not_found_error": "Oops! Looks like the page you're looking for doesn't exist", "return_home": "Return to Homepage", "return_home_caps": "RETURN TO HOMEPAGE", "items_caps_plural": "ITEMS", "items_caps_singular": "ITEM", "price_summary": "PRICE SUMMARY", "discount_greeting_message": "Yayy!!! You've saved", "sale": "Sale", "ifsc_code": "IFSC Code", "enter_otp": "Enter OTP", "enter_valid_otp": "Please Enter Valid O<PERSON>", "mobile_number": "Mobile Number", "enter_valid_mobile_number": "Please Enter Valid Mobile Number", "back_to_top": "Back to top", "copy_link": "Copy Link", "checkout_amazing_product_message": "Check out this amazing product on fynd!", "copied": "<PERSON>pied", "share": "Share", "share_caps": "SHARE", "failed_to_send_reset_link": "Failed to send the reset link to your primary email address.", "reset_link_sent": "The reset link has been sent to your primary email address.", "and": "and", "with": "with", "selected": "selected", "seller": "<PERSON><PERSON>", "store": "Store", "available": "Available", "size": "Size", "comments": "Comments", "optional": "Optional", "optional_lower": "optional", "dont": "Don't", "continue": "Continue", "default": "<PERSON><PERSON><PERSON>", "items": "items", "item": "item", "mrp": "MRP", "quantity": "Quantity", "free": "FREE", "gift": "gift", "placeholder": "placeholder", "icons": "icons", "field_required": "This field is required", "by": "By", "save_as": "SAVE AS", "sold_by": "Sold by", "total": "Total", "pcs": "pcs", "filter": "Filter", "please_enter": "Please enter", "use_this": "Use This", "read_more": "READ MORE", "read_less": "READ LESS", "item_simple_text": "<PERSON><PERSON>", "item_simple_text_plural": "Items", "user_alt": "user", "validation_length": "must be between {{min}} and {{max}} characters", "my_orders": "My Orders", "phone_number": "Phone Number", "email_address": "Email Address", "my_address": "My Address", "male": "Male", "female": "Female", "go_to_register": "GO TO REGISTER", "required_lower": "required", "required": "Required", "mobile": "Mobile", "otp_sent_to": "OTP sent to", "resend_otp": "Resend OTP", "offers": "Offers", "out_of_stock": "Out of stock", "hurry_only_left": "Hurry! Only {{quantity}} Left", "applied": "Applied", "applied_caps": "APPLIED", "select_size": "Select Size", "or": "OR", "enter_valid_phone_number": "Please enter valid phone number", "change_caps": "CHANGE", "open": "Open", "set": "Set", "cm": "cm", "inch": "inch", "edit_lower": "edit", "pay_caps": "PAY", "enter_upi_id": "Enter UPI ID", "shipments_plural": "shipments", "shipments": "shipment", "qty": "Qty", "search_here": "Search Here", "full_name": "Full Name", "email": "Email", "invalid_email_address": "Invalid email address", "contact_us": "Contact Us", "complete_signup": "Complete Signup", "first_name": "First Name", "please_enter_valid_first_name": "Please enter a valid first name", "last_name": "Last Name", "please_enter_valid_last_name": "Please enter a valid last name", "please_enter_valid_email_address": "Please enter valid email address", "password_message": "Password must be at least 8 characters and contain at least 1 letter, 1 number and 1 special character", "enter_valid_username": "Please enter valid username", "reset_link_sent_to": "Reset Link has been sent to", "back_to_login_caps": "BACK TO LOGIN", "sorry_we_couldnt_find_any_results": "Sorry, we couldn’t find any results", "in": "in", "home_seo_description": "Shop online with ease and explore a wide variety of quality products, latest arrivals, and exclusive deals—all in one place"}, "categories": {"empty_state": "No category found"}, "payment_link": {"expired_text": "Payment Link Expired", "expired_description": "Sorry, your payment link has expired. Please request a new link to proceed with your payment.", "loading_text": "Loading the payment page, please hold on...", "loading_sub_text": "This will only take a few seconds", "success_text": "Payment Successful", "order_id": "Order ID"}, "collections": {"empty_state": "No collection found"}, "order": {"uploaded_media": "Uploaded Photos/Videos", "please_do_not_press_back_button": "Please do not press back button", "fetching_order_details": "Fetching Order Details", "return_accepted": "Your return request has been accepted. Refunds will be processed in selected refund option within 7 days of return pickup.", "polling": {"description": "Your order is pending. Please allow us a few minutes to confirm the status. We will update you via SMS or email shortly.", "pending": "Order Pending"}, "list": {"orders_count_suffix": "Orders", "orders_count_singular_suffix": "Order", "my_orders": "My Orders"}, "add_refund_account": "Add Refund Account", "new_payment_added_success": "New payment added successfully", "account_details": "Account Details", "arrived_at_your_location": "Arrived at your location", "delivery_in": "Delivery in {{time}} Minute{{time > 1 ? 's' : ''}}", "processing_your_order": "We are processing your order", "order_is_delivered": "Your order is delivered", "order_cancelled": "Order cancelled", "delivered": "Delivered on: {{time}}", "cancelled": "Cancelled on: {{time}}", "your_delivery_partner": "Your delivery partner", "order_date": "Order Date", "order_status": "Order Status", "single": "{{count}} Item", "multiple": "{{count}} Items", "single_piece": "{{count}} Piece", "multiple_piece": "{{count}} Pieces", "piece_count": "{{count}} Piece{{count > 1 ? 's' : ''}}", "enter_valid_ifsc_code": "Please Enter Valid IFSC Code", "account_number": "Account Number", "enter_valid_account_number": "Please Enter Valid Account Number", "confirm_account_number": "Confirm Account Number", "re_enter_valid_account_number": "Please Re-Enter <PERSON> Account Number", "account_holder_name": "Account Holder Name", "account_holder_name_validation": "Please Enter <PERSON> Account Holder Name", "enter_valid_upi_id": "Please Enter Valid UPI ID", "otp_sent_success": "OTP sent successfully", "aria_label_dec_quantity": "Dec Quantity", "aria_label_inc_quantity": "Inc Quantity", "enter_order_id": "Enter Order ID", "track_order": "Track Order", "invalid_order_id": "Invalid Order Id", "where_is_my_order": "Where is my order", "track_order_caps": "TRACK ORDER", "where_is_order_id": "Where is Order Id", "john_doe": "<PERSON>", "status": "Status", "placed_caps": "PLACED", "gift_wrap_added": "Gift wrap Added", "order_confirmed_caps": "ORDER CONFIRMED", "order_success": "Thank you for shopping with us! Your order is placed successfully", "order_id_caps": "ORDER ID", "placed_on": "Placed on", "cod": "COD", "delivery_address": "DELIVERY ADDRESS"}, "verify_email": {"email_success": "Email Successfully Verified", "code_expired": "Code Expired / Invalid Request"}, "section": {"brand": {"default_brands": {"brand1": "Brand1", "brand2": "Brand2", "brand3": "Brand3", "brand4": "Brand4", "brand5": "Brand5"}}, "cart": {"order_on_behalf": "Placing order on behalf of Customer", "empty_state_title": "There are no items in your cart", "your_bag": "Your Bag", "continue_as_guest": "Continue as Guest", "continue_as_guest_caps": "CONTINUE AS GUEST", "checkout_button": "checkout", "checkout_button_caps": "CHECKOUT"}, "product": {"icon_alt_text": "icon", "check_out_amazing_product_on": "Check out this amazing product on"}, "categories": {"default_categories": {"chair": "Chair", "sofa": "So<PERSON>", "plants_and_flowers": "Plants & Flowers", "bags": "Bags"}}, "collection": {"featured_products": "Featured Products", "new_arrivals": "New Arrivals", "best_sellers": "Best Sellers"}, "order": {"empty_state_title": "Shipment details not found.", "empty_state_desc": "Shipment details are not available. This section might be misplaced and ideally should be on the shipment details page where a shipment ID is provided, or the data might not be found.", "emptybtn_title": "RETURN TO ORDER PAGE"}, "testimonials": {"add_customer_review_text": "Add customer reviews and testimonials to showcase your store's happy customers."}}, "facets": {"check": "CHECK", "view_less": "View Less", "view_less_caps": "VIEW LESS", "view_less_lower": "view less", "view_more": "View More", "view_more_lower": "view more", "apply": "Apply", "apply_caps": "APPLY", "close_alt": "close", "warning_alt": "Warning", "reset": "Reset", "reset_caps": "RESET", "close_esc": "Close (Esc)", "next": "Next", "view_all": "VIEW ALL", "pick": "Pick", "search": "Search", "from": "From", "to": "To", "sort_by": "Sort by", "cancel": "Cancel", "cancel_caps": "CANCEL", "return": "Return", "return_caps": "RETURN", "save_caps": "SAVE", "save": "Save", "remove": "Remove", "remove_caps": "REMOVE", "add": "Add", "add_caps": "ADD", "more": "more", "verify": "Verify", "submit": "SUBMIT", "submit_action": "Submit", "update": "UPDATE", "edit": "Edit", "clear_all_caps": "CLEAR ALL", "prev": "Prev", "reset_all": "Reset All", "filtering_by": "Filtering by"}, "localization": {"select_language": "Select Language", "choose_location": "Choose your location", "choose_address_for_availability": "Choose your address location to see product availability and delivery options", "select_country": "Select country", "invalid_country": "Invalid country", "select_currency": "Select currency", "invalid_currency": "Invalid currency", "provide_valid_time": "Please provide a valid time.", "select_delivery_option": "Please select at least one delivery option", "delivery_not_match": "Delivery option didn't match", "india": "India", "country": "Country", "other_address_type": "Other Address Type", "search_google_maps": "Search Google Maps", "detect_my_location": "Detect My Location", "check_pincode_availability": "Enter pincode to check availability", "use_current_location": "use my current location", "verify_account": "Verify Account"}, "wishlist": {"product_removed": "Products Removed From Wishlist", "no_product_in_wishlist": "You do not have any product added to wishlist", "add_products_to_wishlist": "Add products to wishlist"}, "cart": {"no_items": "There are no items in cart", "size_is_out_of_stock": "Size is out of stock", "replace_bag": "Replace Bag", "add_to_bag": "Add To Bag", "merge_bag": "<PERSON><PERSON>", "shared_bag": "Shared bag", "no_coupon_applied": "No coupon applied", "offers_and_coupons": "Offers & Coupons", "empty_shopping_bag": "Your Shopping Bag is empty", "cart_update_success": "Cart is updated", "view_all_offers": "View all offers", "coupons_title": "COUPONS", "link_copied": "<PERSON> Copied to Clipboard", "failed_to_action_cart": "Failed to {{action}} the cart", "cart_action_successful": "Cart {{action}}d successfully", "cart_item_addition_success": "Cart Items added successfully", "free_gift_applied": "Free Gift Applied", "custom_page_description": "This is a custom page for Cart in flow", "one_offer": "1 Offer", "item_not_deliverable": "Item Not Deliverable", "free_gift_added": "free gift added", "t&c": "T&C", "product_not_available": "This Product is not available", "add_comment": "Add Comment", "add_comment_caps": "ADD COMMENT", "specific_instructions_prompt": "Want to provide any specific instructions?", "placeholder_specific_comment": "Have any specific comment?...", "comment_character_limit": "Comment should be within 500 characters", "have_any_specific_instructions": "Have any specific instructions...", "apply_coupon": "Apply Coupon", "apply_coupons": "Apply Coupons", "you_have_saved": "You've saved", "remove_coupon": "Remove coupon", "open_coupon_drawer": "Open coupon drawer", "enter_coupon_code": "Enter Coupon Code", "select_applicable_coupons": "Select from Applicable Coupons", "coupon_success": "coupon-success", "savings_with_this_coupon": "savings with this coupon", "wohooo": "WOHOOO", "no_coupons_available": "No coupons available", "coupon_code_prompt": "If you have a coupon code try typing it in the coupon code box above", "check_delivery_time_services": "Check delivery time & services", "change": "Change", "enter_pin_code": "Enter PIN Code", "delivery_pin_code": "Delivery PIN Code", "please_enter_valid_pincode": "Please enter valid pincode", "change_address": "Change Address", "select_this_address": "select this address", "use_gst": "Use GST", "enter_gstin": "Enter GSTIN", "gstin_applied_success": "GSTIN Applied Successfully!!! Claimed {{gst_credit}} GST input credit", "enter_gst_number": "Enter GST number to claim {{gst_credit}} input credit", "remove_item": "Remove Item", "confirm_item_removal": "Are your sure you want to remove this item?", "move_to_wishlist": "MOVE TO WISHLIST", "share_shopping_cart_caps": "SHARE SHOPPING CART", "share_bag_caps": "SHARE BAG", "share_shopping_qr": "Spread the shopping delight! Scan QR & share these products with your loved ones", "redeem_rewards_points_worth": "Redeem Rewards Points Worth", "total_price": "Total Price", "view_bill": "View Bill", "view_price_details": "View Price Details", "pay_now": "PAY NOW", "add_to_cart_caps": "ADD TO CART", "merge": "merge", "replace": "replace"}, "compare": {"product_comparison_limit": "You can only compare 4 products at a time", "cannot_compare_different_categories": "Can't compare products of different categories", "add_to_compare": "Add to Compare", "go_to_compare": "Go to Compare", "fetch_suggestion_failure": "Something went wrong, unable to fetch suggestions!", "compare_products": "Compare Products", "add_products_to_compare": "Add Products To Compare", "max_four_products_allowed": "You can only add four products at a time", "search_product_here": "Search Product here"}, "product": {"before_cart_validate_pincode": "Please enter a valid {{displayName}} before Add to Cart/Buy Now", "delivery_on": "Will be delivered on {{date}}", "delivery_between": "Will be delivered between {{minDate}} - {{maxDate}}", "delivery_at": "Delivery at", "check_delivery_time": "Check delivery time", "enter_valid_pincode": "Please enter valid pincode before Add to cart/ Buy now", "made_to_order": "Made to Order", "image": "Image", "best_offers": "Best Offers", "coupons": "Coupons", "promotions": "Promotions", "no_items_available": "No {{activeTab}} available", "best_offers_caps": "BEST OFFERS", "product_description": "Product Description", "product_highlights": "Product Highlights", "interested_in": "Interested in", "style_size": "Style : <PERSON>ze", "size_guide_lower": "Size guide", "select_sellers": "Select Sellers", "select_stores": "Select Stores", "amazing_product": "Amazing Product", "no_return_available_message": "No return available on this product", "shipping_within": "Shipping within", "item_code": "Item code", "product_not_serviceable": "Product is not serviceable at given locality", "select_valid_delivery_location": "Please select a valid delivery location.", "select_size_first": "Please select the size first.", "how_to_measure": "How to measure", "max_quantity": "Maximum quantity is", "min_quantity": "Minimum quantity is", "wishlist_icon": "Wislist <PERSON><PERSON>", "remove_icon": "Remove Icon", "min_value_should_be": "The minimum value should be", "min_value_cannot_exceed": "The minimum value cannot exceed", "max_value_should_be": "The maximum value should be", "max_value_should_be_greater_than": "The maximum value should be greater than", "products_found": "Products Found", "style": "Style", "select_size_to_continue": "Please select size to continue", "view_full_details": "View Full details", "previous_text": "previousText", "mobile_grid_one": "Mobile grid one", "mobile_grid_two": "Mobile grid two", "tablet_grid_two": "Tablet grid two", "tablet_grid_four": "Tablet grid four", "filters_caps": "FILTERS", "desktop_grid_two": "Desktop grid two", "desktop_grid_four": "Desktop grid four", "desktop_banner_alt": "desktop banner", "mobile_banner": "mobile banner", "please_select_size": "Please Select Size", "seo_description": "Browse our collection featuring top-rated, trending, and new products. Enjoy easy filtering, fast delivery, and great value on every item", "inch": "inch", "cm": "cm"}, "profile": {"return_request": "Return Request", "cancel_request": "Cancel Request", "phone_number_verified": "Phone Number verified", "saved": "saved", "gender": "Gender", "confirm_remove_number": "Are you sure you want to remove the number?", "confirm_remove_email": "Are you sure you want to remove the email?", "set_primary": "Set Primary", "primary": "Primary", "verified": "Verified", "verification_link_sent_to": "Verification link sent to", "set_as_primary": "set as primary", "otp_sent_mobile": "OTP sent on mobile", "otp_verified": "OTP verified", "select_one_refund_option": "Please select any one refund option", "add_payment_method": "Please add a payment method", "max_4_images_allowed_upload": "Maximum 4 images are allowed to upload", "max_1_video_allowed_upload": "Maximum 1 video is allowed to upload", "min_2_images_required_upload": "Minimum 2 images are required to upload", "image_size_max_5mb": "Image size should not be more than 5MB", "video_size_max_25mb": "video size should not be more than 25MB", "valid_file_formats_required": "Only JPG,PNG images and MOV,MP4 videos are supported. Please upload a valid file.", "write_reason_for_cancellation": "Please write a reason for cancellation, as it will help us serve you better", "select_one_reason_below": "Please select any one of the below reason", "select_item_to": "Please select an item to", "reason_for": "Reason for", "more_details": "More Details", "select_refund_option": "Select refund option", "add_product_images": "Add product images", "add_images_videos": "Upload Images / Videos", "ensure_product_tag_visible": "Make sure the product tag is visible in the picture.", "accepted_image_formats_and_size": "Accepted image formats jpg & png , File size should be less than 5mb", "accepted_video_formats_and_size": "Accepted Video formats MP4, MOV , File size should be less than 25mb", "profile_page_description": "This is a custom page for Profile in flow", "edit_profile": "Edit Profile", "my_account": "My Account", "sign_out": "Sign Out", "logout": "Logout", "skip_caps": "SKIP", "add_email": "Add <PERSON>", "placeholder_email": "<EMAIL>", "please_enter_a_email_address": "Please enter a email address", "add_number": "Add Number", "verify_number": "Verify Number", "countdown_in_seconds": "in {{count}}s", "send_otp": "Send OTP", "enter_your_email_address": "Enter your email address", "no_email_address_added": "No Email Address Added", "add_email_address": "Add Email Address", "no_phone_number_added": "No Phone Number Added", "add_phone_number_caps": "ADD PHONE NUMBER", "profile": "Profile"}, "footer": {"footer_logo_alt_text": "Footer <PERSON>", "email_id": "Email ID", "social_media": "Social Media", "payment_logo_alt_text": "Payment Logo"}, "header": {"location_access_failed": "Location access failed. Enter pincode manually", "language_is_required": "Language is reuqired", "fetching": "Fetching...", "pin_code": "Enter a pincode", "shop_logo_alt_text": "Name", "signin": "Sign in", "account_text": "Account", "shop_logo_mobile_alt_text": "logo", "products_title_text": "PRODUCTS", "product_not_serviceable": "Product not serviceable", "delivery_time_in_mins": "Delivery in {{minutes}} Minutes", "delivery_time_in_hours": "Delivery in {{hours}} Hours", "delivery_by_today": "Delivery by Today", "delivery_by_tomorrow": "Delivery by Tomorrow", "geolocation_not_available": "Geolocation is not available.", "api_key_not_available": "API key not available.", "contact": "Contact", "comment": "Comment", "contact_request": "Contact Request", "open_navigation": "open navigation", "item_in_cart": "item in cart"}, "blog": {"top_viewed": "Top viewed", "recently_published": "Recently Published", "published": "Published", "follow_us": "Follow us", "slide_alt": "slide", "no_blogs_found": "No blogs found", "showing_results": "Showing {{count}} results of", "no_blog_found": "No Blog Found"}, "checkout": {"payment_retry_message": "Please try again or use a different payment method to complete payment", "payment_failed": "Payment Failed", "payment": "Payment", "summary": "Summary", "address": "Address", "continue_with_cod": "CONTINUE WITH COD", "extra_charges": "extra charges", "confirm_cod": "Are you sure you want to proceed with Cash on delivery?", "redirecting_upi": "Redirecting to your UPI App. Please do not press back button", "finalising_payment": "Finalising Payment", "cancel_payment_caps": "CANCEL PAYMENT", "sent_to": "Sent to", "complete_your_payment": "Complete Your Payment", "no_image": "No Image", "select_payment_option": "Select Payment Option", "select_emi_option": "Select EMI option", "select_pay_later_option": "Select Pay Later option", "place_order": "PLACE ORDER", "cod_extra_charge": "will be charged extra for Cash on delivery option", "pay_on_delivery": "Pay in cash or using UPI at the time of delivery", "cash_on_delivery": "Cash On Delivery (Cash/UPI)", "search_for_banks": "Search for Banks", "select_bank": "Select Bank", "save_upi_id": "Save UPI ID for future use", "upi_id_number": "UPI ID / Number", "invalid_upi_id": "Invalid UPI ID", "saved_upi_id": "Saved UPI ID", "show_qr": "SHOW QR", "valid_for": "Valid for", "and_more": "& more", "scan_qr_upi": "Scan the QR code using any UPI app on your phone", "scan_qr_to_pay": "<PERSON>an QR to pay", "upi_qr_code_caps": "UPI QR CODE", "search_for_wallets": "Search for Wallets", "other_wallets": "Other Wallets", "select_wallet": "Select Wallet", "enter_card_details": "Enter card details", "amex_cvv_description": "It is a 4-digit number on the front, just above your credit card number", "have_american_express_card": "Have American Express Card?", "cvv_description": "It is a 3-digit code on the back of your card", "what_is_cvv_number": "What is CVV Number?", "card_network_not_supported": "Card Network not supported", "qr_code_generation_failed": "Something went wrong while generating QR code", "please_try_again_later": "Please try again later", "pay_online": "Pay Online", "more_payment_options": "More Payment Options", "more_apps": "More Apps", "paytm_upi": "Paytm UPI", "phonepe_upi": "PhonePe UPI", "google_pay": "Google Pay", "enter_expiry_date": "Enter Expiry Date", "this_card_network_is_not_supported": "This card network is not supported", "invalid_card_number": "Invalid card number", "select_payment_method": "Select Payment Method", "card_saved_rbi": "You card is saved as per new RBI guidelines and does not require a CVV for making this payment", "cvv_not_needed": "CVV not needed", "new_card": "New Card", "saved_cards": "Saved Cards", "please_enter_correct_upi_id": "Please enter correct UPI ID", "card_tokenization_benefits": "Tokenization is the safest way to secure the card. It provides the below advantages.", "rbi_icon": "rbi-icon", "secured_payment_method": "Secured payments method", "quick_payment_no_card_details": "Quick payments without entering card details frequently", "rbi_card_data_tokenization_notice": "As stated by RBI, card data will be tokenised, and safeguarded with card networks assuring card details are not being exposed.", "secure_my_card_yes": "Yes, secure my card", "maybe_later": "Maybe later", "deliver_to_this_address": "DELIVER TO THIS ADDRESS", "update_pincode_or_remove_items": "Try changing the pincode or remove the non deliverable items", "edit_cart": "Edit CART", "no_address_found": "No Address Found, Please Add Address", "delivery_address": "Delivery Address", "select_delivery_address": "Select delivery address", "enter_cvv": "Enter CVV", "invalid_cvv": "Invalid CVV", "expiry_date_passed": "The expiry date has passed", "invalid_expiry_time": "Expiry time is invalid", "card_verification_failed": "Please enter the correct card details", "saved_credit_debit_cards": "Saved Credit/Debit Cards", "secured": "secured", "cvv": "CVV", "save_card_rbi_guidelines": "Save my card as per RBI Guidelines", "add_new_card": "Add New Card", "pay_with_credit_debit_card": "Pay Using Credit/Debit Card", "card_number": "Card Number", "name_on_card": "Name on Card", "expiry_date_mm_yy": "Expiry Date (mm/yy)", "select_wallet_to_pay": "Select Wallet To Pay", "select_a_bank": "Select a Bank", "other_banks": "Other Banks", "pay_in_cash": "Pay In Cash", "additional": "Additional", "cash_collection_fee": "will be charged for cash collection", "choose_an_option": "Choose An Option", "payment_method": "Payment Method", "select_a_payment_method": "Select a payment method", "order_summary": "Order Summary", "edit_cart_lower": "<PERSON>", "proceed_to_pay": "Proceed To Pay", "proceed_to_pay_caps": "PROCEED TO PAY", "expiry_date": "Expiry Date", "save_this_card_rbi_guidelines": "Save this card as per RBI Guidelines", "improve_your_card_security": "Improve your card security", "card_consent_request_1": "Your bank/card network will securely save your card info via tokenization if you give consent for the same.", "card_consent_request_2": "In case you choose to not tokenize, you will have to enter the card details every time you pay."}, "contact_us": {"please_enter_your_name": "Please enter your name", "message": "Message", "please_enter_your_comment": "Please enter your comment", "send_message": "SEND MESSAGE", "please_enter_a_valid_name": "Please enter a valid name", "please_enter_a_valid_phone_number": "Please enter a valid phone number", "please_enter_your_phone_number": "Please enter your phone number", "please_enter_a_valid_email_address": "Please enter a valid email address", "please_enter_your_email_address": "Please enter your email address"}, "faq": {"no_frequently_asked_questions_found": "No Frequently Asked Questions Found", "frequently_asked_questions": "Frequently Asked Questions", "still_need_help": "Still need help"}, "brand": {"no_brand_found": "No brand found"}, "refund_order": {"credit_time_message": "Refund Amount will be credited in 7 to 10 working days", "account_numbers_do_not_match": "Account Numbers Do Not Match", "account_number_should_not_exceed_18_digits": "Account Number Should Not Exceed 18 Digits", "account_number_should_be_at_least_9_digits": "Account Number Should Be At Least 9 Digits", "account_number_should_contain_only_numbers": "Account Number Should Contain Only Numbers", "account_number_is_required": "Account Number is Required", "account_holder_name_should_not_exceed_50_characters": "Account Holder Name Should Not Exceed 50 Characters", "account_holder_name_should_be_more_than_5_characters": "Account Holder Name Should Be More than 5 Characters", "special_characters_not_allowed_in_account_holder_name": "Special Characters Are Not Allowed in Account Holder Name", "account_holder_required": "Account Holder Name is Required", "numbers_not_allowed_in_account_holder_name": "Numbers Are Not Allowed in Account Holder Name", "resend_otp_in_seconds": "Resend OTP in {{time}} Sec", "resend_otp": "Resend OTP", "otp_must_be_4_digit_number": "OTP must be a 4-digit number", "otp_must_be_4_digits": "OTP must be 4 digits long", "otp_is_required": "OTP is required", "beneficiary_added": "Beneficiary Added", "invalid_refund_link": "Invalid refund link", "name_alt_text": "name", "select_refund_option": "Select Refund Option", "bank_account_transfer": "Bank Account Transfer", "recently_used": "Recently Used", "add_bank_account": "Add Bank Account", "bank_account": "Bank Account", "account_no": "Account No", "amount_credited_to_beneficiary": "Amount will be credited to the below beneficiary", "beneficiary_details": "Beneficiary Details", "account_holders_name": "Account Holder's Name", "bank_account_no": "Bank Account No.", "order_id": "Order ID", "otp_sent_to_phone": "One Time Password (OTP) successfully sent to the phone number", "verify_caps": "VERIFY", "enter_bank_details": "Enter Bank Details"}}}