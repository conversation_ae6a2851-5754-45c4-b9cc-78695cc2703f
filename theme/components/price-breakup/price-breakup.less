@import "../../styles/main.less";

.priceSummaryContainer {
  border: 1px solid @DividerStokes;
  line-height: 140%;
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  @media @mobile {
    padding: 16px 0;
    border-width: 1px 0;
  }
  .priceSummaryHeading {
    font-size: 12px;
    font-weight: 500;
    color: @TextHeading;
    padding: 0 24px;
    font-family: "Helvetica Medium" !important;
    @media @mobile {
      padding: 0 16px;
    }
  }
  .priceSummaryItem {
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    margin-top: 16px;
    font-size: 12px;
    font-weight: 500;
    color: @TextBody;
    font-family: "Helvetica Medium" !important;
    @media @tablet {
      margin-top: 12px;
      padding: 0 16px;
    }
  }
  .priceSummaryItemTotal {
    color: @TextHeading;
    border-top: 1px solid @DividerStokes;
    margin-top: 16px;
    font-size: 14px;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 700;
    padding: 24px 24px 0;
    font-family: "Helvetica Medium" !important;
    @media @mobile {
      padding-inline-end: 16px;
      padding-inline-start: 16px;
    }
  }
  .internationalTaxLabel {
    color: @TextBody;
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    display: flex;
    gap: 12px;
    padding: 16px 24px 0;
    font-family: "Helvetica Medium" !important;
    @media @mobile {
      padding: 12px 16px 0;
      font-size: 12px;
      gap: 8px;
    }

    .infoIcon {
      flex: 0 0 20px;
    }
  }
  .discountPreviewContiner {
    background-color: @SuccessBackground;
    color: @SuccessText;
    border-radius: 8px;
    box-sizing: border-box;
    padding: 8px 0;
    margin: 16px 24px 0;
    font-family: "Helvetica Medium" !important;
    @media @mobile {
      margin: 12px 16px 0;
    }
    font-size: 14px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    @media @tablet {
      font-size: 12px;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 21px;
      height: 20px;
    }

    .discountPreviewMessage {
      margin-inline-start: 4px;
      font-weight: 400;
      font-family: "Helvetica Medium" !important;
    }
    .discountPreviewAmount {
      margin-inline-start: 4px;
      font-weight: 600;
      font-family: "Helvetica Medium" !important;
    }
  }
}
.discount {
  color: @SuccessText;
}
