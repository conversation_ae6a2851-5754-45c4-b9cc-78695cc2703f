import React from 'react';
import { CiHeart } from "react-icons/ci";
import orangehoodie from "../../assets/images/orangehoodie.png";
import ColorSelector from './colorSelector';
import ProductHeart from './productHeart';

const ProductCard = () => {
    return (
        <>

            <div className="max-w-xs overflow-hidden">



                {/* Image Section */}
                <div className="relative w-[100%]">
                    <img
                        src={orangehoodie} // replace with your image
                        alt="Product"
                        className="w-full object-cover rounded-[20px]"
                    />

                    {/* Best Seller Badge */}
                    <div
                        className="inline-flex items-center justify-center px-[12px] py-[8px] rounded-[17px] bg-[#1A1A1A] absolute bottom-[16px] left-[16px]"
                    >
                        <p
                            className="text-white text-[14px] font-medium leading-[130%] font-['Helvetica Now Display',sans-serif]"
                        >
                            Best Seller
                        </p>
                    </div>

                    {/* Heart Icon */}
                    <div className="absolute top-2 right-2 p-1 rounded-full">
                        <ColorSelector />
                    </div>
                    <div className='absolute bottom-[16px] right-[16px] '>
                        <ProductHeart />
                    </div>
                </div>

                {/* Details Section */}
                <div className="py-[4px] px-[12px] mt-[16px] w-[100%]">
                    {/* Product Name */}
                    <div className=''>
                        <p className=" font-semibold text-[18px] text-[#666666] font-bold leading-[130%] font-['Helvetica Now Display',sans-serif]">
                            Code Essential Polo Shirt
                        </p>
                    </div>

                    {/* Pricing Info */}
                    <div className="flex items-center gap-[15px] text-sm mt-[17px]">
                        <span className="font-bold text-[#1A1A1A] text-[16px] font-medium">₹5,999</span>
                        <span className="line-through text-[#999999] text-[16px] font-medium">₹7,999</span>
                        <span className="text-[#FF1E00] font-semibold text-[16px] font-medium ">25% OFF</span>
                    </div>
                </div>
            </div>


        </>
    );
};

export default ProductCard;
 