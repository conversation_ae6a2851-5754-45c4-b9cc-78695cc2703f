import React from "react";

const ColorSelector = () => {
  return (
    <div className="relative w-12 ">
      {/* Blue Circle */}
      <div className="absolute left-0 top-0 w-[18px] h-[18px] rounded-full bg-blue-600 border-2 border-white"></div>
      {/* Yellow Circle */}
      <div className="absolute left-[10px] top-0 w-[18px] h-[18px] rounded-full bg-yellow-400 border-2 border-white"></div>
      {/* Red Circle */}
      <div className="absolute left-[20px] top-0 w-[18px] h-[18px] rounded-full bg-red-500 border-2 border-white"></div>
    </div>
  );
};

export default ColorSelector;
