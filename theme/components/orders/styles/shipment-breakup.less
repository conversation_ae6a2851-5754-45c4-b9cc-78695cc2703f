@import "../../../styles/main.less";

.title {
  color: @Mako;
}
.lightsm {
  font-weight: 300;
  font-size: 16px;
  @media @tablet {
    font-size: 14px;
  }
  @media @mobile {
    font-size: 14px;
  }
}
.boldsm {
  font-weight: 700;
  font-size: 16px;
  @media @tablet {
    font-size: 14px;
  }
  @media @mobile {
    font-size: 14px;
  }
}
.billing {
  padding: 10px;
  position: relative;
  div {
    margin: 15px;
  }
  .values {
    position: absolute;
    right: 30px;
    font-weight: 400;
  }

  .total {
    padding-top: 10px;
    border-top: 1px solid @LightGray;
    font-weight: bold;
    color: @<PERSON><PERSON>;
  }
}
.paymentDetails {
  display: flex;
  align-items: center;
  .paymentLogo {
    margin: 0px !important;
    img {
      width: 50px;
    }
  }
  .rightAlign {
    position: relative;
    margin-left: auto;
    margin-right: 10px;
    right: 0px;
  }
}
