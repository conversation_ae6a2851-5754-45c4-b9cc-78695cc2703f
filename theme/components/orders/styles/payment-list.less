@import "../../../styles/main.less";

.paymentList {
  position: relative;
  min-height: 360px;
  .paymentListItem {
    display: flex;
    height: 60px;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid @LightGray;
    cursor: pointer;
    .paymentDetails {
      display: flex;
      align-items: center;
      .paymentLogo {
        width: 50px;
        margin-right: 10px;
      }
    }
    &:hover {
      color: @ButtonPrimary;
    }
  }
  .noBorder {
    border-bottom: none;
    margin-bottom: -15px;
  }
  .noHover {
    &:hover {
      color: @Black;
    }
  }
}
