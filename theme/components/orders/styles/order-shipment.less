@import "../../../styles/main.less";

.orderItem {
  color: @Mako;
  background-color: @Alabaster2;
  border-radius: 5px;
  margin: 20px 0px;
  padding: 10px;

  .orderHeader {
    cursor: pointer;
    position: relative;

    h3 {
      margin: 10px;
    }
    h4 {
      margin: 10px;
    }
    .filter {
      position: absolute;
      right: 20px;
    }
    .filterArrowUp {
      margin-left: auto;
      transform: rotate(180deg);
    }
    .filterArrowDown {
      margin-left: auto;
    }
    .bold {
      font-weight: 700;
      font-size: 20px;
      @media @tablet {
        font-size: 18px;
      }
      @media @mobile {
        font-size: 16px;
      }
    }
    .light {
      font-weight: 300;
      font-size: 13px;
      @media @tablet {
        font-size: 12px;
      }
      @media @mobile {
        font-size: 12px;
      }
    }
  }

  .shipmentData {
    animation: fadeIn 0.5s ease;
    border-top: none;
    padding: 10px;
    cursor: pointer;
    .shipmentLeft {
      display: table-cell;
      width: 125px;
      img {
        max-width: 110px;
        max-height: 180px;
      }

      #total-item {
        text-align: center;
        position: relative;
        float: right;
        background: @Mako2;
        color: white;
        top: -40px;
        left: -10px;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        line-height: 13px;
        font-size: 13px;
        padding: 10px;
      }
    }
    .bold {
      font-weight: 700;
      font-size: 14px;
    }
    .light {
      font-weight: 300;
      font-size: 16px;
      @media @tablet {
        font-size: 14px;
      }
      @media @mobile {
        font-size: 14px;
      }
    }
    .regular {
      font-weight: 400;
      font-size: 14px;
      @media @tablet {
        font-size: 13px;
      }
      @media @mobile {
        font-size: 13px;
      }
    }
    .shipmentRight {
      display: table-cell;
      vertical-align: top;
      color: @Mako;
      .shipmentId {
        margin: 10px 0px;
        line-height: 20px;
      }
      .boldls {
        font-weight: 700;
        font-size: 14px;
        @media @tablet {
          font-size: 13px;
        }
        @media @mobile {
          font-size: 13px;
        }
      }
      .uktLinks {
        color: @ButtonLink;
        font-weight: 700;
        cursor: pointer;
        text-decoration: none;
      }
      .shipmentStats {
        margin: 10px 0px;
      }
      .status {
        text-align: center;
        color: @White;
        padding: 10px;
        display: inline-flex;
        border-radius: 3px;
      }
      .shipmentBrands {
        margin: 10px 0px;
      }
    }
  }

  .buttons {
    display: flex;
    .ordercheckout {
      width: calc(100% - 20px) !important;
      height: 40px;
      margin: 10px;
      padding: 15px;
      border-radius: @ButtonRadius;
      margin: 10px;
      font-weight: 700;
      background-color: @TextHeading;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      text-align: center;
      color: @ThemeAccentL5;
      cursor: pointer;
      .reorderIcon {
        margin-right: 10px;
        position: relative;
        top: -2px;
      }
    }
  }
}
