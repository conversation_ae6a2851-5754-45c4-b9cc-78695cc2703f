@import "../../styles/main.less";
.scrollTop {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  color: white;
  box-shadow:
    0px 8px 8px -4px rgba(0, 0, 0, 0.6),
    0px 4px 6px -4px rgba(0, 0, 0, 0.12);
  background-color: black;
  border-radius: 24px;
  transition: top 0.25s ease-in-out;
  border: none;
  position: fixed;
  top: unset;
  bottom: 10%;
  right: 6%;
  z-index: 1;
  transform: translateX(50%);

  @media @desktop {
    display: none;
  }

  .scrollTopText {
    .captionNormal();
  }

  &:not(.isVisible) {
    display: none;
  }

  // .scrollTopIcon {

  //   .backTop {
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //     width: 40px;
  //     height: 40px;
  //   }

  //   svg {
  //     width: 100%;
  //   }

  //   display: none;

  //   @media @tablet {
  //     .backTop {
  //       display: block;
  //     }
  //   }
  // }

  @media @desktop {
    justify-content: center;
    top: unset;
    right: 40px;
    bottom: 40px;
    width: 40px;
    height: 40px;
    display: none;
    .backTop {
      display: none;
    }
    .scrollTopText {
      display: none;
    }

    .scrollTopIcon {
      width: 10px;
      height: 14px;
    }
  }
}
