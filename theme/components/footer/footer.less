@import "../../styles/main.less";

@lg-min: 1024px;

.footer {
  background-color: black;
  background-image: var(--background-desktop);

  @media @tablet {
    background-image: var(--background-mobile);
  }

  &__top,
  &__bottom {
    .footerContainer {
      max-width: @page-width;
      margin-left: auto;
      margin-right: auto;

      @media @tablet {
        display: none;
      }
    }
  }

  &__top {
    &--wrapper {
      @media @desktop {
        display: flex;
        flex-direction: column;
        .column-gap(64px);
        justify-content: space-between;
        margin-bottom: 32px;
      }
    }

    .footerContainer {
      padding: 16px;

      @media @desktop {
        padding: 100px 50px 0 50px;
      }
    }

    &--info {
      margin-bottom: 32px;

      @media @mobile-up {
        margin-bottom: 24px;
      }

      @media @desktop {
        flex: 0 1 586px;
        margin-bottom: 0;
      }

      .logo {

        // margin-bottom: 8px;
        &>img {

          // max-height: 25px;
          @media @desktop {
            // max-height: 36px;
            max-width: @page-width;
            // height: 258px;
            padding: 0 21px;
            margin: auto;
            width: 100%;
          }
        }
      }

      .description {
        // color: @FooterLinkBody;
        color: white;
        line-height: 24px;
      }
    }

    &--menu {
      font-size: 15px;

      @media @mobile-up {
        display: flex;
        justify-content: space-between;
        //.column-gap(119px);
      }

      @media @desktop {
        .column-gap(64px);
      }

      .linkBlock {
        margin-bottom: 0;

        @media @tablet-strict {
          flex: 1;
        }

        @media @desktop {
          margin-bottom: 0;
          max-width: 147px;
        }

        .menuTitle {
          // color: @FooterText;
          color: white;
          margin-bottom: 6px;
          font-family: "Helvetica Bold";

          @media @desktop {
            // margin-bottom: 24px;
            margin-bottom: 30px;
          }

          &>a:hover {
            opacity: 0.7;
          }
        }

        .menuItem {
          // color: @FooterLinkBody;
          color: #6D6D6D;
          margin-bottom: 6px;
          font-family: "Helvetica Medium";
          line-height: 120%;

          &>a:hover {
            opacity: 0.7;
          }

          @media @desktop {
            margin-bottom: 17px;
          }
        }
      }
    }

    &--contactInfo {
      border-radius: 12px;
      padding: 14px;
      margin-bottom: 48px;
      justify-content: space-between;
      align-items: center;
      font-family: "Helvetica Medium";

      // background-color: var(
      //   --footer-opacity-background,
      //   @FooterBottomBackground
      // );
      @media @desktop {
        display: flex;
        justify-content: space-between;
      }

      @media @desktop {
        padding: 14px 0px;
        // align-items: center;
      }

      .list {
        svg {
          width: 28px;
          height: 28px;

          // stroke: @FooterIcon !important;
        }

        svg>path:first-of-type,
        svg g>path:first-of-type {
          fill: #6B6B6B;
        }

        svg>path:nth-of-type(2),
        svg g>path:nth-of-type(2) {
          stroke: #4A4A4A;
          stroke-width: 1px;
        }

        @media @tablet-strict {
          flex: 1;
        }

        @media @desktop {
          display: flex;
          align-items: center;
          .column-gap(20px);
        }

        li {
          word-break: break-all;
        }
      }

      .list:not(:last-child) {
        @media @mobile {
          margin-bottom: 24px;
        }
      }

      .title {
        color: #4A4A4A;

        @media @tablet {
          margin-bottom: 8px;
        }
      }

      .detail {
        // color: @FooterLinkBody;
        color: white;
      }

      .social {
        flex-wrap: wrap;

        .column-gap(8px);

        .socialIcon {
          position: static !important;

          /deep/ svg {
            width: 28px;
            height: 28px;
            overflow: visible;

            &:hover {
              opacity: 0.7;
            }
          }
        }
      }
    }

    &--noBackground {
      background-color: unset;
      padding: 0;

      @media @desktop {
        // padding: 14px 0;
      }
    }
  }

  &__bottom {
    // background-color: var(--footer-opacity-background, @FooterBottomBackground);
    background-color: black;

    .footerContainer {
      display: flex;
      justify-content: center;
      align-items: center;

      @media @mobile-up {}

      @media @desktop {
        padding: 45px 50px;
      }

      .copyright {
        // color: @FooterLinkBody;
        color: #6D6D6D;
        font-size: 14px;
        font-family: "Helvetica Medium";

        @media @mobile {
          margin-bottom: 8px;
        }
      }

      .paymentLogo {
        padding: 9px 10px;

        &>img {
          max-height: 18px;

          @media @desktop {
            max-height: 24px;
          }
        }
      }
    }
  }

}

.footerSupportData {
  @media @tablet {
    @media @tablet {
      padding-bottom: 12px;
    }

    @media @desktop {
      display: flex;
      justify-content: left;
      gap: 12px;
      align-items: center;
    }
  }
}

.listSocial {
  @media @desktop {
    padding-bottom: 110px;
    display: flex;
    justify-content: space-between;
    align-items: end;
  }
}

.contactus {
  display: flex;
  flex-direction: column;
  gap: 16px;
  // margin: 0 14px;
  color: white;
}

.listData {
  display: flex;
  align-items: center;
  flex-direction: column;
  align-items: flex-start;

  @media @tablet {
    margin-bottom: 16px;
  }

  .footerSupportData {
    padding-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5px;

    &:last-child {
      padding-bottom: 0;
    }
  }
}

.socialContainer {
  .socialContainer {
    @media @desktop {
      // padding: 55px 0 110px 0;
      display: flex;
      align-items: center;
      justify-content: end;
    }
  }


  .socialTitle {
    @media @desktop {
      padding-right: 12px;
    }

    padding-right: 12px;
  }
}

// .footerIcon.vimeo g > path:first-of-type {
//   fill: transparent !important;
// }

ul {
  list-style: none;
}

@media @tablet-strict {
  /deep/ .flex-align-center {
    flex-wrap: wrap;
    max-width: 180px;
  }

  .list li {
    margin-bottom: 10.7px;
  }
}

.navmobile {
  display: flex;
  padding: 21px 24px 20px 24px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  z-index: 50;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.footer_dummy {
  display: flex;
  flex-direction: column;
}

.dummy {
  color: rgba(255, 255, 255, 0.70);
  font-size: 14px;
  font-family: "Helvetica Medium";
  padding-bottom: 70px;
}

.dummyforP {
  display: flex;
  flex-direction: column;
  margin-bottom: 37px;
}

.dummy2 {
  color: #C4C4C4;
  // leading-trim: both;
  text-box-edge: cap;
  font-family: "Helvetica Bold";
  font-size: 24px;
  line-height: 120%;
  /* 28.8px */
  letter-spacing: 0.48px;
}

// .email {
//   // display: flex;
//   // flex-direction: column;
//   // justify-content: flex-end;
//   // align-items: flex-start;
//   font-family: "Helvetica Medium";
//   align-self: stretch;
//   width: 336px;
//   height: 52px;
//   border-radius: 100px;
//   background-color: rgba(255, 255, 255, 0.10);
// }

.email_main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 336px;
  height: 52px;
  border-radius: 999px;
  padding: 0 25px;
  background-color: rgba(255, 255, 255, 0.1);
  font-family: 'Helvetica Medium';
  overflow: hidden;
  margin-bottom: 37px;

  input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: #b3b3b3;
    font-size: 14px;
    padding: 6.5px 0 6.5px;

    &::placeholder {
      color: #6d6d6d;
    }
  }

  p {
    color: #fff;
    // font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    cursor: pointer;

    span {
      display: flex;
      align-items: center;
    }

    svg {
      display: block;
    }
  }
}

.appdownload {}

.downloadText {
  font-family: "Helvetica Medium";
  font-size: 14px;
  padding-bottom: 24px;
}

.head {
  color: #6D6D6D;
}

.app {
  color: #D7D7D7;
}

.applogo {
  display: flex;
  gap: 18px;
}

// Add bottom padding to body when mobile footer is fixed
@media @tablet {
  body {
    padding-bottom: 80px; // Adjust based on navmobile height
  }
}