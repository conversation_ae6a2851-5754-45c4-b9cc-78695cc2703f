import React, { useMemo, useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { FDKLink } from "fdk-core/components";
import { convertActionToUrl } from "@gofynd/fdk-client-javascript/sdk/common/Utility";
import styles from "./footer.less";
import useHeader from "../header/useHeader";
import SocialLinks from "../socail-media/socail-media";
import { useThemeConfig, useViewport } from "../../helper/hooks";
import Home from "../../assets/images/home-2.svg";
import WishlistIcon from "../../assets/images/single-row-wishlist.svg";
import UserIcon from "../../assets/images/single-row-user.svg";
import CartIcon from "../../assets/images/single-row-cart.svg";
import Category from "../../assets/images/category.svg";
import { sync } from "framer-motion";
import AppStore from "../../assets/images/app-store.svg";
import PlayStore from "../../assets/images/play-store.svg";
import Navigation from "../header/navigation";
// import HeaderNavigation from "../header/useHeader";

function Footer({
  fpi,
  // checkLogin,
  fallbackLogo,
  cartItemCount,
  LoggedIn,
  appInfo,
  navigation,
  navigationList,
  wishlistCount,
  isHyperlocal = false,
  isPromiseLoading = false,
  pincode = "",
  deliveryMessage = "",
  onDeliveryClick = () => { },
}) {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    globalConfig,
    FooterNavigation,
    contactInfo,
    supportInfo,
    HeaderNavigation = [],
  } = useHeader(fpi);
  const { email, phone } = supportInfo?.contact ?? {};
  const { active: emailActive = false, email: emailArray = [] } = email ?? {};
  const { active: phoneActive = false, phone: phoneArray = [] } = phone ?? {};
  const { pallete } = useThemeConfig({ fpi });
  const [isMobile, setIsMobile] = useState(false);
  const isMobileView = useViewport(0, 768);
  const loggedIn = useHeader(fpi);
  // const [showMobileNav, setShowMobileNav] = useState(false);
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 0
  );

  const checkLogin = (type) => {
    if (type === "cart") {
      navigate?.("/cart/bag/");
      return;
    }

    if (!loggedIn) {
      openLogin();
      return;
    }

    const routes = {
      profile: "/profile/details",
      profile_mobile: "/profile/profile-tabs",
      wishlist: "/wishlist",
    };

    if (routes[type]) {
      navigate?.(routes[type]);
    }
  };

  const isPDP = /^\/product\/[^/]+\/?$/.test(location.pathname); // ⬅️ PDP check
  useEffect(() => {
    if (typeof window !== "undefined") {
      const mq = window.matchMedia("(max-width: 767px)");
      setIsMobile(mq.matches);

      const handler = (e) => setIsMobile(e.matches);

      if (mq.addEventListener) {
        mq.addEventListener("change", handler);
      } else if (mq.addListener) {
        mq.addListener(handler);
      }

      return () => {
        if (mq.removeEventListener) {
          mq.removeEventListener("change", handler);
        } else if (mq.removeListener) {
          mq.removeListener(handler);
        }
      };
    }
  }, []);

  const getArtWork = () => {
    if (globalConfig?.footer_image) {
      return {
        "--background-desktop": `url(${globalConfig?.footer_image_desktop ||
          "../../assets/images/placeholder19x6.png"
          })`,
        "--background-mobile": `url(${globalConfig?.footer_image_mobile ||
          "../../assets/images/placeholder4x5.png"
          })`,
        "--footer-opacity": 0.25,
        "--footer-opacity-background": `${pallete?.footer?.footer_bottom_background}40`, // The last two digits represents the opacity (0.25 is converted to hex)
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover ",
        backgroundPosition: "center",
      };
    }
    return {};
  };

  const getLogo = globalConfig?.logo?.replace("original", "resize-h:100");

  const isSocialLinks = Object.values(contactInfo?.social_links ?? {}).some(
    (value) => value?.link?.trim?.()?.length > 0
  );

  function hasOne() {
    return emailArray?.length || phoneArray?.length || isSocialLinks;
  }

  const footerStyle = {
    ...getArtWork(),
    ...(isMobile && isPDP ? { paddingBottom: "74px" } : {}),
  };

  const isFooterHidden = useMemo(() => {
    const regex =
      /^\/refund\/order\/([^/]+)\/shipment\/([^/]+)$|^\/cart\/bag\/?$|^\/cart\/checkout\/?$/;
    return regex.test(location?.pathname);
  }, [location?.pathname]);

  const isListingPage = /^\/(products\/?|collection\/.+)$/.test(location?.pathname);

  // Simplified logic: hide footer on mobile for listing pages
  const shouldHideFooter = isFooterHidden || (isListingPage && isMobileView);

  // Track window resize
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return !shouldHideFooter ? (
    <footer className={`${styles.footer} fontBody`} style={footerStyle} >
      <>
        <div className={styles.footer__top}>
          <div className={`${styles.footerContainer}`}>
            <div className={`${styles["footer__top--wrapper"]}`}>
              <div className={`${styles["footer__top--menu"]}`}>
                <div className={`${styles.footer_dummy}`}>
                  <p className={`${styles.dummy}`}>NEWSLETTER</p>
                  <div className={`${styles.dummyforP}`}>
                    <p className={`${styles.dummy2}`}>Subscribe to our</p>
                    <p className={`${styles.dummy2}`}>newsletter</p>
                  </div>
                  <div className={styles.email_main}>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className={styles.emailInput}
                    />
                    <p>
                      sign up{" "}
                      <span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="5"
                          height="9"
                          viewBox="0 0 5 9"
                          fill="none"
                        >
                          <path
                            d="M1.31155 7.5L3.78125 5.0303C4.07292 4.73864 4.07292 4.26136 3.78125 3.9697L1.31155 1.5"
                            stroke="white"
                            strokeWidth="2"
                            strokeMiterlimit="10"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </span>
                    </p>
                  </div>
                </div>
                {FooterNavigation?.map((item, index) => (
                  <div className={styles.linkBlock} key={index}>
                    <h5 className={`${styles.menuTitle} ${styles.fontBody}`}>
                      {item?.action?.page?.type === "external" ? (
                        <a
                          href={item?.action?.page?.query?.url[0]}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {item.display}
                        </a>
                      ) : convertActionToUrl(item?.action)?.length > 0 ? (
                        <FDKLink to={convertActionToUrl(item?.action)}>
                          {item.display}
                        </FDKLink>
                      ) : (
                        <p>{item.display}</p>
                      )}
                    </h5>
                    <ul className={`${styles.list}  `}>
                      {item?.sub_navigation?.map((subItem, subIndex) =>
                        subItem?.active ? (
                          <li
                            className={`${styles.menuItem} b1 ${styles.fontBody} `}
                            key={subIndex}
                          >
                            {subItem?.action?.page?.type === "external" ? (
                              <a
                                href={subItem?.action?.page?.query?.url[0]}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {subItem.display}
                              </a>
                            ) : convertActionToUrl(subItem?.action)?.length >
                              0 ? (
                              <FDKLink to={convertActionToUrl(subItem?.action)}>
                                {subItem.display}
                              </FDKLink>
                            ) : (
                              <p>{subItem.display}</p>
                            )}
                          </li>
                        ) : null
                      )}
                    </ul>
                  </div>
                ))}
                {FooterNavigation?.length === 1 && (
                  <div className={styles.lineBlock} />
                )}
                {FooterNavigation?.length === 2 && (
                  <div className={styles.lineBlock} />
                )}
              </div>
              {hasOne() && (
                <div
                  className={`${styles["footer__top--contactInfo"]} ${globalConfig?.footer_contact_background !== false ? "" : styles["footer__top--noBackground"]}`}
                >
                  <div className={`${styles.contactus}`}>
                    {phoneActive && phoneArray?.length > 0 && (
                      <div className={styles.listData}>
                        {phoneArray.map((item, idx) => (
                          <div
                            className={styles.footerSupportData}
                            key={`phone-${idx}`}
                          >
                            <h5
                              className={`${styles.title} ${styles.contacts} ${styles.fontBody}`}
                            >
                              {item?.key}
                            </h5>
                            <a
                              href={`tel:${item?.number}`}
                              className={`${styles.detail} b1 ${styles.fontBody}`}
                            >
                              {`${item?.code ? `+${item.code}-` : ""}${item?.number}`}
                            </a>
                          </div>
                        ))}
                      </div>
                    )}
                    {emailActive && emailArray?.length > 0 && (
                      <div className={styles.listData}>
                        {emailArray.map((item, idx) => (
                          <div
                            className={styles.footerSupportData}
                            key={`email-${idx}`}
                          >
                            <h5
                              className={`${styles.title} ${styles.contacts} ${styles.fontBody}`}
                            >
                              {item?.key}
                            </h5>
                            <a
                              href={`mailto:${item?.value}`}
                              className={`${styles.detail} b1 ${styles.fontBody}`}
                            >
                              {item?.value}
                            </a>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            {hasOne() && (
              <>
                {/* <div
                  className={`${styles["footer__top--contactInfo"]} ${globalConfig?.footer_contact_background !== false ? "" : styles["footer__top--noBackground"]}`}
                >
                  <div className={`${styles.contactus}`}>
                    {phoneActive && phoneArray?.length > 0 && (
                      <div className={styles.listData}>
                        {phoneArray.map((item, idx) => (
                          <div
                            className={styles.footerSupportData}
                            key={`phone-${idx}`}
                          >
                            <h5
                              className={`${styles.title} ${styles.contacts} ${styles.fontBody}`}
                            >
                              {item?.key}
                            </h5>
                            <a
                              href={`tel:${item?.number}`}
                              className={`${styles.detail} b1 ${styles.fontBody}`}
                            >
                              {`${item?.code ? `+${item.code}-` : ""}${item?.number}`}
                            </a>
                          </div>
                        ))}
                      </div>
                    )}
                    {emailActive && emailArray?.length > 0 && (
                      <div className={styles.listData}>
                        {emailArray.map((item, idx) => (
                          <div
                            className={styles.footerSupportData}
                            key={`email-${idx}`}
                          >
                            <h5
                              className={`${styles.title} ${styles.contacts} ${styles.fontBody}`}
                            >
                              {item?.key}
                            </h5>
                            <a
                              href={`mailto:${item?.value}`}
                              className={`${styles.detail} b1 ${styles.fontBody}`}
                            >
                              {item?.value}
                            </a>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div> */}
                <div className={`${styles.appdownload}`}>
                  <div className={`${styles.download}`}>
                    <div className={`${styles.downloadText}`}>
                      <p className={`${styles.head}`}>
                        Download{" "}
                        <span className={`${styles.app}`}>Superdry app</span>
                      </p>
                    </div>
                  </div>
                  <div className={`${styles.list} ${styles.listSocial} `}>
                    <div className={`${styles.applogo}`}>
                      <AppStore />
                      <PlayStore />
                    </div>
                    {isSocialLinks && (
                      <>
                        <div className={`${styles.socialContainer}`}>
                          {globalConfig?.footer_social_text && (
                            <h5
                              className={`${styles.title} ${styles.socialTitle} ${styles.contacts} ${styles.fontBody}`}
                            >
                              {globalConfig?.footer_social_text}
                            </h5>
                          )}
                          <span>
                            <SocialLinks
                              social_links={contactInfo?.social_links}
                            />
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
        <div className={styles["footer__top--info"]}>
          {getLogo?.length > 0 && (
            <div className={`${styles.logo} `}>
              <img
                src={getLogo}
                loading="lazy"
                alt="Footer Logo"
                fetchpriority="low"
              />
            </div>
          )}
          {/* <p className={`${styles.description} b1 ${styles.fontBody}`}>
                    {globalConfig?.footer_description}
                  </p> */}
        </div>
        {contactInfo?.copyright_text && (
          <div className={`${styles.footer__bottom}`}>
            <div className={styles.footerContainer}>
              <div className={`${styles.copyright} b1 ${styles.fontBody}`}>
                {contactInfo?.copyright_text}
              </div>
              {globalConfig?.payments_logo && (
                <div className={styles.paymentLogo}>
                  <img
                    src={globalConfig?.payments_logo}
                    alt="Payment Logo"
                    loading="lazy"
                    fetchpriority="low"
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </>
    </footer>
  ) : (
    <footer >
      <ul className={styles.navmobile}>
        <button type="button" onClick={() => navigate("/")}>
          <Home />
        </button>
        {/* 
        <button type="button">
          <Category />
        </button> */}
        <Navigation
          fallbackLogo={fallbackLogo}
          maxMenuLenght={12}
          reset
          isSidebarNav
          LoggedIn={loggedIn}
          navigationList={HeaderNavigation}
          appInfo={appInfo}
          globalConfig={globalConfig}
          checkLogin={checkLogin}
        />
        <button
          type="button"
          className={` ${styles["right__icons--wishlist"]}`}
          aria-label="wishlist"
          onClick={() => checkLogin("wishlist")}
        >
          <div className={styles.icon}>
            <WishlistIcon
              className={`${styles.wishlist} ${styles.singleRowIcon}`}
            />
            {wishlistCount > 0 && LoggedIn && (
              <span className={styles.count}>{wishlistCount}</span>
            )}
          </div>
        </button>
        {!globalConfig?.disable_cart &&
          globalConfig?.button_options !== "none" && (
            <button
              type="button"
              className={`${styles.icon} ${styles["right__icons--bag"]}`}
              aria-label={`${cartItemCount ?? 0} item in cart`}
              onClick={() => checkLogin("cart")}
            >
              <div>
                <CartIcon
                  className={`${styles.cart} ${styles.singleRowIcon}`}
                />
                {cartItemCount > 0 && (
                  <span className={styles.count}>{cartItemCount}</span>
                )}
              </div>
            </button>
          )}
        <button
          type="button"
          className={`${styles.icon} ${styles["right__icons--profile"]}`}
          aria-label="Profile"
          onClick={() => checkLogin("profile_mobile")}
        >
          <UserIcon
            className={`${styles.user} ${styles.headerIcon} ${styles.singleRowIcon}`}
          />
        </button>
      </ul>
    </footer>
  );
}

export default Footer;
