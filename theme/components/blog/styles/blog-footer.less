@import "../../../styles/main.less";

.footer {
  background-color: @ThemeAccentL4;
  padding-left: 3%;
  padding-right: 3%;
  @media @tablet {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  &__container {
    max-width: @page-width;
    margin-left: auto;
    margin-right: auto;
    padding: 40px 0;
    @media @mobile-up {
      padding: 72px 0;
    }
  }
  &__title {
    text-align: center;
    text-transform: capitalize;
    font-size: 20px;
    line-height: 28px;
    @media @mobile-up {
      font-size: 32px;
      line-height: 40px;
    }
  }
  &__description {
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    @media @mobile-up {
      font-size: 18px;
    }
  }
  &__ctaWrapper {
    margin-top: 30px;
    justify-content: center;
    display: flex;
  }
  &__cta {
    text-align: center;
    border-radius: @ButtonRadius;
    justify-content: center;
    align-items: center;
    margin-top: 0;
    padding: 16px 48px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    position: relative;
    line-height: 18px;
    @media @tablet-strict {
      min-height: 48px;
    }
  }
}
