@import "../../../styles/main.less";

.imageWrapper {
  &:before {
    content: unset;
  }
  ::v-deep {
    .fy__img {
      object-fit: cover;
    }
    & > * {
      position: static;
    }
    picture {
      width: 100%;
      height: 100%;
    }
  }
}
.sideTabs {
  background-color: @ThemeAccentL5;
  border: 1px solid @ThemeAccentL4;
  border-radius: @ImageRadius;
  padding: 1rem;
  position: sticky;
  top: calc(var(--headerHeight) + 86px);
  &__menu {
    border: 1px solid @DividerStokes;
    color: @TextHeading;
    background-color: @ThemeAccentL5;
    border-radius: 100px;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 275px;
    height: 48px;
    margin-left: auto;
    margin-right: auto;
    padding: 4px;
    display: flex;
    box-shadow: 0 4px 9px 4px rgba(235, 235, 235, 0.5);
    // @media @desktop {
    //   max-width: 400px;
    // }
    // @media screen and (min-width: 1280px) {
    //   //  max-width: 269px;
    //   max-width: 275px;
    // }
    button {
      background-color: transparent;
      border: none;
      color: @ButtonPrimary;
      justify-content: center;
      align-items: center;
      height: 100%;
      padding: 8px 16px;
      display: flex;
      &.active {
        background-color: @ButtonPrimary;
        color: @ButtonSecondary;
        border-radius: 100px;
        flex: 1;
        padding-left: 16px;
        padding-right: 16px;
      }
    }
  }
  &__content {
    margin-top: 20px;
  }
  &__list {
    gap: 20px;
    display: flex;
    flex-direction: column;
  }
}
.blogHorizontal {
  gap: 12px;
  display: flex;
  align-items: center;
  &__image {
    flex: 0 0 80px;
    width: 80px;
    height: 60px;
    border-radius: 4px;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 70%;
    overflow: hidden;
    ::v-deep {
      picture {
        background-color: transparent;
      }
    }
  }
}
