@import "../../styles/main.less";

.mapWrap {
  padding-bottom: 16px;
}

.formGroup {
  &:not(:first-child) {
    margin-top: 12px;
  }
  .groupLabel {
    font-size: 12px;
    margin-bottom: 8px;
    color: var(--textBody);
    font-family: "Helvetica Medium" !important;
  }
}
.formItemDiv {
  position: relative;
  margin-bottom: 24px;
  &.fullInput {
    width: 100%;
  }
  &.halfInput {
    width: 47%;
    @media @tablet {
      width: 100%;
    }
  }

  .formInputBox {
    &.otherInput {
      width: 45%;
    }
    font-size: 0.8rem;
    outline: none;
    border: 1px solid var(--dividerStokes);
    border-radius: 5px;
    padding: 0 0.7rem;
    height: 48px;
    color: var(--textHeading);
    transition: 0.1s ease-out;
    box-sizing: border-box;
    width: 100%;
    background-color: transparent;
  }

  .formCheckBox {
    font-size: 0.8rem;
    outline: none;
    border: 1px solid var(--dividerStokes);
    border-radius: 5px;
    padding: 0 0.7rem;
    height: 15px;
    width: 15px;
    color: var(--buttonPrimary);
    transition: 0.1s ease-out;
    box-sizing: border-box;
    margin-inline-end: 10px;
  }

  .formCheckboxLabel {
    font-size: 0.8rem;
    position: absolute;
    inset-inline-start: 11px;
    top: 0px;
    color: var(--textLabel);
    padding: 0 0.3rem;
    margin: 0 0.5rem;
  }

  .formInputBox:focus {
    border-color: var(--buttonPrimary);
  }

  .formLabel {
    position: absolute;
    font-size: 0.8rem;
    inset-inline-start: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
    color: var(--textLabel);
    padding: 0 0.3rem;
    margin: 0 0.5rem;
    transition: 0.1s ease-out;
    transform-origin: left top;
    pointer-events: none;
    top: 0;
    transform: translateY(-50%) scale(0.9);
    font-family: "Helvetica Medium" !important;

    .formReq {
      color: var(--errorText);
    }
  }

  .formInputBox:focus + .formLabel {
    color: var(--buttonPrimary);
  }
}
.formItemDiv:has(input[type="hidden"]) {
  margin-bottom: 0;
}
.addressFormWrapper {
  .formContainer {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    width: 100%;
    justify-content: space-between;
    .labelClassName {
      span {
        color: @ErrorText;
        font-family: "Helvetica Medium" !important;
      }
    }

    .formItemDiv:nth-child(odd) {
      margin-inline-end: 8px;
      @media @tablet {
        margin-inline-end: 0;
      }
    }
  }

  .formError {
    color: @ErrorText;
    width: max-content;
    font-size: 12px;
    font-weight: 400;
    line-height: 16.8px;
    margin-top: 5px;
    font-family: "Helvetica Medium" !important;
  }

  .customClass {
    display: flex;
    flex: 1;
    flex-direction: column;
    position: relative;
  }

  .addressTypeContainer {
    width: 100%;
    .typeWrap {
      display: flex;
      margin-bottom: 16px;
      flex-wrap: wrap;
      row-gap: 16px;
    }
    .typeIcon {
      margin-inline-end: 4px;
    }
    .typeBtn {
      display: flex;
      background: transparent;
      align-items: center;
      padding: 8px;
      white-space: nowrap;
      border-radius: 6px;
      margin-right: 16px;
      border: 1px solid var(--dividerStokes);
      color: @TextHeading;
      font-family: "Helvetica Medium" !important;

      svg {
        path {
          fill: @TextHeading;
        }
      }

      &.selected {
        color: var(--buttonPrimary);
        border-width: 2px;
        border-color: var(--buttonPrimary);

        svg {
          path {
            fill: var(--buttonPrimary);
          }
        }
      }
    }
    .addressTypeHeader {
      font-size: 13px;
      font-weight: 600;
      margin-bottom: 16px;
      margin-top: 16px;
      display: block;
      font-family: "Helvetica Bold" !important;
    }

    .addressType {
      margin-top: 20px;
      position: relative;
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      align-items: flex-end;

      .addressTypes {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 4px 12px;
        gap: 10px;
        height: 40px;
        border: 1px solid #e2e2e2;
        border-radius: 4px;
      }

      .selectedDiv {
        border: 1px solid var(--buttonPrimary);
      }

      input[type="radio"] {
        margin-inline-start: 0;
        margin-inline-end: 10px;
      }

      label {
        display: flex;
        align-items: center;
        cursor: pointer;
        gap: 5px;
        font-family: "Helvetica Medium" !important;

        &:hover {
          font-weight: 400;
        }
      }
    }
  }
}

.formContainer2 {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  width: 100%;
  justify-content: space-between;
  padding: 0;
}

.defaultAddressContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 20px 0 10px;
  .checkbox {
    accent-color: var(--buttonPrimary);
    height: 13px;
    width: 13px;
    cursor: pointer;
  }

  .label {
    color: @TextBody;
    font-size: 14px;
    line-height: 14px;
    cursor: pointer;
    font-family: "Helvetica Medium" !important;
  }
}

.deliverBtnDivEdit {
  width: 100%;
}

.deliverBtn {
  padding: 14px;
  border-radius: @ButtonRadius;
  background-color: var(--buttonPrimary);
  border: none;
  margin-top: 24px;
  width: 100%;
  color: #fff;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  font-family: "Helvetica Bold" !important;
}

.contactInfo {
  width: 100%;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
}

.deliveryInfo {
  margin-inline-start: 24px;
  font-weight: 600;
  font-size: 12px;
  margin-top: 24px;
  font-family: "Helvetica Medium" !important;
}

.container {
  position: relative;

  .trigger {
    cursor: pointer;
    text-decoration: underline;
    font-size: 12px;
    font-weight: 400;
    font-family: "Helvetica Medium" !important;
  }

  .dropdown {
    margin: 5px 0;
    width: 100%;
    padding: 3px;
    font-family: "Helvetica Medium" !important;
  }

  .saveButton {
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
    width: 100%;
    font-family: "Helvetica Bold" !important;
  }
  .dropdownInput {
    position: relative;
  }
  .customDropdown {
    position: relative;
  }

  .dropdownTrigger {
    width: 100%;
    border-bottom: 1px solid #dcdcdc;
  }
  .dropdownIcon {
    margin-top: 10px;
    position: absolute;
    inset-inline-end: 2px;
    top: 0;
    width: 0;
    height: 0;
    border-inline-start: 5px solid transparent;
    border-inline-end: 5px solid transparent;
    border-top: 5px solid #333;
    margin-inline-start: 5px;
    transition: transform 0.2s ease;
  }

  .dropdownIcon.open {
    transform: rotate(180deg);
  }

  .dropdownOptions {
    z-index: 2;
    width: 100%;
    position: absolute;
    top: 100%;
    inset-inline-start: 0;
    margin-top: 5px;
    list-style: none;
    padding: 0;
    border: 1px solid #ccc;
    background-color: #fff;
    overflow-y: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .dropdownOptions li {
    font-size: 12px;
    font-weight: 400;
    padding: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-family: "Helvetica Medium" !important;
  }

  .dropdownOptions li:hover {
    background-color: #f0f0f0;
  }
}
