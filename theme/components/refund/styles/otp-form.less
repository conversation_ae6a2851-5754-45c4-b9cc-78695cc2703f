@import "../../../styles/main.less";

.formItem {
  width: 100%;
  margin: 25px 0px;
  .formTitle {
    color: @DustyGray2;
  }
  .formReq {
    color: @Required;
  }
  .formInput {
    //margin: 10px 0px;
    margin: 7px 0px 0px;
    .paymentInput {
      border-bottom: 1px solid #dcdcdc;
      font-weight: 300;
      font-size: 14px;
      color: #41434c;
      border: 1px solid #d7d7d7;
      border-radius: 4px;
      width: 95%;
      animation: fadeIn 0.5s ease;
      height: 40px;
      padding-left: 10px;
    }
    .formError {
      // margin: 10px 0px;
      padding: 5px 0px 7px;
      margin-bottom: 7px;
      visibility: hidden;
      color: @Required;
    }
    .visible {
      visibility: visible;
    }
  }
  .paymentInputError {
    font-weight: bold !important;
    border-color: @Required !important;
  }
  .error {
    color: @Required !important;
  }
}
