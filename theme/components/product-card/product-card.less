// @import "../../styles/main.less";
// .productCard {
//   display: flex;
//   flex-direction: column;
//   height: 100%;
//   overflow: hidden;
// }

// /* Image section styling */
// .imageSection {
//   position: relative;
//   width: 100%;
//   margin-bottom: 12px;
// }

// .imageContainer {
//   width: 100%;
//   object-fit: cover;
//   border-radius: 20px;
//   transition: 300ms transform cubic-bezier(0, 0, 0.2, 1);
// }

// /* Badge styling */
// .badge {
//   display: inline-flex;
//   align-items: center;
//   justify-content: center;
//   padding: 0.5rem 0.75rem;
//   border-radius: 17px;
//   position: absolute;
//   bottom: 1rem;
//   left: 16px;
//   background: #000000; /* @Dark */
// }

// .badgeText {
//   color: #ffffff; /* @ThemeAccentL5 */
//   font-size: 14px;
//   font-weight: 500;
//   line-height: 130%;
//   font-family: "Helvetica medium";
// }

// /* Color selector styling */
// .colorSelector {
//   position: absolute;
//   top: 0.5rem;
//   right: 0.5rem;
//   padding: 0.25rem;
//   border-radius: 9999px;
// }

// /* Details section styling */
// .detailsSection {
//   padding: 0.25rem 0.75rem;
//   margin-top: 1rem;
//   width: 100%;
//   max-width: 100%;
// }

// .productName {
//   font-weight: 700;
//   font-size: 1.125rem;
//   line-height: 130%;
//   color: #333333; /* @Dark-60 */
//   font-family: "Helvetica Bold";
// }

// /* Price container styling */
// .priceContainer {
//   display: flex;
//   align-items: center;
//   gap: 15px;
//   margin-top: 17px;
//   font-size: 1.125rem;
//   line-height: 130%;
// }

// .effectivePrice {
//   font-weight: 700;
//   color: #1a1a1a;
//   font-size: 16px;
//   font-family: "Helvetica Bold";
// }

// .markedPrice {
//   text-decoration: line-through;
//   color: #999999;
//   font-size: 1rem;
//   font-family: "Helvetica Medium";
// }

// .discountLabel {
//   color: #ff1e00;
//   font-weight: 500;
//   font-size: 1rem;
//   font-family: "Helvetica Medium";
// }

// /* Animation effects */
// .animate {
//   animation: 0.5s ease-in var(--delay, 0ms) fadeSlide;
//   animation-fill-mode: both;
// }

// @keyframes fadeSlide {
//   0% {
//     opacity: 0;
//     transform: translateY(10px);
//   }
//   to {
//     opacity: 1;
//     transform: translateY(0);
//   }
// }

// /* Mobile styles */
// @media (max-width: 768px) {
//   .badge {
//     left: 8px;
//     top: 8px;
//   }

//   .productName {
//     font-size: 1rem;
//   }
// }

// /* Tablet styles */
// @media (min-width: 769px) and (max-width: 1023px) {
//   .badge {
//     left: 12px;
//     top: 12px;
//   }

//
// }

// /* Desktop styles */
// @media (min-width: 1024px) {
//   .badge {
//     left: 24px;
//     top: 24px;
//   }

//   .wishlistBtn {
//     bottom: 1rem;
//     right: 1rem;
//   }
// }
// @media @mobile {
//   &.mob-grid-1-card {
//     .addToCart {
//       width: 104px;
//     }

//     .imageContainer {
//
//       .badge {
//         left: 16px;
//         top: 16px;
//       }
//       .review {
//         right: 16px;
//         bottom: 16px;
//       }
//       .extension {
//         left: 16px;
//         bottom: 16px;
//       }
//     }
//   }
//   &.mob-grid-2-card {
//     .productDescContainer {
//       flex-direction: column;
//       gap: 8px;

//       .productDesc {
//         .productBrand {
//           margin-bottom: 0px;
//         }
//         .productName {
//           margin-bottom: 7px;
//         }
//       }

//       .addToCart {
//         width: 100%;
//       }
//     }
//     .imageContainer {
//
//       .badge {
//         left: 8px;
//         top: 8px;
//       }
//       .review {
//         right: 8px;
//         bottom: 8px;
//       }
//       .extension {
//         left: 8px;
//         bottom: 8px;
//       }
//     }
//   }
//   &.mob-grid-3-card {
//     .addToCart {
//       width: 104px;
//     }
//     .imageSection {
//       .badge {
//         left: 16px;
//         top: 16px;
//       }
//       .review {
//         right: 16px;
//         bottom: 16px;
//       }
//       .extension {
//         left: 16px;
//         bottom: 16px;
//       }
//     }
//   }
// }
// @media @mobile-up {
//   &.tablet-grid-2-card {
//     .addToCart {
//       width: 104px;
//     }

//     .imageContainer {
//       .wishlistBtn {
//         top: 24px;
//         right: 24px;
//       }
//       .badge {
//         left: 24px;
//         top: 29px;
//       }
//       .review {
//         right: 24px;
//         bottom: 24px;
//       }
//       .extension {
//         left: 24px;
//         bottom: 24px;
//       }
//     }
//     .productDesc {
//       .productName {
//         margin-bottom: 9px;
//       }
//     }
//   }
//   &.tablet-grid-3-card {
//     .addToCart {
//       width: 104px;
//     }

//     .imageContainer {
//       .wishlistBtn {
//         top: 12px;
//         right: 12px;
//       }
//       .badge {
//         left: 12px;
//         top: 17px;
//       }
//       .review {
//         right: 12px;
//         bottom: 12px;
//       }
//       .extension {
//         left: 12px;
//         bottom: 12px;
//       }
//     }
//     .productDesc {
//       .product-name {
//         margin-bottom: 5px;
//       }
//     }
//   }
// }
@import "../../styles/main.less";

.productCard {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .imageContainer {
    position: relative;
    overflow: hidden;
    position: relative;
    border-radius: @ImageRadius;
    -webkit-mask-image: -webkit-radial-gradient(white, black); //safari fix

    .wishlistBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      fill: @ButtonPrimary;
      color: @ThemeAccentL5;
      position: absolute;

      @media @desktop {
        &:hover {
          color: @ButtonPrimary;
        }
      }

      &.active {
        color: @ButtonPrimary;
      }
    }

    .wishlistIcon {
      width: 20px;
      height: 20px;
      cursor: pointer;
      fill: white;
      color: @ButtonPrimary;

      &:hover {
        fill: black;
        color: @ThemeAccentL5;
      }
    }

    .productImage {
      // transition: 300ms transform cubic-bezier(0, 0, 0.2, 1);
      height: 100%;
      width: 100%;

      &.hoverImage {
        position: absolute;
        .inset(0);
        visibility: hidden;
        opacity: 0;
      }
    }

    @lg-min: 860px;

    @media @desktop {
      &:hover {
        .hoverImage + .mainImage {
          visibility: hidden;
        }

        .hoverImage,
        .mainImage {
          visibility: visible;
          opacity: 1;
          // transform: scale(1.1);
        }
      }
    }

    .badge {
      position: absolute;
      min-width: 50px;
      border-radius: 24px;
      padding: 6px 12px;
      text-align: center;
      background-color: @SaleBadgeBackground;
      color: @SaleBadgeText;
      white-space: nowrap;
      overflow: hidden;

      .text {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      &.outOfStock {
        background-color: @ThemeAccentD5;
        color: @ThemeAccentL5;
      }

      &.sale {
        background-color: @SaleDiscountText;
        color: @ThemeAccentL5;
      }
    }

    .review {
      display: flex;
      .column-gap(4px);
      border-radius: 4px;
      padding: 4px 8px;
      background-color: @ThemeAccentL5;
      color: @ThemeAccentD5;
      position: absolute;

      ::v-deep .inline-svg svg {
        width: 14px !important;
        height: 14px !important;
      }
    }

    .extension {
      position: absolute;
    }
  }

  .productDescContainer {
    display: flex;
    justify-content: space-between;
    // gap: 12px;
    padding: 0.25rem 0.75rem;
    flex: 1;

    .productDesc {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // flex: 1;
      gap: 8px;

      .bestSellerContainer {
        display: flex;
        padding: 5.556px 7.407px;
        justify-content: center;
        align-items: center;
        gap: 7.407px;
        border-radius: 18.519px;
        background: @Dark;
        width: fit-content;
        @media (min-width: 769px) {
          display: none;
        }
      }

      .bestSellerText {
        color: #fff;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Medium";
        font-size: 0.625rem;
        font-style: normal;
        font-weight: 500;
        line-height: 130%; /* 13px */
        letter-spacing: 0.2px;
      }

      .productNameContainer {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
        gap: 16px;
        flex-direction: column;
      }
      .markedDiscountContainer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 11px;
        flex-wrap: wrap;

        .markedDiscountContainerUp {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          gap: 7px;
        }

        @media (min-width: 769px) {
          display: none;
        }
      }

      .productBrand {
        margin-bottom: 4px;
        .text-line-clamp();
        color: @TextBody;
        font-size: 16px;
        font-weight: 600;
        line-height: 18px;
      }

      .productName {
        // margin-bottom: 4px;
        .text-line-clamp();

        color: @Dark-60;
        leading-trim: both;
        text-edge: cap;

        /* H5 */
        font-family: "Helvetica Bold";
        font-size: 1.125rem;
        /* 18px */
        font-style: normal;
        font-weight: 700;
        line-height: 130%;
        /* 23.4px */

        &.centerAlign {
          text-align: center;
        }
      }

      .productPrice {
        display: flex;
        flex-wrap: wrap;
        align-items: baseline;
        gap: 1rem;
        @media (max-width: 768px) {
          display: none;
        }

        &.center {
          justify-content: center;
        }

        .productPriceContainer {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 1rem;
          @media (max-width: 768px) {
            display: none;
          }
        }

        &--sale {
          // margin-right: 8px;
          color: @Dark;
          leading-trim: both;
          text-edge: cap;

          /* H5 */
          font-family: "Helvetica Bold";
          font-size: 1.125rem;
          /* 18px */
          font-style: normal;
          font-weight: 700;
          line-height: 130%;
          /* 23.4px */
        }

        &--regular {
          color: @Dark-40;
          leading-trim: both;
          text-edge: cap;

          /* Body 16 */
          font-family: "Helvetica Medium";
          font-size: 1rem;
          /* 16px */
          font-style: normal;
          font-weight: 500;
          line-height: 130%;
          /* 20.8px */
          text-decoration-line: line-through;
        }

        &--discount {
          color: @Brand;
          leading-trim: both;
          text-edge: cap;

          /* Body 16 Bold */
          font-family: "Helvetica Bold";
          font-size: 1rem;
          font-style: normal;
          font-weight: 700;
          line-height: 130%;
          /* 20.8px */
        }
      }

      .productVariants {
        margin-top: 8px;

        .flex-align-center();
        gap: 8px;

        .shade {
          .flex-align-center();
          padding: 4px 8px;
          background-color: @ThemeAccentL5;
          border: 1px solid @DividerStokes;
          border-radius: 40px;

          .shadeColor {
            width: 12px;
            height: 12px;
            border: 1px solid @DividerStokes;
            border-radius: 50%;
          }

          .shadeName,
          .shadeCount {
            margin-left: 4px;
            font-size: 12px;
            line-height: 14px;
            color: @TextBody;
            text-transform: capitalize;
          }
        }

        .allShades {
          .variantContainer {
            display: flex;
            isolation: isolate;

            .shadeColor:not(:first-child) {
              margin-left: -4px;
              z-index: -1;
            }

            .shadeColor:last-child {
              z-index: -2;
            }
          }
        }
      }
    }

    // .addToCart {
    //   border: 0.8px solid @DividerStokes;
    //   background: @ButtonSecondary;
    //   font-weight: 500;
    //   line-height: 16px;
    //   letter-spacing: -0.24px;
    //   text-transform: uppercase;
    //   padding: 12px 16px;
    //   height: 40px;
    //   font-size: 12px;
    //   white-space: nowrap;
    // }
  }

  // Mobile Styles
  @media (max-width: 767px) {
    // Mobile 1 Card Per Row - Horizontal Layout
    &.mob-grid-1-card {
      flex-direction: row;
      gap: 10px;
      align-items: stretch;

      .imageContainer {
        width: 40%;
        margin-bottom: 0;
        flex-shrink: 0;
        min-height: 174px;
        min-width: 139px;

        .productImage {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .wishlistBtn {
          bottom: 0.5rem;
          right: 0.5rem;
          border-radius: 7px;
          background: @Dark-10;
          display: flex;
          height: 27px;
          width: 27px;
          padding: 6.3px;
          justify-content: center;
          align-items: center;
          flex-shrink: 0;

          svg {
            display: flex;
            width: 14px;
            height: 14px;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
          }
        }

        .badge {
          left: 8px;
          top: 8px;
          font-size: 10px;
          padding: 4px 8px;
          min-width: auto;
        }

        .review {
          right: 8px;
          bottom: 8px;
        }

        .extension {
          left: 8px;
          bottom: 8px;
        }
      }

      .productDescContainer {
        width: 60%;
        flex: 1;
        padding: 0;
        flex-direction: column;
        justify-content: flex-start;

        padding: 0.5rem;

        .productDesc {
          gap: 1rem;

          .productName {
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 0;
            display: -webkit-box;

            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .productPrice {
            gap: 8px;

            &--sale {
              font-size: 14px;
            }

            &--regular {
              font-size: 14px;
            }

            &--discount {
              font-size: 14px;
            }
          }

          .productVariants {
            margin-top: 6px;

            .shade {
              padding: 2px 6px;

              .shadeColor {
                width: 10px;
                height: 10px;
              }

              .shadeName,
              .shadeCount {
                font-size: 10px;
                margin-left: 3px;
              }
            }
          }
        }
      }
    }

    // Mobile 2 Cards Per Row
    &.mob-grid-2-card {
      .imageContainer {
        width: 100%;
        aspect-ratio: calc(162 / 203);
        min-width: 0;
        height: auto;

        .productImage {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .wishlistBtn {
          bottom: 0.5rem;
          right: 0.5rem;
          display: flex;
          width: 27px;
          height: 27px;
          padding: 6.3px;
          justify-content: center;
          align-items: center;
          gap: 6.267px;
          flex-shrink: 0;
          border-radius: 7px;
          background: @Dark-10;

          svg {
            display: flex;
            width: 14px;
            height: 14px;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
          }
        }

        .badge {
          left: 8px;
          top: 8px;
          font-size: 11px;
          padding: 4px 8px;
        }

        .review {
          right: 8px;
          bottom: 8px;
        }

        .extension {
          left: 8px;
          bottom: 8px;
        }
      }

      .productDescContainer {
        flex-direction: column;
        gap: 8px;
        padding: 0.5rem;
        flex: 1;
        justify-content: flex-start;

        .productDesc {
          gap: 4px;

          .productBrand {
            margin-bottom: 0px;
          }

          .productName {
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .productPrice {
            gap: 6px;

            &--sale {
              font-size: 14px;
            }

            &--regular,
            &--discount {
              font-size: 12px;
            }
          }

          .productVariants {
            margin-top: 4px;

            .shade {
              padding: 2px 6px;

              .shadeColor {
                width: 10px;
                height: 10px;
              }

              .shadeName,
              .shadeCount {
                font-size: 10px;
                margin-left: 3px;
              }
            }
          }
        }

        .addToCart {
          width: 100%;
          font-size: 11px;
          padding: 8px 12px;
          height: 32px;
        }
      }
    }

    // Mobile 3 Cards Per Row - Image Only
    &.mob-grid-3-card {
      .imageContainer {
        width: 100%;
        aspect-ratio: 102 / 127;
        min-width: 0;
        height: auto;

        .productImage {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .wishlistBtn {
          bottom: 0.5rem;
          right: 0.5rem;
          display: flex;
          padding: 0.25rem;
          justify-content: center;
          align-items: center;
          gap: 3.942px;
          border-radius: 4.403px;
          background: @Dark-10;

          svg {
            display: flex;
            width: 8.806px;
            height: 8.806px;
            justify-content: center;
            align-items: center;
          }
        }

        .badge {
          left: 6px;
          top: 6px;
          font-size: 9px;
          padding: 3px 6px;
          min-width: auto;
        }

        .review {
          right: 6px;
          bottom: 6px;
          padding: 2px 4px;
          font-size: 10px;
        }

        .extension {
          left: 6px;
          bottom: 6px;
        }
      }

      .productDescContainer {
        display: none;
      }
    }
  }

  // Tablet Styles
  @media (min-width: 769px) and (max-width: 1024px) {
    &.tablet-grid-2-card {
      .addToCart {
        width: 104px;
      }

      .imageContainer {
        .wishlistBtn {
          bottom: 1rem;
          right: 1rem;
        }

        .badge {
          left: 24px;
          top: 29px;
        }

        .review {
          right: 24px;
          bottom: 24px;
        }

        .extension {
          left: 24px;
          bottom: 24px;
        }
      }

      .productDesc {
        .productName {
          margin-bottom: 9px;
        }
      }
    }

    &.tablet-grid-3-card {
      .addToCart {
        width: 104px;
      }

      .imageContainer {
        // .wishlistBtn {
        //   top: 12px;
        //   right: 12px;
        // }
        .badge {
          left: 12px;
          top: 17px;
        }

        .review {
          right: 12px;
          bottom: 12px;
        }

        .extension {
          left: 12px;
          bottom: 12px;
        }
      }

      .productDesc {
        .product-name {
          margin-bottom: 5px;
        }
      }
    }
  }

  // Desktop Styles
  @media @desktop {
    &.desktop-grid-2-card {
      .addToCart {
        width: 144px;
      }

      .imageContainer {
        .wishlistBtn {
          bottom: 1rem;
          right: 1rem;
        }

        .badge {
          left: 24px;
          top: 24px;
        }

        .review {
          right: 24px;
          bottom: 24px;
        }

        .extension {
          left: 24px;
          bottom: 24px;
        }
      }

      .productDesc {
        .productName {
          margin-bottom: 9px;
        }
      }
    }

    &.desktop-grid-4-card {
      .productDescContainer {
        flex-direction: column;

        .addToCart {
          width: 100%;
        }

        .productDesc {
          .productName {
            // margin-bottom: 5px;
          }
        }
      }

      .imageContainer {
        .wishlistBtn {
          bottom: 1rem;
          right: 1rem;
          display: flex;
          padding: 0.75rem;
          justify-content: center;
          align-items: center;
          gap: 0.75rem;
          flex-shrink: 0;
          border-radius: 9.647px;
          background: var(--Dark-10, #e6e6e6);
        }

        .badge {
          left: 12px;
          top: 12px;
        }

        .review {
          right: 12px;
          bottom: 12px;
        }

        .extension {
          left: 12px;
          bottom: 12px;
        }
      }
    }
  }

  .captionNormal();
}


.animate {
  animation: 0.5s ease-in var(--delay, 0ms) fadeSlide;
}

// @keyframes fadeSlide {
//   0% {
//     opacity: 0;
//     transform: translateY(10px);
//   }

//   to {
//     opacity: 1;
//     transform: translateY(0);
//   }
// }
