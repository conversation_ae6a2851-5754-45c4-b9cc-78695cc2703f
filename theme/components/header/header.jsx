import React, { useEffect, useRef, Suspense, useState } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { FDKLink } from "fdk-core/components";
import { useGlobalStore } from "fdk-core/utils";
import { CART_COUNT } from "../../queries/headerQuery";
import { isRunningOnClient, isEmptyOrNull } from "../../helper/utils";
import Search from "./search";
import HeaderDesktop from "./desktop-header";
import Navigation from "./navigation";
import I18Dropdown from "./i18n-dropdown";
import useHeader from "./useHeader";
import styles from "./styles/header.less";
import fallbackLogo from "../../assets/images/logo.png";
import { useAccounts } from "../../helper/hooks";
import useHyperlocal from "./useHyperlocal";
import CartIcon from "../../assets/images/single-row-cart.svg";
import AngleDownIcon from "../../assets/images/header-angle-down.svg";
import Searchbar from "../../sections/search-bar";

const LocationModal = React.lazy(
  () => import("fdk-react-templates/components/location-modal/location-modal")
);
import "fdk-react-templates/components/location-modal/location-modal.css";

function Header({ fpi }) {
  const headerRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 0
  );
  const CART_ITEMS = useGlobalStore(fpi?.getters?.CART);
  const { headerHeight = 0 } = useGlobalStore(fpi.getters.CUSTOM_VALUE);
  const {
    globalConfig,
    cartItemCount,
    appInfo,
    HeaderNavigation = [],
    wishlistCount,
    loggedIn,
  } = useHeader(fpi);
  const { openLogin } = useAccounts({ fpi });

  const buyNow = searchParams?.get("buy_now") || false;

  const isListingPage = /^\/(products\/?|collection\/.+)$/.test(location?.pathname);
  const isHeaderHidden = /^\/refund\/order\/([^/]+)\/shipment\/([^/]+)$/.test(location?.pathname);

  // Track window resize
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (
      isEmptyOrNull(CART_ITEMS?.cart_items) &&
      location.pathname !== "/cart/bag/"
    ) {
      const payload = {
        includeAllItems: true,
        includeCodCharges: true,
        includeBreakup: true,
        buyNow: buyNow === "true",
      };
      fpi.executeGQL(CART_COUNT, payload);
    }

    const observers = [];

    if (isRunningOnClient()) {
      const header = document?.querySelector(".fdk-theme-header");
      if (header) {
        const resizeObserver = new ResizeObserver(() => {
          fpi.custom.setValue(
            `headerHeight`,
            header.getBoundingClientRect().height
          );
        });
        resizeObserver.observe(header);
        observers.push(resizeObserver);
      }

      if (headerRef.current) {
        const themeHeaderObserver = new ResizeObserver(() => {
          fpi.custom.setValue(
            `themeHeaderHeight`,
            headerRef.current.getBoundingClientRect().height
          );
        });
        themeHeaderObserver.observe(headerRef.current);
        observers.push(themeHeaderObserver);
      }
    }

    return () => {
      observers.forEach((observer) => observer.disconnect());
    };
  }, []);

  useEffect(() => {
    if (isRunningOnClient()) {
      const cssVariables = {
        "--headerHeight": `${headerHeight}px`,
      };

      const styleElement = document.createElement("style");
      const variables = JSON.stringify(cssVariables)
        .replaceAll(",", ";")
        .replace(/"/g, "");
      const str = `:root, ::before, ::after${variables}`;
      styleElement.innerHTML = str;

      document.head.appendChild(styleElement);

      return () => {
        document.head.removeChild(styleElement);
      };
    }
  }, [headerHeight]);

  const getShopLogoMobile = () =>
    appInfo?.mobile_logo?.secure_url?.replace("original", "resize-h:165") ||
    appInfo?.logo?.secure_url?.replace("original", "resize-h:165") ||
    fallbackLogo;

  const checkLogin = (type) => {
    if (type === "cart") {
      navigate?.("/cart/bag/");
      return;
    }

    if (!loggedIn) {
      openLogin();
      return;
    }

    const routes = {
      profile: "/profile/details",
      profile_mobile: "/profile/profile-tabs",
      wishlist: "/wishlist",
    };

    if (routes[type]) {
      navigate?.(routes[type]);
    }
  };

  const {
    isHyperlocal,
    isLoading,
    pincode,
    deliveryMessage,
    servicibilityError,
    isCurrentLocButton,
    isLocationModalOpen,
    handleLocationModalOpen,
    handleLocationModalClose,
    handleCurrentLocClick,
    handlePincodeSubmit,
  } = useHyperlocal(fpi);

  // 👉 Final condition to hide header only on mobile listing pages
  const hideHeader = isListingPage && windowWidth <= 768;

  return (
    <>
      {!isHeaderHidden && !hideHeader && (
        <div
          className={`${styles.ctHeaderWrapper} fontBody ${isListingPage ? styles.listing : ""}`}
          ref={headerRef}
        >
          <header
            className={`${styles.header} ${globalConfig?.header_border ? styles.seperator : ""}`}
          >
            <div className={`${styles.headerContainer} basePageContainer margin0auto`}>
              <div className={styles.desktop}>
                <HeaderDesktop
                  checkLogin={checkLogin}
                  fallbackLogo={fallbackLogo}
                  cartItemCount={cartItemCount}
                  globalConfig={globalConfig}
                  LoggedIn={loggedIn}
                  appInfo={appInfo}
                  navigation={HeaderNavigation}
                  wishlistCount={wishlistCount}
                  fpi={fpi}
                  isHyperlocal={isHyperlocal}
                  isPromiseLoading={isLoading}
                  pincode={pincode}
                  deliveryMessage={deliveryMessage}
                  onDeliveryClick={handleLocationModalOpen}
                />
              </div>
              <div className={styles.mobile}>
                <div className={`${styles.mobileTop} ${styles[globalConfig.header_layout]} ${styles[globalConfig.logo_menu_alignment]}`}>
                  <FDKLink to="/" className={`${styles.middle} ${styles.flexAlignCenter}`}>
                    <img className={styles.logo} src={getShopLogoMobile()} alt="name" />
                  </FDKLink>
                  <Searchbar globalConfig={globalConfig} fpi={fpi} />
                </div>
                {isHyperlocal && (
                  <button className={styles.mobileBottom} onClick={handleLocationModalOpen}>
                    {isLoading ? (
                      "Fetching..."
                    ) : (
                      <>
                        <div className={styles.label}>
                          {pincode ? deliveryMessage : "Enter a pincode"}
                        </div>
                        {pincode && (
                          <div className={styles.pincode}>
                            <span>{pincode}</span>
                            <AngleDownIcon className={styles.headerAngleDownIcon} />
                          </div>
                        )}
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
            <div className={`${styles.mobile} ${styles.i18Wrapper}`}>
              <I18Dropdown fpi={fpi} />
            </div>
          </header>
        </div>
      )}
      {isLocationModalOpen && (
        <Suspense fallback={<div />}>
          <LocationModal
            isOpen={isLocationModalOpen}
            pincode={pincode}
            error={servicibilityError}
            isLocationButton={isCurrentLocButton}
            onClose={handleLocationModalClose}
            onSubmit={handlePincodeSubmit}
            onCurrentLocationClick={handleCurrentLocClick}
          />
        </Suspense>
      )}
    </>
  );
}

export default Header;
