@import "../../../styles/main.less";
@import "./desktop-header.less";
@lg-min: 1024px;

.searchIcon {
  display: flex;
}
.search {
  position: absolute;
  .inset(0);
  background-color: @HeaderBackground;
  z-index: 1;
  @media @tablet {
    display: flex;
    align-items: center;
  }

  @media @desktop {
    padding: 20px 0;
  }

  &__wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    padding: 8px 16px;

    @media @desktop {
      width: 800px;
      padding: 0;
      margin-left: auto;
      margin-right: auto;
    }
  }

  &__input {
    position: relative;
    flex-grow: 1;
    &--text {
      background: @ThemeAccentL4;
      width: 100%;
      padding: 7px 32px;
      font-size: 12px;
      line-height: 20px;
      border-radius: 4px;
      border: 1px solid @DividerStokes;

      @media @desktop {
        font-size: 14px;
        padding: 9px 38px;
      }

      @media @mobile {
        font-size: 14px;
      }

      &::placeholder {
        color: @TextSecondary;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        letter-spacing: -0.28px;

        @media @tablet {
          font-size: 12px;
          line-height: 16px;
          letter-spacing: -0.24px;
        }
      }
    }
    &--removeSpace {
      padding: 9px 38px 9px 12px;

      @media @mobile {
        padding: 8px;
      }
    }
    &--search-icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
      position: absolute;
      top: 45%;
      transform: translateY(-50%);
      left: 8px;
      fill: #928b8b;
      @media @desktop {
        width: 24px;
        height: 24px;
        top: 50%;
      }
    }
  }
  &--closeIcon {
    margin-left: 12px;

    @media @mobile {
      margin-left: 8px;
    }
  }
  &__suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    @media @desktop {
      top: calc(100% + 6px);
    }
    &--products {
      padding: 8px;
      background-color: @DialogBackground;
      border: 1px solid @DividerStokes;
      border-radius: 4px;
      box-shadow:
        0px 4px 4px 0px rgba(78, 63, 9, 0.08),
        0px 8px 24px -4px rgba(78, 63, 9, 0.08);
      width: 100%;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      @media @desktop {
        margin-left: auto;
        margin-right: auto;
      }

      @media @tablet {
        border: none;
        border-top: 1px solid @DividerStokes;
        border-bottom: 1px solid @DividerStokes;
        border-radius: 0 0 4px 4px;
        box-shadow: none;
        padding: 16px;
      }

      > ul {
        overflow-y: auto;
      }
    }
    &--item {
      cursor: pointer;

      &:not(:last-child) {
        margin-bottom: 12px;
        @media @desktop {
          margin-bottom: 8px;
        }
      }

      &.no-result {
        height: 20px;
      }

      .productThumb {
        flex: 0 0 56px;
        border-radius: 4px;
        margin-right: 12px;
      }

      .productTitle {
        color: @TextHeading;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: -0.32px;

        @media @tablet {
          font-size: 14px;
          line-height: 18px;
          letter-spacing: -0.28px;
        }

        @media @desktop {
          .text-line-clamp();
        }
      }
    }
    &--button {
      text-align: center;
      margin-top: 12px;

      @media @desktop {
        margin-top: 8px;
      }
    }
  }
}
.double-row-search .search__suggestions {
  @media @desktop {
    top: calc(100% + 6px);
  }
}
.searchIcon g > path {
  fill: @HeaderIcon;
}
.single-row-search {
  .search {
    padding: 0;
    &--closeIcon {
      margin-left: 16px;
      @media @desktop {
        width: 32px !important;
        height: 32px !important;
        margin-left: 32px;
      }
    }
    &__wrapper {
      padding: 8px;
      @media @mobile-up {
        padding: 8px 24px;
      }
      @media @desktop {
        padding: 11px 0 0;
        width: 884px;
      }
    }
    &__input {
      padding: 4px 16px;
      height: 42px;
      border: 1px solid @TextLabel;
      border-radius: 4px;
      background: @ThemeAccentL5;
      @media @desktop {
        padding: 6px 16px;
        height: 48px;
      }
      &--text {
        background: transparent;
        border: none;
        padding: 0;
        height: calc(100% - 19px);
        border-radius: unset;
        position: absolute;
        bottom: 4px;
        left: 16px;
        z-index: 2;
      }
      &--label {
        position: absolute;
        top: 50%;
        left: 16px;
        transform: translateY(-50%);
        color: @TextDisabled;
        transition: 300ms all cubic-bezier(0, 0, 0.2, 1);
        &.active {
          font-size: 10px;
          font-weight: 300;
          line-height: 12px;
          letter-spacing: 0em;
          top: 4px;
          transform: unset;
          color: @TextBody;
        }
      }
      &--search-icon {
        left: unset;
        right: 10px;
        fill: @TextHeading;
        z-index: 3;
      }
    }
    &__suggestions {
      &--products {
        padding: 20px 8px;
        margin: 0;
        @media @desktop {
          width: calc(100% - 64px);
        }
      }
      &--item {
        padding: 0 16px;
        margin-bottom: 16px;
        &:last-child {
          margin-bottom: 0;
        }
        .product-title {
          color: @TextLabel;
        }
      }
      &--title {
        margin: 0 16px 24px;
        padding-bottom: 3px;
        border-bottom: 1px solid @DividerStokes;
      }
      &--button {
        margin: 24px 16px 0;
        padding: 12px 0 0;
        border-top: 1px solid @DividerStokes;
        .btnLink {
          display: flex;
          align-items: center;
          justify-content: space-between;
          text-decoration: none;
          color: @TextHeading;
          width: 100%;
          .showMoreIcon {
            width: 15px;
            height: 15px;
            transform: rotate(180deg);
            fill: @TextLabel;
          }
        }
      }
    }
  }
}

.noResult {
  color: black;
}
