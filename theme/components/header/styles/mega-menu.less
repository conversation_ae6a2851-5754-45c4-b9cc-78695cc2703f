@import "../../../styles/main.less";

.headerNavigation {
  display: flex;
  justify-content: center;
  gap: 32px;
  .navigationItem {
    display: flex;
    align-items: center;
    letter-spacing: 0.3px;
    text-align: left;
    white-space: nowrap;
    height: 100%;

    .l1Category {
      padding: 12px 0;
      display: flex;
      align-items: center;
      height: 100%;
    }
    .l1Category,
    a {
      cursor: pointer;
    }
  }
}
.navigationItem {
  position: relative;
  .l2CategoriesWrapper {
    position: absolute;
    top: 100%;
    left: 0;
    padding-top: 4px;
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    
  }
  .l2Categories {
    border-radius: min(@ButtonRadius, 8px);
    box-shadow:
      0 12px 16px rgba(0, 0, 0, 0.16),
      0 4px 4px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    max-height: 520px;
    background-color: @DialogBackground;
    padding: 10px;
    transition: 0.3s ease;
    box-sizing: content-box;
    overflow-y: auto;
    overflow-x: hidden;

    .l2Category {
      padding: 10px 16px;
      width: max-content;
      max-width: 200px;
      white-space: normal;
      overflow-wrap: break-word;
    }

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-thumb {
      border: 2px solid rgba(0, 0, 0, 0);
      background-clip: padding-box;
      border-radius: 16px;
      background-color: #7d7676;
    }
    &::-webkit-scrollbar-track {
      background: #edece9;
    }
  }
  .l1CategoryHeading {
    color: @HeaderNav;
    font-weight: inherit;
  }
  &:hover {
    .l1CategoryHeading {
      opacity: 0.5;
    }

    .menuIcon {
      transform: rotate(180deg);
      opacity: 0.5;
    }

    .l2CategoriesWrapper {
      visibility: visible;
      opacity: 1;
      pointer-events: all;
    }
  }
  .l3Category {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    margin-top: 8px;
  }
}
.l2CategoryHeading {
  display: flex;
  align-items: center;
  column-gap: 4px;
}

.l2CategoryItem {
  color: @ButtonPrimary;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: -0.32px;
  min-width: 0;
  text-overflow: ellipsis;

  &:hover {
    opacity: 0.5;
  }
}

.l3CategoryItem {
  color: #261a1a;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 128.571% */
  letter-spacing: -0.28px;

  &:hover {
    opacity: 0.5;
  }
}

.menuIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  fill: @HeaderIcon;
  min-width: 24px;
}
