// import React, { useState, useEffect, useMemo } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { FDKLink } from "fdk-core/components";
// import { convertActionToUrl } from "@gofynd/fdk-client-javascript/sdk/common/Utility";
// import styles from "./styles/navigation.less";

// // import HamburgerIcon from "../../assets/images/hamburger.svg";
// import Category from "../../assets/images/category.svg";
// import CloseIcon from "../../assets/images/close.svg";
// import ArrowDownIcon from "../../assets/images/arrow-down.svg";
// import UserIcon from "../../assets/images/user.svg";
// import WishlistIcon from "../../assets/images/single-row-wishlist.svg";
// import Search from "../../assets/images/search.svg";
// import { isRunningOnClient } from "../../helper/utils";
// import MegaMenu from "./mega-menu";
// import MegaMenuLarge from "./mega-menu-large";

// function Navigation({
//   reset,
//   isSidebarNav,
//   maxMenuLength = 0,
//   customClass = {},
//   navigationList,
//   globalConfig,
//   appInfo,
//   checkLogin,
// }) {
//   const [navigationListState, setNavigationListState] =
//     useState(navigationList);
//   const [showSidebar, setShowSidebar] = useState(false);
//   const [showSidebarNav, setShowSidebarNav] = useState(true);
//   const [sidebarl2Nav, setSidebarl2Nav] = useState({});
//   const [sidebarl3Nav, setSidebarl3Nav] = useState({});
//   const [activeItem, setActiveItem] = useState(null);
//   const [hoveredL2Index, setHoveredL2Index] = useState(null);
//   const [isClient, setIsClient] = useState(false);
//   const [selectedNav, setSelectedNav] = useState(0);

//   useEffect(() => {
//     if (sidebarl3Nav.state) {
//       // console.log(navigationList, "navigationList");
//     } else if (setSidebarl2Nav.state) {
//     }
//   }, [sidebarl2Nav, sidebarl3Nav]);

//   const handleMouseEnter = (index) => {
//     setActiveItem(index);
//   };

//   const handleMouseLeave = () => {
//     setActiveItem(null);
//     setHoveredL2Index(null);
//   };

//   const dropdownVariants = {
//     hidden: { opacity: 0, y: -20 },
//     visible: { opacity: 1, y: 0 },
//   };

//   const navWeightClassName = useMemo(() => {
//     if (globalConfig?.nav_weight === "regular") {
//       return styles.fwRegular;
//     }
//     if (globalConfig?.nav_weight === "bold") {
//       return styles.fwBold;
//     }
//     return styles.fwSemibold;
//   }, [globalConfig?.nav_weight]);

//   const closeSidebarNav = () => {
//     setShowSidebar(false);
//     setShowSidebarNav(true);
//     setSidebarl2Nav({ state: false });
//     setSidebarl3Nav({ state: false });
//   };

//   useEffect(() => {
//     if (reset) {
//       closeSidebarNav();
//     }
//   }, [reset]);

//   useEffect(() => {
//     if (isRunningOnClient()) {
//       setIsClient(true);
//       if (showSidebar) {
//         document.body.classList.add("remove-scroll");
//       } else {
//         document.body.classList.remove("remove-scroll");
//       }
//       return () => {
//         document.body.classList.remove("remove-scroll");
//       };
//     }
//   }, [showSidebar]);

//   useEffect(() => {
//     if (navigationList[0]?.sub_navigation?.length > 0) {
//       redirectToMenu(navigationList[0], "l2");
//     }
//   }, []);

//   const isHorizontalNav = navigationList?.length > 0 && !isSidebarNav;
//   // Modified to enable mega menu for both single and double row layouts
//   const isMegaMenu = isHorizontalNav && globalConfig?.header_mega_menu;
//   const isFullWidthMegamenu =
//     globalConfig?.header_mega_menu_fullwidth && isMegaMenu;
//   const getNavigation = navigationList?.slice(0, maxMenuLength);
//   const getShopLogoMobile = () =>
//     appInfo?.mobile_logo?.secure_url || appInfo?.logo?.secure_url || "";

//   const openSidebarNav = () => {
//     setShowSidebar(true);
//   };

//   const getAnimate = (index) => {
//     let animate = null;
//     if (isClient) {
//       if (activeItem === index) {
//         animate = { opacity: 1, y: 0 };
//       } else {
//         animate = { opacity: 0, y: -20 };
//       }
//     }
//     return animate;
//   };

//   const redirectToMenu = (menu, level) => {
//     // console.log(menu, "menumenumenumenumenumenu???", level);
//     if (!menu.sub_navigation.length) {
//       closeSidebarNav();
//     } else {
//       if (level === "l2") {
//         setShowSidebarNav(false);
//         setSidebarl3Nav((prev) => ({ ...prev, title: false, state: false }));

//         setSidebarl2Nav({
//           state: true,
//           title: menu.display,
//           navigation: menu.sub_navigation,
//         });
//       }
//       if (level === "l3") {
//         setSidebarl2Nav((prev) => ({ ...prev, state: false }));
//         setSidebarl3Nav({
//           state: true,
//           title: menu.display,
//           navigation: menu.sub_navigation,
//         });
//       }
//     }
//   };

//   const goBack = (level) => {
//     if (level === "l2") {
//       setSidebarl2Nav((prev) => ({ ...prev, state: true }));
//       setSidebarl3Nav((prev) => ({ ...prev, title: false, state: false }));
//     }
//     if (level === "l1") {
//       setSidebarl2Nav((prev) => ({ ...prev, title: false, state: false }));
//       setShowSidebarNav(true);
//     }
//   };

//   // console.log(navigationList, sidebarl2Nav, "navigationList");

//   return (
//     <div className={customClass}>
//       {isFullWidthMegamenu ? (
//         <MegaMenuLarge
//           headerNavigation={getNavigation}
//           l1MenuClassName={navWeightClassName}
//         ></MegaMenuLarge>
//       ) : isMegaMenu ? (
//         <MegaMenu
//           headerNavigation={getNavigation}
//           l1MenuClassName={navWeightClassName}
//         ></MegaMenu>
//       ) : isHorizontalNav ? (
//         <nav className={`${styles.nav} ${customClass}`}>
//           <AnimatePresence>
//             <motion.ul
//               className={`${styles.l1NavigationList} `}
//               style={{ fontFamily: '"Helvetica Medium"' }}
//               initial={isClient ? "hidden" : undefined}
//               animate={isClient ? "visible" : undefined}
//               exit={isClient ? "hidden" : undefined}
//               variants={dropdownVariants}
//               transition={{ duration: 0.3 }}
//             >
//               {getNavigation?.map((l1nav, index) => (
//                 <li
//                   key={index}
//                   className={`${styles.l1NavigationList__item}  ${styles.flexAlignCenter} ${styles.fontBody} ${navWeightClassName}`}
//                   onMouseEnter={() => handleMouseEnter(index)}
//                   onMouseLeave={handleMouseLeave}
//                 >
//                   {l1nav?.action?.page?.type === "external" ? (
//                     <a
//                       href={l1nav?.action?.page?.query?.url[0]}
//                       target="_blank"
//                       rel="noopener noreferrer"
//                     >
//                       <span
//                         className={`${styles.menuTitle} ${styles.flexAlignCenter}`}
//                       >
//                         <span>{l1nav.display}</span>
//                         {/* {l1nav.sub_navigation?.length > 0 && (
//                             <ArrowDownIcon
//                               className={`${styles.menuIcon} ${styles.dropdownIcon}`}
//                             />
//                           )} */}
//                       </span>
//                     </a>
//                   ) : (
//                     <FDKLink to={convertActionToUrl(l1nav?.action)}>
//                       <span
//                         className={`${styles.menuTitle} ${styles.flexAlignCenter}`}
//                       >
//                         <span>{l1nav.display}</span>
//                         {/* {l1nav.sub_navigation?.length > 0 && (
//                             <ArrowDownIcon
//                               className={`${styles.menuIcon} ${styles.dropdownIcon}`}
//                             />
//                           )} */}
//                       </span>
//                     </FDKLink>
//                   )}

//                   <AnimatePresence>
//                     {l1nav?.sub_navigation?.length > 0 && (
//                       <motion.div
//                         className={styles.l2NavigationListWrapper}
//                         initial={isClient ? { opacity: 0, y: -20 } : null}
//                         animate={() => getAnimate(index)}
//                         exit={isClient ? { opacity: 0, y: -20 } : null}
//                         transition={{ duration: 0.3 }}
//                       >
//                         <ul className={styles.l2NavigationList}>
//                           {l1nav.sub_navigation.map((l2nav, l2Index) => (
//                             <li
//                               key={l2nav.display}
//                               className={`${styles.l2NavigationList__item} b1 ${styles.fontBody}`}
//                               onMouseEnter={() => setHoveredL2Index(l2Index)}
//                               onMouseLeave={() => setHoveredL2Index(null)}
//                             >
//                               <div
//                                 className={
//                                   styles["l2NavigationList__item--container"]
//                                 }
//                               >
//                                 <FDKLink
//                                   to={convertActionToUrl(l2nav?.action)}
//                                   className={
//                                     styles["l2NavigationList__item--wrapper"]
//                                   }
//                                 >
//                                   <span
//                                     className={`${styles.menuItem} ${styles.flexAlignCenter} ${styles.justifyBetween}`}
//                                   >
//                                     <span>{l2nav.display}</span>
//                                     {/* {l2nav?.sub_navigation?.length > 0 && (
//                                         <ArrowDownIcon
//                                           className={`${styles.menuIcon} ${styles.arrowRightIcon}`}
//                                         />
//                                       )} */}
//                                   </span>
//                                 </FDKLink>
//                                 <AnimatePresence>
//                                   {l2nav.sub_navigation.length > 0 &&
//                                     hoveredL2Index === l2Index && (
//                                       <motion.ul
//                                         className={styles.l3NavigationList}
//                                         initial={
//                                           isClient
//                                             ? { opacity: 0, x: -20 }
//                                             : null
//                                         }
//                                         animate={
//                                           isClient ? { opacity: 1, x: 0 } : null
//                                         }
//                                         exit={
//                                           isClient
//                                             ? { opacity: 0, x: -20 }
//                                             : null
//                                         }
//                                         transition={{ duration: 0.3 }}
//                                       >
//                                         {l2nav.sub_navigation.map((l3nav) => (
//                                           <li
//                                             key={`${l3nav.display}`}
//                                             className={`${styles.l3NavigationList__item} b1 ${styles.fontBody}`}
//                                           >
//                                             <FDKLink
//                                               to={convertActionToUrl(
//                                                 l3nav?.action
//                                               )}
//                                               className={`${styles["l3NavigationList__item--wrapper"]}`}
//                                             >
//                                               <span
//                                                 className={`${styles.menuItem} ${styles.flexAlignCenter}`}
//                                               >
//                                                 <span>{l3nav.display}</span>
//                                               </span>
//                                             </FDKLink>
//                                           </li>
//                                         ))}
//                                       </motion.ul>
//                                     )}
//                                 </AnimatePresence>
//                               </div>
//                             </li>
//                           ))}
//                         </ul>
//                       </motion.div>
//                     )}
//                   </AnimatePresence>
//                 </li>
//               ))}
//             </motion.ul>
//           </AnimatePresence>
//         </nav>
//       ) : null}
//       <button
//         className={`${styles.icon} ${styles.flexCenter}`}
//         style={{ display: isSidebarNav ? "flex" : "none" }}
//         onClick={openSidebarNav}
//         aria-label="open navigation"
//       >
//         <Category
//         // className={`${styles.category} ${styles.menuIcon}`}
//         />
//       </button>
//       {/* Sidebar If */}
//       <div>
//         <motion.div
//           className={`${styles.sidebar}`}
//           initial={{ x: "-100%" }} // Start off-screen to the left
//           animate={{ x: showSidebar ? 0 : "-100%" }} // Animate to 0 when open, back to -100% when closed
//           transition={{ duration: 0.1, ease: "easeInOut" }}
//           style={{
//             position: "fixed",
//             top: 0,
//             left: 0,
//             height: "100%",
//           }}
//         >
//           {/* <div
//             className={`${styles.sidebar__header} ${styles.flexAlignCenter} ${styles.justifyBetween}`}
//             style={{
//               display: "flex",
//             }}
//           > */}
//           {/* <FDKLink link="/">
//               <img
//                 style={{
//                   maxHeight: `${globalConfig?.mobile_logo_max_height || 24}px`,
//                 }}
//                 className={styles.logo}
//                 src={getShopLogoMobile()}
//                 loading="lazy"
//                 alt="logo"
//               />
//             </FDKLink> */}
//           {/* <button
//               type="button"
//               className={styles.closeIcon}
//               onClick={closeSidebarNav}
//               aria-label="close"
//             >
//               <CloseIcon
//                 className={`${styles.menuIcon} ${styles.crossIcon} ${styles.sidebarIcon}`}
//               />
//             </button>
//           </div> */}
//           <nav className={styles.sidebar__navigation}>
//             <div
//               style={{
//                 display: "flex",
//                 alignItems: "center",
//                 justifyContent: "space-between",
//                 padding: "4px 20px 10px",
//               }}
//             >
//               <ul
//                 key="l1_Nav"
//                 style={{
//                   display: "flex",
//                   justifyContent: "space-between",
//                   alignItems: "center",
//                   backgroundColor: "#F2F2F2",
//                   // padding: "8px 12px",
//                   borderRadius: "999px",
//                   gap: "8px",
//                 }}
//               >
//                 {navigationListState.map((nav, index) => {
//                   return (
//                     <li
//                       key={`${nav.display}-${index}`}
//                       className={`${styles["sidebar__navigation--item"]} ${styles.flexAlignCenter} ${styles.justifyBetween} ${styles.fontBody} `}
//                       style={{
//                         backgroundColor:
//                           selectedNav === index ? "#000" : "transparent",
//                         color: selectedNav === index ? "white" : "black",
//                         borderRadius: "999px",
//                         cursor: "pointer",
//                       }}
//                       onClick={() => setSelectedNav(index)}
//                     >
//                       {nav?.action?.page?.type === "external" ? (
//                         <div
//                           href={nav?.action?.page?.query?.url[0]}
//                           target="_blank"
//                           rel="noopener noreferrer"
//                           className={styles.navLink}
//                           onClick={() => {
//                             setShowSidebar(false);
//                             closeSidebarNav();
//                           }}
//                         >
//                           {nav.display}
//                         </div>
//                       ) : // <></>
//                       convertActionToUrl(nav?.action) ? (
//                         // <FDKLink
//                         //   className={styles.navLink}
//                         //   to={convertActionToUrl(nav?.action)}
//                         //   onClick={() => {
//                         //     setShowSidebar(false);
//                         //     closeSidebarNav();
//                         //   }}
//                         // >
//                         //   {nav.display}
//                         // </FDKLink>
//                         <span
//                           className={styles.navLink}
//                           onClick={() => {
//                             if (nav.sub_navigation?.length > 0) {
//                               redirectToMenu(nav, "l2");
//                             } else {
//                               setShowSidebar(false);
//                               closeSidebarNav();
//                             }
//                           }}
//                         >
//                           {nav.display}
//                         </span>
//                       ) : (
//                         <span
//                           onClick={() => {
//                             if (nav.sub_navigation?.length > 0) {
//                               redirectToMenu(nav, "l2");
//                             } else {
//                               setShowSidebar(false);
//                               closeSidebarNav();
//                             }
//                           }}
//                         >
//                           {nav.display}
//                         </span>
//                       )}
//                       {nav?.sub_navigation?.length > 0 && (
//                         <div onClick={() => redirectToMenu(nav, "l2")}>
//                           {/* <ArrowDownIcon
//                       className={`${styles.arrowRightIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                       style={{
//                         display: "block",
//                       }}
//                     /> */}
//                         </div>
//                       )}
//                     </li>
//                   );
//                 })}
//               </ul>
//               {navigationListState[0].type === "l1" && (
//                 <Search
//                   style={{
//                     backgroundColor: "#F2F2F2",
//                     padding: "12px 16px",
//                     height: "50px",
//                     width: "56px",
//                     borderRadius: "50%",
//                   }}
//                 />
//               )}
//             </div>
//             {sidebarl2Nav.state && (
//               <>
//                 {/* <div
//                   onClick={() => goBack("l1")}
//                   className={`${styles["sidebar__navigation--header"]} ${styles.flexAlignCenter}`}
//                   style={{
//                     padding: "10px 0 20px 0",
//                     borderBottom: "1px solid #E5E5E5",
//                     marginBottom: "20px",
//                     cursor: "pointer",
//                   }}
//                 >
//                   <ArrowDownIcon
//                     className={`${styles.arrowLeftIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                     style={{ marginRight: "10px", transform: "rotate(90deg)" }}
//                   />
//                   <h3
//                     style={{ fontSize: "18px", fontWeight: "600", margin: 0 }}
//                   >
//                     {sidebarl2Nav.title}
//                   </h3>
//                 </div> */}
//                 <ul key="l2_Nav">
//                   {/* <li
//                   onClick={() => goBack("l1")}
//                   className={`${styles["sidebar__navigation--item"]} ${
//                     styles.title
//                   } ${styles.flexAlignCenter} ${styles.justifyStart} ${
//                     styles.fontBody
//                   } b1`}
//                   style={{ display: sidebarl2Nav.title ? "flex" : "none" }}
//                 >
//                   <ArrowDownIcon
//                     className={`${styles.arrowLeftIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                   />
//                   <span>{sidebarl2Nav.title}</span>
//                 </li> */}
//                   {sidebarl2Nav.navigation.map((nav, index) => (
//                     <li
//                       key={index}
//                       className={`${styles["sidebar__navigation--item"]} ${
//                         styles.flexAlignCenter
//                       } ${styles.justifyBetween} ${styles.fontBody} `}
//                     >
//                       {convertActionToUrl(nav?.action) ? (
//                         <FDKLink
//                           className={styles.navLink}
//                           to={convertActionToUrl(nav?.action)}
//                           onClick={() => {
//                             if (nav?.sub_navigation?.length) {
//                               // setNavigationListState([{ ...nav }]);
//                               redirectToMenu(nav, "l3");
//                             } else {
//                               goBack("l1");
//                               closeSidebarNav();
//                             }
//                           }}
//                         >
//                           {!!nav?.image && (
//                             <div className={styles.navigationImage}>
//                               <img
//                                 src={nav.image}
//                                 defer={true}
//                                 alt={nav?.display}
//                                 isFixedAspectRatio={false}
//                               />
//                             </div>
//                           )}
//                           {nav.display}
//                         </FDKLink>
//                       ) : (
//                         <span
//                           onClick={() => {
//                             if (nav?.sub_navigation?.length) {
//                               redirectToMenu(nav, "l3");
//                             } else {
//                               goBack("l1");
//                               closeSidebarNav();
//                             }
//                           }}
//                         >
//                           {nav.display}
//                         </span>
//                       )}
//                       {nav?.sub_navigation?.length > 0 && (
//                         <div
//                           onClick={() => {
//                             if (nav?.sub_navigation?.length) {
//                               // setNavigationListState([{ ...nav }]);
//                               redirectToMenu(nav, "l3");
//                             } else {
//                               goBack("l1");
//                               closeSidebarNav();
//                             }
//                           }}
//                           // onClick={() => redirectToMenu(nav, "l3")}
//                         >
//                           <ArrowDownIcon
//                             className={`${styles.arrowRightIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                             style={{
//                               display: "block",
//                             }}
//                           />
//                         </div>
//                       )}
//                     </li>
//                   ))}
//                 </ul>
//               </>
//             )}
//             {sidebarl3Nav.state && (<>
//               <div
//               onClick={() => goBack("l2")}
//               className={`${styles["sidebar__navigation--header"]} ${styles.flexAlignCenter}`}
//               style={{
//                 padding: "10px 0 20px 0",
//                 borderBottom: "1px solid #E5E5E5",
//                 marginBottom: "20px",
//                 cursor: "pointer",
//               }}
//             >
//               <ArrowDownIcon
//                 className={`${styles.arrowLeftIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                 style={{ marginRight: "10px", transform: "rotate(90deg)" }}
//               />
//               <h3 style={{ fontSize: "18px", fontWeight: "600", margin: 0 }}>
//                 {sidebarl3Nav.title}
//               </h3>
//             </div>
//               <ul key="l3_Nav">
//                 {/* <li
//                   onClick={() => goBack("l2")}
//                   className={`${styles["sidebar__navigation--item"]} ${
//                     styles.title
//                   } ${styles.flexAlignCenter} ${styles.justifyStart} ${
//                     styles.fontBody
//                   } b1`}
//                   style={{ display: sidebarl3Nav.title ? "flex" : "none" }}
//                 > */}
//                   {/* <ArrowDownIcon
//                     className={`${styles.arrowLeftIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                     style={{
//                       display: "block",
//                     }}
//                   /> */}

//                   {/* <span>{sidebarl3Nav.title}</span> */}
//                 {/* </li> */}
//                 {sidebarl3Nav.navigation.map((nav, index) => (
//                   <li
//                     key={index}
//                     className={`${styles["sidebar__navigation--item"]} ${
//                       styles.flexAlignCenter
//                     } ${styles.justifyBetween} ${styles.fontBody} `}
//                   >
//                     {convertActionToUrl(nav?.action) ? (
//                       <FDKLink
//                         to={convertActionToUrl(nav?.action)}
//                         className={styles.navLink}
//                         onClick={() => {
//                           closeSidebarNav();
//                         }}
//                       >
//                         {!!nav?.image && (
//                           <div className={styles.navigationImage}>
//                             <img
//                               src={nav.image}
//                               defer={true}
//                               alt={nav?.display}
//                               isFixedAspectRatio={false}
//                             />
//                           </div>
//                         )}
//                         <span>{nav.display}</span>
//                       </FDKLink>
//                     ) : (
//                       <button
//                         type="button"
//                         // onClick={() => {
//                         //   goBack("l2");
//                         //   closeSidebarNav();
//                         // }}
//                       >
//                         {nav.display}
//                       </button>
//                     )}
//                     <FDKLink
//                       to={convertActionToUrl(nav?.action)}
//                       onClick={() => closeSidebarNav()}
//                     >
//                       <ArrowDownIcon
//                         className={`${styles.arrowRightIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
//                         style={{
//                           display: "block",
//                         }}
//                       />
//                     </FDKLink>
//                   </li>
//                 ))}
//               </ul>
//               </>
//             )}
//           </nav>

//           {/* <div className={styles.sidebar__footer}>
//             <button
//               type="button"
//               className={`${styles["sidebar__footer--item"]} ${styles.account} ${styles.flexAlignCenter} ${styles.fontBody} `}
//               style={{ display: "flex" }}
//               onClick={() => {
//                 checkLogin("profile_mobile");
//                 setShowSidebar(false);
//               }}
//             >
//               <UserIcon
//                 className={`${styles.user} ${styles["sidebar-icon"]} ${styles.menuIcon}`}
//               />
//               <span>Account</span>
//             </button>
//             <button
//               type="button"
//               className={`${styles["sidebar__footer--item"]} ${
//                 styles.wishlist
//               } ${styles.flexAlignCenter} ${styles.fontBody} `}
//               onClick={() => {
//                 checkLogin("wishlist");
//                 setShowSidebar(false);
//               }}
//             >
//               <WishlistIcon
//                 className={`${styles.menuIcon}  ${styles.sidebarIcon}${styles.wishlist}`}
//               />
//               <span>Wishlist</span>
//             </button>
//           </div> */}
//         </motion.div>
//       </div>
//       {/* eslint-disable jsx-a11y/no-static-element-interactions */}
//       <div
//         className={`${styles.overlay} ${showSidebar ? styles.show : ""} `}
//         onClick={closeSidebarNav}
//       />
//     </div>
//   );
// }

// export default Navigation;

import React, { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FDKLink } from "fdk-core/components";
import { convertActionToUrl } from "@gofynd/fdk-client-javascript/sdk/common/Utility";
import styles from "./styles/navigation.less";

// import HamburgerIcon from "../../assets/images/hamburger.svg";
import Category from "../../assets/images/category.svg";
import CloseIcon from "../../assets/images/close.svg";
import ArrowDownIcon from "../../assets/images/arrow-down.svg";
import UserIcon from "../../assets/images/user.svg";
import WishlistIcon from "../../assets/images/single-row-wishlist.svg";
import Search from "../../assets/images/search.svg";
import { isRunningOnClient } from "../../helper/utils";
import MegaMenu from "./mega-menu";
import MegaMenuLarge from "./mega-menu-large";

function Navigation({
  reset,
  isSidebarNav,
  maxMenuLength = 0,
  customClass = {},
  navigationList,
  globalConfig,
  appInfo,
  checkLogin,
}) {
  const [navigationListState, setNavigationListState] =
    useState(navigationList);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showSidebarNav, setShowSidebarNav] = useState(true);
  const [sidebarl2Nav, setSidebarl2Nav] = useState({});
  const [sidebarl3Nav, setSidebarl3Nav] = useState({});
  const [activeItem, setActiveItem] = useState(null);
  const [hoveredL2Index, setHoveredL2Index] = useState(null);
  const [isClient, setIsClient] = useState(false);
  const [selectedNav, setSelectedNav] = useState(0);
  const [flag, setflag] = useState(true);

  // useEffect(() => {
  //   if (sidebarl3Nav.state) {
  //     // console.log(navigationList, "navigationList");
  //   } else if (setSidebarl2Nav.state) {
  //   }
  // }, [sidebarl2Nav, sidebarl3Nav]);

  const handleMouseEnter = (index) => {
    setActiveItem(index);
  };

  const handleMouseLeave = () => {
    setActiveItem(null);
    setHoveredL2Index(null);
  };

  const dropdownVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { opacity: 1, y: 0 },
  };

  const navWeightClassName = useMemo(() => {
    if (globalConfig?.nav_weight === "regular") {
      return styles.fwRegular;
    }
    if (globalConfig?.nav_weight === "bold") {
      return styles.fwBold;
    }
    return styles.fwSemibold;
  }, [globalConfig?.nav_weight]);

  const closeSidebarNav = () => {
    setShowSidebar(false);
    setShowSidebarNav(true);
    setSidebarl2Nav({ state: false });
    setSidebarl3Nav({ state: false });
  };

  useEffect(() => {
    if (reset) {
      closeSidebarNav();
    }
  }, [reset]);

  useEffect(() => {
    if (isRunningOnClient()) {
      setIsClient(true);
      if (showSidebar) {
        document.body.classList.add("remove-scroll");
      } else {
        document.body.classList.remove("remove-scroll");
      }
      return () => {
        document.body.classList.remove("remove-scroll");
      };
    }
  }, [showSidebar]);

  useEffect(() => {
    if (navigationListState[0]?.sub_navigation?.length > 0) {
      redirectToMenu(navigationListState[0], "l2");
    }
  }, [navigationListState]);

  const isHorizontalNav = navigationList?.length > 0 && !isSidebarNav;
  // Modified to enable mega menu for both single and double row layouts
  const isMegaMenu = isHorizontalNav && globalConfig?.header_mega_menu;
  const isFullWidthMegamenu =
    globalConfig?.header_mega_menu_fullwidth && isMegaMenu;
  const getNavigation = navigationList?.slice(0, maxMenuLength);
  const getShopLogoMobile = () =>
    appInfo?.mobile_logo?.secure_url || appInfo?.logo?.secure_url || "";

  const openSidebarNav = () => {
    setShowSidebar(true);
  };

  const getAnimate = (index) => {
    let animate = null;
    if (isClient) {
      if (activeItem === index) {
        animate = { opacity: 1, y: 0 };
      } else {
        animate = { opacity: 0, y: -20 };
      }
    }
    return animate;
  };

  const redirectToMenu = (menu, level) => {
    // console.log(menu, "menumenumenumenumenumenu???", level);
    if (!menu.sub_navigation.length) {
      closeSidebarNav();
    } else {
      if (level === "l2") {
        setShowSidebarNav(false);
        setSidebarl3Nav((prev) => ({ ...prev, title: false, state: false }));

        setSidebarl2Nav({
          state: true,
          title: menu.display,
          navigation: menu.sub_navigation,
        });
      }
      if (level === "l3") {
        setSidebarl2Nav((prev) => ({ ...prev, state: false }));
        setSidebarl3Nav({
          state: true,
          title: menu.display,
          navigation: menu.sub_navigation,
        });
      }
    }
  };

  const goBack = (level) => {
    if (level === "l2") {
      setSidebarl2Nav((prev) => ({ ...prev, state: true }));
      setSidebarl3Nav((prev) => ({ ...prev, title: false, state: false }));
    }
    if (level === "l1") {
      setSidebarl2Nav((prev) => ({ ...prev, title: false, state: false }));
      setShowSidebarNav(true);
    }
  };

  // console.log(navigationList, "navigationList");

  console.log(navigationListState[0].sort_order, "hello");
  return (
    <div className={customClass}>
      {isFullWidthMegamenu ? (
        <MegaMenuLarge
          headerNavigation={getNavigation}
          l1MenuClassName={navWeightClassName}
        ></MegaMenuLarge>
      ) : isMegaMenu ? (
        <MegaMenu
          headerNavigation={getNavigation}
          l1MenuClassName={navWeightClassName}
        ></MegaMenu>
      ) : isHorizontalNav ? (
        <nav className={`${styles.nav} ${customClass}`}>
          <AnimatePresence>
            <motion.ul
              className={`${styles.l1NavigationList} `}
              style={{ fontFamily: '"Helvetica Medium"' }}
              initial={isClient ? "hidden" : undefined}
              animate={isClient ? "visible" : undefined}
              exit={isClient ? "hidden" : undefined}
              variants={dropdownVariants}
              transition={{ duration: 0.3 }}
            >
              {getNavigation?.map((l1nav, index) => (
                <li
                  key={index}
                  className={`${styles.l1NavigationList__item}  ${styles.flexAlignCenter} ${styles.fontBody} ${navWeightClassName}`}
                  onMouseEnter={() => handleMouseEnter(index)}
                  onMouseLeave={handleMouseLeave}
                >
                  {l1nav?.action?.page?.type === "external" ? (
                    <a
                      href={l1nav?.action?.page?.query?.url[0]}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <span
                        className={`${styles.menuTitle} ${styles.flexAlignCenter}`}
                      >
                        <span>{l1nav.display}</span>
                        {/* {l1nav.sub_navigation?.length > 0 && (
                            <ArrowDownIcon
                              className={`${styles.menuIcon} ${styles.dropdownIcon}`}
                            />
                          )} */}
                      </span>
                    </a>
                  ) : (
                    <FDKLink to={convertActionToUrl(l1nav?.action)}>
                      <span
                        className={`${styles.menuTitle} ${styles.flexAlignCenter}`}
                      >
                        <span>{l1nav.display}</span>
                        {/* {l1nav.sub_navigation?.length > 0 && (
                            <ArrowDownIcon
                              className={`${styles.menuIcon} ${styles.dropdownIcon}`}
                            />
                          )} */}
                      </span>
                    </FDKLink>
                  )}

                  <AnimatePresence>
                    {l1nav?.sub_navigation?.length > 0 && (
                      <motion.div
                        className={styles.l2NavigationListWrapper}
                        initial={isClient ? { opacity: 0, y: -20 } : null}
                        animate={() => getAnimate(index)}
                        exit={isClient ? { opacity: 0, y: -20 } : null}
                        transition={{ duration: 0.3 }}
                      >
                        <ul className={styles.l2NavigationList}>
                          {l1nav.sub_navigation.map((l2nav, l2Index) => (
                            <li
                              key={l2nav.display}
                              className={`${styles.l2NavigationList__item} b1 ${styles.fontBody}`}
                              onMouseEnter={() => setHoveredL2Index(l2Index)}
                              onMouseLeave={() => setHoveredL2Index(null)}
                            >
                              <div
                                className={
                                  styles["l2NavigationList__item--container"]
                                }
                              >
                                <FDKLink
                                  to={convertActionToUrl(l2nav?.action)}
                                  className={
                                    styles["l2NavigationList__item--wrapper"]
                                  }
                                >
                                  <span
                                    className={`${styles.menuItem} ${styles.flexAlignCenter} ${styles.justifyBetween}`}
                                  >
                                    <span>{l2nav.display}</span>
                                    {/* {l2nav?.sub_navigation?.length > 0 && (
                                        <ArrowDownIcon
                                          className={`${styles.menuIcon} ${styles.arrowRightIcon}`}
                                        />
                                      )} */}
                                  </span>
                                </FDKLink>
                                <AnimatePresence>
                                  {l2nav.sub_navigation.length > 0 &&
                                    hoveredL2Index === l2Index && (
                                      <motion.ul
                                        className={styles.l3NavigationList}
                                        initial={
                                          isClient
                                            ? { opacity: 0, x: -20 }
                                            : null
                                        }
                                        animate={
                                          isClient ? { opacity: 1, x: 0 } : null
                                        }
                                        exit={
                                          isClient
                                            ? { opacity: 0, x: -20 }
                                            : null
                                        }
                                        transition={{ duration: 0.3 }}
                                      >
                                        {l2nav.sub_navigation.map((l3nav) => (
                                          <li
                                            key={`${l3nav.display}`}
                                            className={`${styles.l3NavigationList__item} b1 ${styles.fontBody}`}
                                          >
                                            <FDKLink
                                              to={convertActionToUrl(
                                                l3nav?.action
                                              )}
                                              className={`${styles["l3NavigationList__item--wrapper"]}`}
                                            >
                                              <span
                                                className={`${styles.menuItem} ${styles.flexAlignCenter}`}
                                              >
                                                <span>{l3nav.display}</span>
                                              </span>
                                            </FDKLink>
                                          </li>
                                        ))}
                                      </motion.ul>
                                    )}
                                </AnimatePresence>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </li>
              ))}
            </motion.ul>
          </AnimatePresence>
        </nav>
      ) : null}

      <button
        className={`${styles.icon} ${styles.flexCenter}`}
        style={{ display: isSidebarNav ? "flex" : "none" }}
        onClick={openSidebarNav}
        aria-label="open navigation"
      >
        <Category
        // className={`${styles.category} ${styles.menuIcon}`}
        />
      </button>
      {/* Sidebar If */}

      <div>
        <motion.div
          className={`${styles.sidebar}`}
          initial={{ x: "-100%" }} // Start off-screen to the left
          animate={{ x: showSidebar ? 0 : "-100%" }} // Animate to 0 when open, back to -100% when closed
          transition={{ duration: 0.1, ease: "easeInOut" }}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            height: "100%",
            backgroundColor: "#F2F2F2",
          }}
        >
          {/* <div
            className={`${styles.sidebar__header} ${styles.flexAlignCenter} ${styles.justifyBetween}`}
            style={{
              display: "flex",
            }}
          > */}
          {/* <FDKLink link="/">
              <img
                style={{
                  maxHeight: `${globalConfig?.mobile_logo_max_height || 24}px`,
                }}
                className={styles.logo}
                src={getShopLogoMobile()}
                loading="lazy"
                alt="logo"
              />
            </FDKLink> */}
          {/* <button
              type="button"
              className={styles.closeIcon}
              onClick={closeSidebarNav}
              aria-label="close"
            >
              <CloseIcon
                className={`${styles.menuIcon} ${styles.crossIcon} ${styles.sidebarIcon}`}
              />
            </button>
          </div> */}
          <nav className={styles.sidebar__navigation}>
            {flag && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "4px 20px 8px",
                  borderBottomLeftRadius: "20px",
                  borderBottomRightRadius: "20px",
                  backgroundColor: "white",
                }}
              >
                <ul
                  key="l1_Nav"
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    backgroundColor: "#F2F2F2",
                    // padding: "8px 12px",
                    borderRadius: "999px",
                    gap: "8px",
                  }}
                >
                  {navigationListState.map((nav, index) => {
                    return (
                      <li
                        key={`${nav.display}-${index}`}
                        className={`${styles["sidebar__navigation--item"]} ${styles.flexAlignCenter} ${styles.justifyBetween} ${styles.fontBody} `}
                        style={{
                          backgroundColor:
                            selectedNav === index ? "#000" : "transparent",
                          color: selectedNav === index ? "white" : "black",
                          borderRadius: "999px",
                          cursor: "pointer",
                        }}
                        onClick={() => setSelectedNav(index)}
                      >
                        {nav?.action?.page?.type === "external" ? (
                          <div
                            href={nav?.action?.page?.query?.url[0]}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={styles.navLink}
                            onClick={() => {
                              setShowSidebar(false);
                              closeSidebarNav();
                            }}
                          >
                            {nav.display}
                          </div>
                        ) : // <></>
                        convertActionToUrl(nav?.action) ? (
                          // <FDKLink
                          //   className={styles.navLink}
                          //   to={convertActionToUrl(nav?.action)}
                          //   onClick={() => {
                          //     setShowSidebar(false);
                          //     closeSidebarNav();
                          //   }}
                          // >
                          //   {nav.display}
                          // </FDKLink>
                          <span
                            className={styles.navLink}
                            onClick={() => {
                              if (nav.sub_navigation?.length > 0) {
                                redirectToMenu(nav, "l2");
                              } else {
                                setShowSidebar(false);
                                closeSidebarNav();
                              }
                            }}
                          >
                            {nav.display}
                          </span>
                        ) : (
                          <span
                            onClick={() => {
                              if (nav.sub_navigation?.length > 0) {
                                redirectToMenu(nav, "l2");
                              } else {
                                setShowSidebar(false);
                                closeSidebarNav();
                              }
                            }}
                          >
                            {nav.display}
                          </span>
                        )}
                        {nav?.sub_navigation?.length > 0 && (
                          <div onClick={() => redirectToMenu(nav, "l2")}>
                            {/* <ArrowDownIcon
                      className={`${styles.arrowRightIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
                      style={{
                        display: "block",
                      }}
                    /> */}
                          </div>
                        )}
                      </li>
                    );
                  })}
                </ul>
                <Search
                  style={{
                    backgroundColor: "#F2F2F2",
                    padding: "12px 16px",
                    height: "50px",
                    width: "56px",
                    borderRadius: "50%",
                  }}
                />
              </div>
            )}
            <div>
              {sidebarl2Nav.state && (
                <div>
                  {/* Back button and title */}
                  {/* <div
                  onClick={() => goBack("l1")}
                  className={`${styles["sidebar__navigation--header"]} ${styles.flexAlignCenter}`}
                  style={{
                    padding: "10px 0 20px 0",
                    borderBottom: "1px solid #E5E5E5",
                    marginBottom: "20px",
                    cursor: "pointer",
                  }}
                >
                  <ArrowDownIcon
                    className={`${styles.arrowLeftIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
                    style={{ marginRight: "10px", transform: "rotate(90deg)" }}
                  />
                  <h3
                    style={{ fontSize: "18px", fontWeight: "600", margin: 0 }}
                  >
                    {sidebarl2Nav.title}
                  </h3>
                </div> */}

                  {/* L2 Navigation List */}
                  <ul
                    key="l2_Nav"
                    style={{ listStyle: "none", padding: "10px 20px" }}
                  >
                    {sidebarl2Nav.navigation.map((nav, index) => (
                      <li
                        key={index}
                        className={`${styles["sidebar__navigation--item"]}`}
                        style={{
                          padding: "0 0 10px 0",
                          borderBottom: "1px solid #F0F0F0",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            flex: 1,
                          }}
                        >
                          {!!nav?.image && (
                            <div
                              className={styles.navigationImage}
                              style={{
                                marginRight: "15px",
                                width: "49px",
                                height: "49px",
                              }}
                            >
                              <img
                                src={nav.image}
                                defer={true}
                                alt={nav?.display}
                                style={{
                                  width: "100%",
                                  height: "100%",
                                  objectFit: "cover",
                                  borderRadius: "8px",
                                }}
                              />
                            </div>
                          )}

                          {convertActionToUrl(nav?.action) ? (
                            <FDKLink
                              className={styles.navLink}
                              to={convertActionToUrl(nav?.action)}
                              style={{
                                fontSize: "12px",
                                fontWeight: "500",
                                color: "#333",
                                textDecoration: "none",
                                flex: 1,
                              }}
                              onClick={() => {
                                if (nav?.sub_navigation?.length) {
                                  console.log(flag, "before");
                                  setflag(false);
                                  console.log(flag, "after");
                                  // setNavigationListState([{ ...nav }]);
                                  redirectToMenu(nav, "l3");
                                } else {
                                  goBack("l1");
                                  closeSidebarNav();
                                }
                              }}
                            >
                              {nav.display}
                            </FDKLink>
                          ) : (
                            <span
                              style={{
                                fontSize: "16px",
                                fontWeight: "500",
                                color: "#333",
                                cursor: "pointer",
                                flex: 1,
                              }}
                              onClick={() => {
                                if (nav?.sub_navigation?.length) {
                                  redirectToMenu(nav, "l3");
                                } else {
                                  goBack("l1");
                                  closeSidebarNav();
                                }
                              }}
                            >
                              {nav.display}
                            </span>
                          )}
                        </div>

                        {nav?.sub_navigation?.length > 0 && (
                          <div
                            onClick={() => {
                              if (nav?.sub_navigation?.length) {
                                // setNavigationListState([{ ...nav }]);
                                redirectToMenu(nav, "l3");
                                setflag(false);
                              } else {
                                goBack("l1");
                                closeSidebarNav();
                              }
                            }}
                            style={{ cursor: "pointer", padding: "5px" }}
                          >
                            <ArrowDownIcon
                              className={`${styles.arrowRightIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
                              style={{
                                display: "block",
                                transform: "rotate(-90deg)",
                              }}
                            />
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* L3 Navigation UI - Simple vertical list */}
              {sidebarl3Nav.state && (
                <div>
                  {/* Back button and title */}
                  <div
                    onClick={() => {
                      goBack("l2");
                      setflag(true);
                    }}
                    className={`${styles["sidebar__navigation--header"]} ${styles.flexAlignCenter}`}
                    style={{
                      padding: "16px 20px",
                      backgroundColor: "white",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      position: "relative",
                      borderBottom: "1px solid #f0f0f0", // Optional: subtle border
                    }}
                  >
                    {/* Circular Back Button */}
                    <div
                      style={{
                        width: "40px",
                        height: "40px",
                        borderRadius: "50%",
                        border: "1px solid #E6E6E6",
                        backgroundColor: "white",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        cursor: "pointer",
                        transition: "all 0.2s ease",
                        position: "absolute",
                        left: "20px",
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.backgroundColor = "#f8f8f8";
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = "white";
                      }}
                    >
                      <ArrowDownIcon
                        className={`${styles.arrowLeftIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
                        style={{
                          width: "20px",
                          height: "20px",
                          transform: "rotate(90deg)",
                          color: "#333",
                        }}
                      />
                    </div>

                    {/* Title */}
                    <h3
                      style={{
                        fontSize: "20px",
                        fontWeight: "600",
                        margin: 0,
                        color: "#333",
                        letterSpacing: "-0.02em",
                      }}
                    >
                      {sidebarl3Nav.title}
                    </h3>
                  </div>

                  {/* L3 Navigation List */}
                  <ul
                    key="l3_Nav"
                    style={{
                      listStyle: "none",
                      padding: "10px 20px",
                    }}
                  >
                    {sidebarl3Nav.navigation.map((nav, index) => (
                      <li
                        key={index}
                        style={{
                          padding: "0 0 10px 0",
                          borderBottom:
                            index < sidebarl3Nav.navigation.length - 1
                              ? "1px solid #F0F0F0"
                              : "none",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            flex: 1,
                          }}
                        >
                          {!!nav?.image && (
                            <div
                              className={styles.navigationImage}
                              style={{
                                marginRight: "15px",
                                width: "49px",
                                height: "49px",
                              }}
                            >
                              <img
                                src={nav.image}
                                defer={true}
                                alt={nav?.display}
                                style={{
                                  width: "100%",
                                  height: "100%",
                                  objectFit: "cover",
                                  borderRadius: "6px",
                                }}
                              />
                            </div>
                          )}

                          {convertActionToUrl(nav?.action) ? (
                            <FDKLink
                              to={convertActionToUrl(nav?.action)}
                              className={styles.navLink}
                              style={{
                                fontSize: "12px",
                                fontWeight: "400",
                                color: "#555",
                                textDecoration: "none",
                                flex: 1,
                              }}
                              onClick={() => closeSidebarNav()}
                            >
                              {nav.display}
                            </FDKLink>
                          ) : (
                            <button
                              type="button"
                              style={{
                                background: "none",
                                border: "none",
                                fontSize: "12px",
                                fontWeight: "400",
                                color: "#555",
                                cursor: "pointer",
                                textAlign: "left",
                                padding: 0,
                                flex: 1,
                              }}
                            >
                              {nav.display}
                            </button>
                          )}
                        </div>

                        <FDKLink
                          to={convertActionToUrl(nav?.action)}
                          onClick={() => closeSidebarNav()}
                          style={{ padding: "5px" }}
                        >
                          <ArrowDownIcon
                            className={`${styles.arrowRightIcon} ${styles.sidebarIcon} ${styles.menuIcon}`}
                            style={{
                              display: "block",
                              transform: "rotate(-90deg)",
                              opacity: 0.6,
                            }}
                          />
                        </FDKLink>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </nav>

          {/* <div className={styles.sidebar__footer}>
            <button
              type="button"
              className={`${styles["sidebar__footer--item"]} ${styles.account} ${styles.flexAlignCenter} ${styles.fontBody} `}
              style={{ display: "flex" }}
              onClick={() => {
                checkLogin("profile_mobile");
                setShowSidebar(false);
              }}
            >
              <UserIcon
                className={`${styles.user} ${styles["sidebar-icon"]} ${styles.menuIcon}`}
              />
              <span>Account</span>
            </button>
            <button
              type="button"
              className={`${styles["sidebar__footer--item"]} ${
                styles.wishlist
              } ${styles.flexAlignCenter} ${styles.fontBody} `}
              onClick={() => {
                checkLogin("wishlist");
                setShowSidebar(false);
              }}
            >
              <WishlistIcon
                className={`${styles.menuIcon}  ${styles.sidebarIcon}${styles.wishlist}`}
              />
              <span>Wishlist</span>
            </button>
          </div> */}
        </motion.div>
      </div>
      {/* eslint-disable jsx-a11y/no-static-element-interactions */}
      <div
        className={`${styles.overlay} ${showSidebar ? styles.show : ""} `}
        onClick={closeSidebarNav}
      />
    </div>
  );
}

export default Navigation;
