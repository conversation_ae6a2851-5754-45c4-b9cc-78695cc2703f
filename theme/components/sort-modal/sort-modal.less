@import "../../styles/main.less";

.contentWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  
}

.modalContent {
  flex: 1;
  padding: 8px;
  overflow-y: auto;

  .sortList {
    display: flex;
    flex-direction: column;
    padding-left: unset;
    @media @mobile {
      padding-top: 22px;
    }
    // row-gap: 8px;
    .sortItem {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      justify-content: flex-end;
      column-gap: 4px;
      font-size: 13px;
      text-transform: capitalize;
      line-height: 14px;
      color: #212529;
      margin: 0 15px;
      padding: 10px 0;
      height: 50px;
      font-weight: 400;

      .radioIcon {
        width: 28px;
        height: 28px;
        // padding: 4px;
        &.selected {
          color: @ButtonPrimary;
        }
      }
    }
  }
  .resetBtn {
    flex: 1;
    padding: 6px 8px;
    margin: 30px 15px 15px;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    border-top: 1px solid black;
    border-bottom: 1px solid black;
    font-size: 13px;
    text-transform: capitalize;
    font-family: "Circular Medium" !important;
  }
}

.modalFooter {
  padding: 14px 16px;
  display: flex;
  column-gap: 16px;

  .resetBtn {
    // .btnSecondary();
  }
  .applyBtn {
    .btnPrimary();
  }
  .resetBtn,
  .applyBtn {
    flex: 1;
    padding: 15px 28px;
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;
  }
}
