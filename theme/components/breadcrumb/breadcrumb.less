@import "../../styles/main.less";

// .breadcrumbs {
//   .captionNormal();
//   span {
//     color: black;

//     &.active {
//       color: @ButtonPrimary;
//     }
//   }
// }

@import "../../styles/main.less";

.breadcrumbs {
  .captionNormal();
  display: flex;
  align-items: center;
  font-family: "Helvetica Medium";
  font-size: clamp(12px, 0.73vw, 14px);
  color: @Dark-60;
  w .breadcrumbItem {
    color: @Dark-60;
    text-align: center;
    font-family: "Helvetica Medium";
    font-size: clamp(12px, 0.73vw, 14px);
    font-style: normal;
    text-align: center;
    font-style: normal;
    font-weight: 400;
    line-height: 130%; /* 18.2px */
    letter-spacing: 0.28px;
    leading-trim: both;
    text-edge: cap;
  }

  .crumbItem {
    // if you need spacing between items
    &:not(:last-child) {
      margin-right: 4px;
    }
  }

  .separator {
    color: var(--Dark-60, #666);
    margin: 0 4px;
  }
}
