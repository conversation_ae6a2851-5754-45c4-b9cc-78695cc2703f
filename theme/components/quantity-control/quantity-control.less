@import "../../styles/main.less";

.quantityCtrlContainer {
  display: flex;
  min-width: 143px;
  height: 50px;
  padding: 12px 0px;
  justify-content: space-between;
  align-items: center;

  .decreaseCount {
    // @media @mobile {
    //   width: 28px;
    // }

    .svgContainer {
      display: flex;
      width: 40px;
      height: 40px;
      padding: 12px 20px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
      border-radius: 999px;
      border: 1px solid #e6e6e6;
      background: #fff;
      box-sizing: border-box;

      svg {
        width: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .increaseCount {
    .svgContainer {
      display: flex;
      width: 40px;
      height: 40px;
      padding: 12px 20px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
      border-radius: 999px;
      border: 1px solid #e6e6e6;
      background: #fff;
      box-sizing: border-box;

      svg {
        width: 12px;
        height: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  // .decreaseCount {
  //   border-radius: 4px 0 0 4px;
  // }
  // .increaseCount {
  //   border-radius: 0 4px 4px 0;
  // }
  .count {
    color: @Dark-80;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold" !important;
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
    // & > span {

    //   border: none;
    //   width: 100%;
    //   height: 100%;
    //   box-sizing: border-box;
    //   vertical-align: middle;
    //   text-align: center;

    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    // }
  }
}
