@import "../../../styles/main.less";

.modal {
  position: sticky;
  inset: 0;
  bottom: 0;
  z-index: @header;
  overflow-y: auto;
  overflow-x: hidden;
  transition: opacity 0.25s ease;
  .flex-center();
  @media @tablet {
    align-items: flex-end;
  }
  &:before {
    content: "";
    background-color: @Overlay;
    opacity: 0;
    inset: 0;
    position: fixed;
    z-index: -1;
    transition: opacity 0.3s ease;
  }

  // &.active {
  //   &:before {
  //     // opacity: 0.6;
  //   }
  // }

  .modalContainer {
    max-width: 484px;
    // max-height: 828px;
    max-height: clamp(650px, 43.13vw, 828px);

    background-color: @DialogBackground;
    border: 1px solid @White;
    border-radius: @border-radius;
    min-height: 100px;
    // max-width: 720px;
    overflow: auto;
    transition: transform 0.3s ease-out;
    @media @tablet {
      width: 100%;
      max-width: unset;
    }

    .stripContainer {
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: sticky;
      top: 0;
      z-index: 84;
      background-color: @White;

      .strip {
        width: 50px;
        height: 4px;
        background-color: @Dark-10;
        border-radius: 50px;
      }
      @media (min-width: 769px) {
        display: none;
      }
    }
  }
}

.leftModal {
  justify-content: flex-start;
  align-items: center;

  .modalContainer {
    position: sticky;
    top: calc(130px + clamp(120px, 9.74vw, 187px));
    // height: 65%;
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease-out;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;

    @media (max-width: 768px) {
      top: auto; /* remove the top offset */
      bottom: 0; /* pin to bottom */
      width: 100%; /* full width */
      height: 80%; /* height by content or override as you like */
      transform: translateY(100%);
      transition: transform 0.3s ease-out;
      z-index: 1000;
    }
  }

  &.active .modalContainer {
    transform: translateX(0);
  }
}

.rightModal {
  justify-content: flex-end;
  // height: fit-content;

  .modalContainer {
    position: absolute;
    top: 20%;
    height: 25%;
    width: 20%;
    margin-left: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;

    @media @tablet {
      width: 100%;
    }
  }

  &.active .modalContainer {
    transform: translateX(0);
  }
}
