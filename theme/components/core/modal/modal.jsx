import React, { useEffect, useRef, useState } from "react";
import styles from "./modal.less";

function Modal({
  isOpen,
  isCancelable = true,
  childHandleFocus = false,
  modalType = "",
  closeDialog,
  children,
}) {
  const modalRef = useRef(null);
  const modalContainerRef = useRef(null);
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    // Handle initial open state
    if (isOpen) {
      // Make modal visible first
      setTimeout(() => {
        // Then trigger animation after a brief delay
        setIsActive(true);
      }, 10);
    } else {
      // Remove active class first to trigger animation out
      setIsActive(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && !childHandleFocus && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen, childHandleFocus]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        modalContainerRef.current &&
        !modalContainerRef.current.contains(event.target) &&
        isCancelable
      ) {
        closeDialog();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [closeDialog, isCancelable]);

  return (
    isOpen && (
      <div
        role="button"
        className={`${styles.modal} ${modalType === "right-modal"
          ? styles.rightModal
          : modalType === "left-modal"
            ? styles.leftModal
            : ""
          } ${isActive ? styles.active : ""}`}
        ref={modalRef}
        tabIndex="0"
        onKeyDown={(e) => e.key === "Escape" && isCancelable && closeDialog()}
      >
        <div className={styles.modalContainer} ref={modalContainerRef}>
          <div className={styles.stripContainer}>
            <div className={styles.strip}></div>
          </div>
          {children}

        </div>
      </div>
    )
  );
}

export default Modal;