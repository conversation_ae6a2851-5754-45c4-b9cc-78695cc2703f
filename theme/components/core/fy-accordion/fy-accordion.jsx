import React, { useState, useRef, useEffect } from "react";
import styles from "./fy-accordion.less";
import MinusIcon from "../../../assets/images/minus.svg";
import AddIcon from "../../../assets/images/add.svg";

function FyAccordion({ isOpen: initialIsOpen, children }) {
  const [isOpenState, setIsOpenState] = useState(initialIsOpen);
  const [maxHeight, setMaxHeight] = useState("0px");
  const contentRef = useRef(null);

  const toggleAccordion = () => {
    setIsOpenState(!isOpenState);
  };

  useEffect(() => {
    if (isOpenState && contentRef.current) {
      setMaxHeight(`${contentRef.current.scrollHeight}px`);
    } else {
      setMaxHeight("0px");
    }
  }, [isOpenState, children[1]]); // remeasure if content changes

  return (
    <div className={styles.accordion}>
      <button
        type="button"
        className={`${styles.flexAlignCenter} ${styles.justifyBetween} ${styles.accordionHead}`}
        onClick={toggleAccordion}
      >
        <div>{children[0]}</div>
        <div className={styles.accordion__icon}>
          {isOpenState ? <MinusIcon /> : <AddIcon />}
        </div>
      </button>
      <div
        className={`${styles.accordion__content} ${isOpenState ? styles.open : ""}`}
        ref={contentRef}
        style={{ maxHeight }}
      >
        <div className={styles.accordion__inner}>{children[1]}</div>
      </div>
    </div>
  );
}

export default FyAccordion;
