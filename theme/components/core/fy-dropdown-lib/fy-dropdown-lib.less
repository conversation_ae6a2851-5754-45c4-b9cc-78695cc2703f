@import "../../../styles/main.less";

.dropdownContainer {
  display: flex;
  flex-direction: column;
  position: relative;

  .error {
    color: @ErrorText;
    font-weight: 400;
    font-size: 12px;
    padding-top: 5px;
  }

  .emptyDiv {
    position: fixed;
    background: transparent;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: -1;
  }

  .dropdown {
    box-sizing: border-box;
    border: 1px solid @DividerStokes;
    border-radius: @ButtonRadius;
    position: relative;
    display: flex;
    flex-direction: column;

    &.dropDownError {
      border-color: @ErrorText;
    }

    .dropdownButton {
      min-height: 22px;
      padding: 10px 14px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:not([disabled]) {
        cursor: pointer;
      }

      .dropdownIcon {
        transition: all linear 0.3s;
        height: 24px;
        width: 24px;

        &.open {
          transform: rotate(180deg);
        }
      }

      .selectedValue {
        font-size: 14px;
        line-height: 22px;
        color: @TextHeading;
      }
    }
  }
}

.dropdownList {
  max-height: 200px;
  overflow-y: auto;
  border-radius: @ButtonRadius;
  padding: 8px 0;
  z-index: 999;
  box-shadow:
    0 4px 8px 0 hsla(0, 0%, 90%, 0.2),
    0 6px 20px 0 hsla(0, 0%, 90%, 0.19);
  border: 1px solid @DividerStokes;
  display: none;
  opacity: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s;
  background-color: @PageBackground;

  .listWrapper {
    overflow: hidden;
    max-height: 184px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 10px;
    }

    &::-webkit-scrollbar-thumb {
      border: 4px solid rgba(0, 0, 0, 0);
      background-clip: padding-box;
      border-radius: 3px;
      background-color: green;
    }
    &::-webkit-scrollbar-track {
      background: orange;
    }
  }

  &.open {
    display: block;
    opacity: 1;
    pointer-events: all;
  }

  .dropdownOption {
    position: relative;
    font-size: 14px;
    color: @TextBody;
    line-height: 22px;
    padding: 10px 14px;
    cursor: pointer;
    transition: all 0.3s;

    &.hover {
      &:hover {
        background: @HighlightColor;
      }
    }

    &.noOption {
      cursor: default;
    }
  }
}
.label {
  color: @TextLabel;
  font-size: 12px;
  line-height: 21px;
}

.clear_icon {
  margin: 0 12px;
}

.text_field {
  border: none;
  width: 100%;
  color: @TextBody;
  &::placeholder {
    color: @TextLabel;
  }
  &:disabled::placeholder {
    color: @TextSecondary;
  }
  // To be removed once viewport meta is updated
  @media @mobile {
    font-size: 16px;
  }
}

// .label {
//   position: absolute;
//   top: 0;
//   bottom: 0;
//   left: 14px;
//   display: flex;
//   align-items: center;
//   pointer-events: none;
// }

// .label .text {
//   transition: all 0.15s ease-out;
//   color: grey;
// }

// .text_field:focus {
//   outline: none;
//   // border: 2px solid blue;
// }

// // ,:not(.text_field[value=""]) + .label .text
// .text_field:focus + .label .text {
//   font-size: 12px;
//   transform: translate(0, -100%);
//   background-color: white;
//   padding-left: 4px;
//   padding-right: 4px;
// }

// .text_field:focus + .label .text {
//   color: blue;
// }
