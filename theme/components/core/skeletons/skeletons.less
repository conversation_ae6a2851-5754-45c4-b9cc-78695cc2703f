@import "../../../styles/main.less";

.card {
  z-index: unset;
  position: relative;
  width: 100%;
  height: fit-content;
  background-color: #ececec;
  overflow: hidden;

  canvas {
    display: block;
    width: 100%;
    transform: translateX(-100%);
    background: -webkit-gradient(
      linear,
      left top,
      right top,
      from(transparent),
      color-stop(rgba(255, 255, 255, 0.5)),
      to(transparent)
    );
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.5),
      transparent
    );
    animation: loading 0.8s infinite;

    @media @tablet {
      display: none;
    }
  }

  .mobileCanvas {
    display: none;

    @media @tablet {
      display: block;
    }
  }
}

@keyframes loading {
  100% {
    transform: translateX(100%);
  }
}
