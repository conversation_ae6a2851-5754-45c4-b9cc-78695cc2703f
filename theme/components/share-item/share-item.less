@import "../../styles/main.less";

.share-popup-overlay {
  position: absolute;
  top: 45px;
  right: 0px;
  .upArrow {
    position: absolute;
    right: 8px;
    top: -14px;
    @media @tablet {
      display: none;
    }
  }
  @media @tablet {
    position: fixed;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    left: 0;
    top: 0;
    z-index: 99;
  }
}
.overlay-share {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
}

.share-popup {
  @media @tablet {
    width: 340px;
    border: 1px solid @ThemeAccentD3;
  }
  box-shadow: 0px 4px 10px 0px #0000001a;
  background-color: @color-white;
  @media @desktop {
    border-radius: 4px;
  }
  @media @tablet {
    top: 44px;
    right: 0;
  }
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  .icons {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    @media @tablet {
      padding: unset;
      gap: 20px;
      justify-content: center;
      flex-direction: row;
    }
    span {
      display: flex;
      align-items: center;
      padding: 8px;
      gap: 8px;
      a {
        width: 24px;
        height: 24px;
      }
    }
  }
  @media @tablet {
    position: fixed;
    bottom: 0;
    top: unset;
    left: 0;
    width: 100%;
    z-index: 6;
    padding-bottom: 32px;
    border: 0;
    gap: 16px;
  }
  .popup-title {
    display: none;
    color: @ThemeAccentD5;
    @media @tablet {
      display: flex;
    }
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    .close-icon {
      width: 24px;
      height: 24px;
      display: none;
      cursor: pointer;
      @media @tablet {
        display: inline-block;
      }
    }
  }

  .copy-input {
    display: none;
    width: 100%;
    padding-top: 16px;

    @media @tablet {
      display: flex;
      padding-top: 0px;
    }
    input {
      height: 41px;
      padding: 12px;
      width: calc(100% - 92px);
      background-color: @ThemeAccentD1;
      color: @ThemeAccentD5;
      border: 0;
      @media @tablet {
        font-size: 12px;
      }
    }
    button {
      width: 92px;
      height: 41px;
      padding: 12px;
      font-weight: 600;
      @media @tablet {
        font-size: 12px;
      }
      color: @color-white;
      background: @ButtonPrimary;
      border: 0;
      text-transform: uppercase;
      &.white-btn {
        color: @ButtonPrimary;
        background: @color-white;
        border: 1px solid #000;
      }
    }
  }
}
