import React, { useState } from "react";
import styles from "./filter-modal.less";
import Modal from "../core/modal/modal";
import FilterList from "../../page-layouts/plp/Components/filter-list/filter-list";
import { useNavigate } from "react-router-dom";
import SvgWrapper from "../core/svgWrapper/SvgWrapper";
import plusIcon from "../../assets/images/plus.png";
import minusIcon from "../../assets/images/minus.png";
import FilterTags from "../../page-layouts/plp/Components/filter-tags/filter-tags";
import Sort from "../../page-layouts/plp/Components/sort/sort";
import FyAccordion from "../core/fy-accordion/fy-accordion";

/**
 * FilterModal is a React component that renders a modal for applying filters.
 *
 * @param {Object} props - The properties object.
 * @param {boolean} [props.isOpen=true] - Determines if the modal is open.
 * @param {Array} [props.filters=[]] - An array of filter objects to be displayed.
 * @param {Object} [props.selectedFilters={}] - An object mapping filter keys to arrays of selected values.
 * @param {boolean} [props.isResetFilterDisable=false] - Flag to disable the reset filter button.
 * @param {Function} [props.onCloseModalClick=() => {}] - Callback function triggered when the modal close button is clicked.
 * @param {Function} [props.onResetBtnClick=() => {}] - Callback function triggered when the reset button is clicked.
 * @param {Function} [props.onApplyBtnClick=() => {}] - Callback function triggered when the apply button is clicked.
 * @param {Function} [props.onFilterUpdate=() => {}] - Callback function triggered when a filter is updated.
 * @param {number} props.productCount - Number of products to display.
 * @param {Array} props.sortList - List of sort options.
 * @param {Function} [props.onSortUpdate=() => {}] - Callback for sort updates.
 *
 * @returns {JSX.Element} A modal component for filter selection.
 */
function FilterModal({
  isOpen = true,
  filters = [],
  selectedFilters = {},
  isResetFilterDisable = false,
  onCloseModalClick = () => { },
  onResetBtnClick = () => { },
  onApplyBtnClick = () => { },
  onFilterUpdate = () => { },
  productCount = 0,
  sortList = [],
  onSortUpdate = () => { },
  size_selection_style = "",
}) {
  const [openFilters, setOpenFilters] = useState([]);
  // Local state for size selections
  const [selectedSizes, setSelectedSizes] = useState([]);
  const navigate = useNavigate();
  const [mobileSortOpen, setMobileSortOpen] = useState(false);


  const handleFilterToggle = (name) => {
    setOpenFilters((prev) =>
      prev.includes(name) ? prev.filter((n) => n !== name) : [...prev, name]
    );
  };

  const handleModalClose = () => {
    onCloseModalClick();
    setOpenFilters([]); // reset open filters when closing
  };

  // Handle clicks on size boxes: update local UI state and propagate to parent
  const handleSizeClick = (keyName, value) => {
    setSelectedSizes((prev) =>
      prev.includes(value) ? prev.filter((s) => s !== value) : [...prev, value]
    );
    onFilterUpdate(keyName, value);
  };
  const handleSortUpdate = (value) => {
    const searchParams = new URLSearchParams(location.search);
    if (value) searchParams.set("sort_on", value);
    else searchParams.delete("sort_on");
    searchParams.delete("page_no");

    navigate({ pathname: location.pathname, search: searchParams.toString() });
  };

  // Determine if a non-size value is selected via props
  const isValueSelected = (keyName, value) => {
    return selectedFilters[keyName]?.includes(value);
  };
  return (
    <Modal
      isOpen={isOpen}
      modalType="left-modal"
      closeDialog={handleModalClose}
      title="Filter"
      hideHeader={true}

    >
      {/* Header */}
      <div className={styles.filterHeader}>
        <div className={styles.filterHeaderInner}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
          >
            <path d="M2.25 5.25H15.75" stroke="#1A1A1A" strokeWidth="2" strokeLinecap="round" />
            <path d="M4.5 9H13.5" stroke="#1A1A1A" strokeWidth="2" strokeLinecap="round" />
            <path d="M7.5 12.75H10.5" stroke="#1A1A1A" strokeWidth="2" strokeLinecap="round" />
          </svg>
          <h4 className={styles.filterTitle}>Filters</h4>
        </div>
      </div>

      {/* Selected Tags & Sort */}




      {/* Filter Lists */}
      <div className={styles.contentWrapper}>
        <div className={styles.modalContent}>
          <div className={`${styles.selectedTags} ${Object.keys(selectedFilters).length > 0 ? '' : styles.hide}`}>
            <FilterTags
              selectedFilters={selectedFilters}
              onFilterUpdate={onFilterUpdate}
              onResetFiltersClick={onResetBtnClick}
            />
          </div>
          <div className={styles.sortContainer} onClick={() => setMobileSortOpen((prev) => !prev)}>
            <Sort
              sortList={sortList}
              onSortUpdate={handleSortUpdate}
              mobileSortOpen={mobileSortOpen}
              setMobileSortOpen={setMobileSortOpen}
            />
            <div
              className="mobileToggleIcon" // your Tailwind or LESS class here

            >
              <SvgWrapper
                svgSrc={mobileSortOpen ? "minus" : "plus"}
              />
            </div>

          </div>
          <ul className={styles.filterNameList}>
            {filters.map((item) => (
              <li key={item.key.name} className={styles.filterBlock} 
                onClick={() => handleFilterToggle(item.key.name)}
              >


                <button
                  className={`${styles.filterNameItem} ${openFilters.includes(item.key.name) ? styles.active : ''}`}

                >
                  <span className={styles.filterNameTitle}>
                    {item.key.display}
                    {!openFilters.includes(item.key.name) &&
                      item.values.filter((v) => v.is_selected).length > 0 && (
                        <span className={styles.filterCount}>
                          {item.values.filter((v) => v.is_selected).length}
                        </span>
                      )}
                  </span>

                  <img
                    src={openFilters.includes(item.key.name) ? minusIcon : plusIcon}
                    alt="icon"
                  />
                </button>
                <div
                  className={`${styles.accordionPanel} ${openFilters.includes(item.key.name) ? styles.open : ''}`}
                >
                  <FilterList
                    filter={item}
                    isCollapsedView={false}
                    onFilterUpdate={onFilterUpdate}
                    size_selection_style={size_selection_style}
                  />
                </div>

              </li>

            ))}
          </ul>
        </div>
      </div>

      {/* Footer */}
      <div className={styles.modalFooter}>
        <button
          className={styles.resetBtn}
          onClick={onResetBtnClick}
          disabled={isResetFilterDisable}
        >
          Reset ({Object.values(selectedFilters).flat().length})
        </button>
        <button className={styles.showBtn} onClick={onApplyBtnClick}>
          Show {productCount} Products
        </button>
      </div>
    </Modal>
  );
}

export default FilterModal;
