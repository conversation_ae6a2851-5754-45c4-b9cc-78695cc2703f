@import "../../styles/main.less";

// default variables
@sidebar-width: 140px;
@header-padding: 16px;
@footer-padding: 16px;
@item-padding-vert: 12px;
@item-padding-horiz: 16px;
@ButtonPrimary: #ff3b30;
@ActiveBackground: #ffedee;
@DividerStokes: #e0e0e0;
@HoverBackground: #f5f5f5;

// Font setup
@font-family-base: "Helvetica Now Display", sans-serif;

// Size filter specific styles
.sizeFilterContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  padding: 16px 0;
}

.sizeBox {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: white;
  font-family: @font-family-base;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0 12px;

  border: 0.803px solid var(--Dark-20, #ccc);

  &:hover {
    border-color: #999;
  }
}

.sizeBoxSelected {
  background-color: @ButtonPrimary;
  background: var(--Brand, #ff1e00);
  color: #fff;
  border-color: @ButtonPrimary;
}

// Regular filter styles
.regularFilterContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 8px 0;
}

.filterItem {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  font-family: @font-family-base;

  &:hover {
    color: @ButtonPrimary;
  }
}

.filterCheckbox {
  margin-right: 12px;
}

.filterItemLabel {
  flex: 1;
  font-size: 14px;
}

.filterItemCount {
  color: #999;
  font-size: 12px;
}

// Existing styles
.filterModal {
  width: 100%;
  height: 828px;
}

.modalWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: @DialogBackground;
}

.header {
  display: flex;
  align-items: center;
  padding: @header-padding;
  border-bottom: 1px solid @DividerStokes;
  width: 100%;

  .backButton {
    background: none;
    border: none;
    margin-right: 8px;
    cursor: pointer;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
    font-family: @font-family-base;
  }
}

.selectedTags {
  padding: 8px @item-padding-horiz;
  border-bottom: 1px solid @DividerStokes;
  width: 100%;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  height: 590px;

  // max-height: clamp(320px, 31.25vw, 600px);
  overflow-y: auto;
  width: 100%;
  @media (max-width: 1028px) {
    max-height: 328px;
  }
  @media (min-width: 1028px) and (max-width: 1650px) {
    max-height: 400px;
  }

  @media @tablet {
    max-height: 328px ;
  }
}

.filterCount {
  display: flex;
  width: 16px;
  height: 16px;
  padding: 3px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 50%;
  background-color: #ff1e00;
  color: white;
  font-size: 10px;
  font-weight: 500;
  line-height: 130%;
  letter-spacing: 0.2px;
}

.modalContent {
  // flex: 1;
  overflow-y: scroll;

  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  // .leftPane {
  //   overflow-y: auto;
  //   border-right: 1px solid @DividerStokes;
  //   width: 100%;

  .filterNameList {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .filterBlock {
      // margin-bottom: 8px;
      width: 100%;
      padding: clamp(16px, 1.56vw, 30px) clamp(20px, 2.08vw, 40px)
        clamp(16px, 1.56vw, 30px) clamp(20px, 3.13vw, 60px);
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      flex-direction: column;
      gap: 1.875rem;
      // cursor: pointer;

      @media @tablet {
        padding: 1.25rem;
        gap: 1.5rem;
      }
      // @media (max-width: 480px) {
      //   padding: 1.25rem 1.25rem;
      // }
    }

    .toggleIcon {
      width: 16px;
      height: 16px;
      min-width: 16px; // Prevents icon from shrinking
    }

    .accordionPanel {
      max-height: 0;
      overflow: hidden;
      display: none;
      transition: max-height 0.3s ease;
      width: 100%;
    }

    .accordionPanel.open {
      max-height: fit-content; /* enough to fit inner content */
      transition: max-height 0.5s ease;
      // padding: 0 16px 12px;
      display: flex;
      width: 100%;
      padding: 0.5rem;
    }
  }

  .filterNameItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 0.75rem 1rem;
    cursor: pointer;
    width: 100%;

    // &:hover {
    //   background-color: @HoverBackground;
    // }

    &.active {
      font-weight: 500;
      // border-right: 3px solid @ButtonPrimary;
      // background-color: @ActiveBackground;
      border-bottom: none;
    }

    .titleIconContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .filterNameTitle {
      display: flex;
      align-items: center;
      gap: 11px;
      font-size: clamp(12px, 0.83vw, 16px);
      line-height: 130%;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: "Helvetica Bold";
      @media (max-width: 768px) {
        font-size: 16px;
      }
    }
  }
  // }

  .rightPane {
    flex-grow: 1;
    overflow-y: auto;
    padding: 8px;
    width: 100%;
  }
}

.modalFooter {
  display: flex;
  align-items: center;
  column-gap: 1rem;
  width: 100%;
  position: sticky;
  bottom: 0;
  z-index: 40;
  padding: clamp(16px, 1.56vw, 30px) clamp(20px, 2.08vw, 40px)
    clamp(16px, 1.56vw, 30px) clamp(20px, 3.13vw, 60px);
  border-top: 1px solid rgb(224, 224, 224);
  background: white;
  @media (max-width: 768px) {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 40;
    padding: 1.25rem;
  }

  // @media (min-width: 769px) and (max-width: 1200px) {
  // padding: 0.75rem; // tablets
  // font-size: 12px;
  // }

  .resetBtn,
  .showBtn {
    flex: 1;
    padding: clamp(8px, 0.63vw, 12px) clamp(16px, 1.56vw, 30px);
    display: flex;
    // height: 3.125rem; /* 50px = 3.125rem */
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-family: "Helvetica Bold" !important;
    font-size: clamp(12px, 0.83vw, 14px);
    width: 100%;
  }

  .resetBtn {
    background: white;
    border-radius: var(--radius-round, 999px);
    border: 1px solid var(--Dark, #1a1a1a);
    color: var(--Dark, #1a1a1a);
    font-size: clamp(12px, 0.73vw, 14px);
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
    font-family: "Helvetica Bold";

    &:disabled {
      opacity: 0.5;
      cursor: default;
    }
  }

  .showBtn {
    color: #ffffff;
    border-radius: var(--radius-round, 999px);
    background: @Dark;
  }
}

// Header section previously styled with Tailwind
.filterHeader {
  width: 100%;
  display: flex;
  padding: clamp(12px, 1.04vw, 20px) clamp(12px, 1.04vw, 20px)
    clamp(12px, 1.04vw, 20px) clamp(20px, 3.13vw, 60px);
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
  border-bottom: 1px solid @Dark-10;
  position: sticky;
  top: 0;
  background: white;
  z-index: 40;

  @media (max-width: 768px) {
    padding: 0.625rem 1.25rem 1.25rem 1.25rem;
    top: 24px;
  }
  .filterHeaderInner {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    width: 100%;
  }

  .filterTitle {
    font-family: "Helvetica Bold";
    font-size: 16px;
    font-weight: 700;
    line-height: 130%;
    color: @Dark;
  }
}
.selectedTags {
  display: flex;
  flex-wrap: wrap;

  padding: clamp(12px, 1.04vw, 20px) clamp(12px, 1.04vw, 20px)
    clamp(12px, 1.04vw, 20px) clamp(20px, 3.13vw, 60px);
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
  border-bottom: 1px solid var(--Dark-10, #e6e6e6);
  color: black;
  background: white;

  @media (max-width: 768px) {
    padding: 10px 20px;
  }
}
.hide {
  display: none;
}

.sortContainer {
  display: flex;
  padding: 1.25rem 1.25rem 1.25rem 1.25rem;
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
  align-self: stretch;
  border-bottom: 1px solid @Dark-10;
  color: @Dark;
  width: 100%;
  background: white;
  @media @desktop {
    display: none;
  }
}

.filterTagsContainer {
  display: flex;
  padding: 20px 20px 20px 60px;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
  @media @tablet {
    padding: 0px;
  }
}
