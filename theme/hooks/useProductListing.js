// useProductListing.js (simplified)
import { useState, useEffect, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useGlobalStore } from "fdk-core/utils";
import { PLP_PRODUCTS } from "../queries/plpQuery";
import { fireCustomGtmEvent, isRunningOnClient } from "../helper/utils";

const PAGE_SIZE = 12;

export default function useProductListing({ fpi }) {
  const location = useLocation();
  const navigate = useNavigate();
  const i18nDetails = useGlobalStore(fpi.getters.i18N_DETAILS);
  const productsData = useGlobalStore(fpi.getters.PRODUCTS) || {};
  const { items = [], page } = productsData;
  const isClient = useMemo(() => isRunningOnClient(), []);

  const [productList, setProductList] = useState(items);
  const [loading, setLoading] = useState(true);
  function appendDelimiter(queryString) {
    const searchParams = isClient ? new URLSearchParams(queryString) : null;
    const params = Array.from(searchParams?.entries() || []);

    const result = params.reduce((acc, [key, value]) => {
      // Skip only pagination and sort parameters
      if (key === "page_no" || key === "sort_on") {
        return acc;
      }

      // Handle all other parameters including search query
      acc.push(`${key}:${value}`);
      return acc;
    }, []);

    // Join with ::: delimiter
    return result.join(":::");
  }
  useEffect(() => {
    // fetch whenever currency or query string changes
    const searchParams = new URLSearchParams(location.search);
    const payload = {
      pageType: "number",
      first: 1,
      filterQuery: appendDelimiter(searchParams.toString()) || undefined,
      sortOn: searchParams.get("sort_on") || undefined,
      currency: i18nDetails.currency.code,
      pageNo: Number(searchParams.get("page_no")) || 1,
    };

    setLoading(true);
    fpi
      .executeGQL(PLP_PRODUCTS, payload)
      .then((res) => {
        const items = res.data.products.items || [];
        setProductList(items);
        fireCustomGtmEvent("custom.product_list.view", {
          products: res.data.products,
          totalProducts: items.length,
        });
      })
      .finally(() => setLoading(false));
  }, [location.search, i18nDetails.currency.code, fpi]);

  const onLoadMore = () => {
    const nextPage = (page?.current || 1) + 1;
    const searchParams = new URLSearchParams(location.search);
    searchParams.set("page_no", nextPage);
    navigate({ pathname: location.pathname, search: searchParams.toString() });
  };

  return { productList, loading, onLoadMore, page };
}
