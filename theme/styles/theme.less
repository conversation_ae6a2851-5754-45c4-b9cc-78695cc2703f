// Colors Pallet
@PageBackground: var(--pageBackground, #f8f8f8);
@ThemeAccent: var(--themeAccent, #e7dbc2);
@ThemeAccentD1: var(--themeAccentD1, #b9af9b);
@ThemeAccentD2: var(--themeAccentD2, #8b8374);
@ThemeAccentD3: var(--themeAccentD3, #5c584e);
@ThemeAccentD4: var(--themeAccentD4, #2e2c27);
@ThemeAccentD5: var(--themeAccentD5, #000);
@ThemeAccentL1: var(--themeAccentL1, #ece2ce);
@ThemeAccentL2: var(--themeAccentL2, #f1e9da);
@ThemeAccentL3: var(--themeAccentL3, #f5f1e7);
@ThemeAccentL4: var(--themeAccentL4, #f9f6f0);
@ThemeAccentL5: var(--themeAccentL5, #fff);
@TextHeading: var(--textHeading, #26201a);
@TextBody: var(--textBody, #3c3131);
@TextLabel: var(--textLabel, #7d7676);
@TextSecondary: var(--textSecondary, #9c9c9c);
@ButtonPrimary: var(--buttonPrimary, #4e3f09);
@ButtonPrimaryL1: var(--buttonPrimaryL1, #71653a);
@ButtonPrimaryL3: var(--buttonPrimaryL3, #b8b29d);
@ButtonSecondary: var(--buttonSecondary, #fff);
@ButtonLink: var(--buttonLink, #b1655b);
@ButtonLinkL1: var(--buttonLinkL1, #c1847c);
@ButtonLinkL2: var(--buttonLinkL2, #d0a39d);
@SaleBadgeBackground: var(--saleBadgeBackground, #fff);
@SaleBadgeText: var(--saleBadgeText, #1c958f);
@SaleDiscountText: var(--saleDiscountText, #1c958f);
@SaleTimer: var(--saleTimer, #116e1c);
@HeaderBackground: var(--headerBackground, #f3f3ed);
@HeaderNav: var(--headerNav, #261a1a);
@HeaderIcon: var(--headerIcon, #261a1a);
@FooterBackground: var(--footerBackground, #2c231e);
@FooterBottomBackground: var(--footerBottomBackground, #231812);
@FooterHeadingText: var(--footerHeadingText, #d9d9d9);
@FooterBodyText: var(--footerBodyText, #6d6d6d);
@FooterIcon: var(--footerIcon, #fff);
@DialogBackground: var(--dialogBackground, #fff);
@Overlay: var(--overlay, #14130e);
@DividerStokes: var(--dividerStokes, #d4d1d1);
@HighlightColor: var(--highlightColor, #a7a7f8);
@SuccessBackground: var(--successBackground, #c2dbc9);
@SuccessText: var(--successText, #1c958f);
@ErrorBackground: var(--errorBackground, #e6d5d5);
@ErrorText: var(--errorText, #b24141);
@InformationBackground: var(--informationBackground, #ebd3bc);
@InformationText: var(--informationText, #d28f51);
@ImageRadius: var(--imageRadius, 0px);
@BadgeRadius: var(--badgeRadius, 0px);
@ButtonRadius: var(--buttonRadius, 0px);
@ButtonRadius-small: var(--buttonRadius-small, 10px);
@ProductImgAspectRatio: var(--productImgAspectRatio, 0.8);
@backgroundPlp: var(--backgroundPlp, #f2f2f2);
@BackgroundCart: #f2f2f2;
@BackgroundPdp: #f2f2f2;

@Alabaster2: #f8f8f8;
@DustyGray: #979797;
@DustyGray2: #9b9b9b;
@Profit: #20ce81;
@Mako2: #4242ad;
@LightGray: #eeeeee;
@Required: #ee485d;
@White: #ffffff;
@Gray: #cccccc;
@Mako: #41434c;
@Black: #000000;
@Iron: #e4e5e6;
@Dark: #1a1a1a;
@Dark-2: #433e2f;
@Dark-5: #f2f2f2;
@Dark-10: #e6e6e6;
@Dark-20: #cccccc;
@Dark-40: #999999;
@Dark-60: #666;
@Dark-80: #333;
@Dark-85: #262626;
@Brand: #ff1e00;
@White: #ffffff;
@RadiusRound: 999px;
@RadiusSmall: 10px;
@RadiusMain: 20px;
@ContainerBGImage: linear-gradient(
  to bottom,
  rgba(0, 0, 0, 0.05),
  rgba(0, 0, 0, 0.1)
);

/** Font family **/
@CurrencyFont: Verdana, Geneva, Tahoma, sans-serif;

/** Font weight **/
@bold: 700;

//To be deprecated
@TextDisabled: var(--textSecondary);
@FooterText: var(--footerHeadingText);
@FooterLinkBody: var(--footerBodyText);
