@import "./main.less";

@lg-min: 1024px;

.productListingPage {
  position: relative;
}

.addToCartContainer {
  @media @desktop {
    max-width: unset !important;
  }
}
.productListingPage {
  // margin-top: 16px;
}
.mobileHeaderWrapper {
  display: none;
  @media (max-width: 768px) {
    display: block;
  }
}
.plpWrapper {
  position: relative;
  padding-bottom: 80px;
  background-color: @backgroundPlp;
  min-height: 100vh;

  .loader {
    position: relative;
    height: 90vh;
    width: 100%;

    .loaderContainer {
      position: absolute;
      height: 100%;
      background: transparent;

      .customLoader {
        margin-left: 0;
      }
    }
  }

  .contentWrapper {
    isolation: isolate;
    padding: 2.5rem;
    // margin-top: calc(clamp(120px, 9.74vw, 187px) + clamp(40px, 4.17vw, 80px));

    @media (max-width: 768px) {
      margin-top: 0px;
      padding: 20px;
    }

    .left {
      flex: 0 0 259px;
      margin-right: 16px;
      z-index: 1;
      max-width: 259px;

      @media @tablet {
        display: none;
      }

      .filter {
        &:not(:last-child) {
          border-bottom: 1px solid @DividerStokes;
          margin-bottom: 20px;
        }
      }
    }

    .right {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      gap: 2.5rem;

      @media @tablet {
        gap: 1rem;
      }
      .rightHeader {
      }

      .filterTags {
        // display: none;
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: center;
        width: 100%;

        @media @tablet {
          display: none;
        }
      }

      .bannerContainer {
        &.mobileBanner {
          display: none;
        }

        @media @mobile {
          &.mobileBanner {
            display: flex;
          }

          &.desktopBanner {
            display: none;
          }
        }
        .redirectionLink {
          display: flex;
          width: 100%;

          .banner {
            width: 100%;
            border-radius: @ImageRadius;
            cursor: pointer;
          }
        }
      }

      .plp-container {
        /deep/.list-items {
          background: none;
        }
      }

      .viewMoreWrapper,
      .paginationWrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 24px;

        .viewMoreBtn {
          .btnSecondary();
          background: transparent;
          padding: 12px 32px;

          &:hover {
            background-color: @ButtonPrimary;
          }
        }
      }
    }
  }

  .plp__desc {
    background: @ThemeAccentL2;
    border: 1px solid @DividerStokes;
    border-radius: 12px;
    padding: 16px;
    margin-top: 24px;

    @media @desktop {
      margin-top: 36px;
    }

    &--content {
      color: @TextBody;
    }

    &--read-more {
      padding: 12px;
      background: transparent;
      margin-top: 16px;

      @media @desktop {
        &:hover {
          background-color: @ButtonPrimary;
        }
      }
    }
  }

  .sort__modal,
  .filter__modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 100;
    background-color: @DialogBackground;

    &--content {
      flex-grow: 1;
    }

    &--footer {
      padding: 14px 16px;
      display: flex;
      .column-gap(16px);

      .actionBtn {
        flex: 1;
        padding: 15px 28px;
        font-size: 12px;
        font-weight: 500;
        line-height: 14px;
      }
    }
  }

  .sort__modal {
    &--header {
      padding: 16px;
      border-bottom: 1px solid @DividerStokes;

      .close-icon {
        fill: @TextHeading;
        cursor: pointer;
        width: 24px;
        height: 24px;
      }
    }

    &--content {
      padding: 8px;
      overflow-y: auto;

      .sort-list {
        & > li:not(:last-child) {
          margin-bottom: 8px;
        }

        .sort-item {
          font-size: 12px;
          line-height: 14px;
          color: @TextHeading;

          .radio-icon {
            width: 28px;
            height: 28px;
            padding: 4px;
            margin-right: 8px;

            &.selected {
              color: @ButtonPrimary;
            }
          }
        }
      }
    }
  }

  .filter__modal {
    &--header {
      padding: 16px;
      border-bottom: 1px solid @DividerStokes;

      .close-icon {
        fill: @TextHeading;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
    }

    &--content {
      overflow: hidden;
      display: flex;

      .leftPane {
        flex: 0 0 122px;
        overflow-y: auto;
        border-right: 1px solid @DividerStokes;

        .filter-title {
          padding: 14px 16px;
          border-bottom: 1px solid @DividerStokes;

          &.active {
            border-right: 2px solid @ButtonPrimary;
          }
        }
      }

      .rightPane {
        flex-grow: 1;
        overflow-y: auto;
        padding: 8px;
      }
    }
  }

  .back-top {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    color: @ThemeAccentD2;
    box-shadow:
      0px 8px 8px -4px rgba(0, 0, 0, 0.6),
      0px 4px 6px -4px rgba(0, 0, 0, 0.12);
    background-color: @DialogBackground;
    border-radius: 24px;
    transition: top 0.25s ease-in-out;
    border: none;
    position: fixed;
    top: calc(var(--topPosition, var(--headerHeight)) + 69px);
    z-index: 1;
    right: 50%;
    z-index: 1;
    transform: translateX(50%);

    & > .arrow-top-icon {
      fill: currentColor;
      width: 8px;
      height: 11px;
    }

    @media @desktop {
      justify-content: center;
      top: unset;
      right: 40px;
      bottom: 40px;
      width: 40px;
      height: 40px;

      & > .text {
        display: none;
      }

      & > .arrow-top-icon {
        width: 10px;
        height: 14px;
      }
    }
  }
}

.topSectionWrapper {
  // background-color: white;
  // position: fixed;
  // top: 130px;
  // z-index: 10;
  // width: 100%;
  // @media @tablet {
  //   display: none;
  // }
}
.breadcrumbWrapperDesktop {
  // padding: 1.25rem 2.5rem;
  background: @Dark-10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  min-height: clamp(120px, 9.74vw, 187px);
  @media @tablet {
    display: none;
  }

  .title {
    color: @Dark;
    text-align: center;
    leading-trim: both;
    text-edge: cap;
    font-family: "Helvetica Bold";
    font-size: clamp(24px, 2.5vw, 48px);
    font-style: normal;
    font-weight: 700;
    line-height: 150%; /* 72px */
    letter-spacing: 0.48px;
  }

  @media (max-width: 768px) {
    display: none;
  }

  // @media (max-height: 768px) {
  //   display: none;
  // }
  // @media @tablet {
  //   display: none;
  // }
}
.breadcrumbWrapperMobile {
  text-align: center;
  margin-top: 24px;
  padding: 0 32px;
  @media @mobile {
    display: none;
  }
}

.filterHeaderContainer {
  padding-bottom: 16px;
  border-bottom: 1px solid @DividerStokes;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .filterHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      .h4(mobile);
    }
    .resetBtn {
      .btn-link();
      &:disabled {
        cursor: default;
      }
    }
  }
}

// .mobileHeader {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   padding: 13px 16px;
//   box-shadow: 0px 4px 8px 0px #a6a3a524;
//   margin-bottom: 24px;
//   background-color: #fff;
//   z-index: 1;
//   top: 0;
//   left: 0;
//   right: 0;
//   position: fixed;
//   transition: top 0.4s ease-in-out;
//   top: var(--topPosition, var(--headerHeight));
//   &.active {
//   }
//   .homeIcon {
//     width: 16px;
//     height: 16px;
//     cursor: pointer;
//     display: none;
//   }
//   @media @desktop {
//     display: none;
//   }
//   .headerLeft {
//     display: flex;
//     align-items: center;
//     button {
//       .b1();
//       display: flex;
//       align-items: center;
//       column-gap: 4px;
//       color: @TextHeading;
//       &.filterBtn {
//         padding-right: 16px;
//         border-right: 1px solid @DividerStokes;
//         margin-right: 16px;
//       }
//     }
//   }
//   .headerRight {
//     display: flex;
//     align-items: center;
//   }
// }

.rightHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  @media (max-width: 768px) {
    display: none;
  }

  .headerLeft {
    display: flex;
    padding: 10px 0px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-right: 4rem;

    @media @desktop {
      column-gap: 8px;
    }
    .title {
      .text-line-clamp();
      .h4();
    }
    .productCount {
      display: flex;
      align-items: center;
      white-space: nowrap;
      color: #000;
      text-align: center;
      leading-trim: both;
      text-edge: cap;
      font-family: "Helvetica Medium";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 150%; /* 24px */
      letter-spacing: 0.16px;
      .b1();
    }
  }
  .headerRight {
    display: flex;
    align-items: center;
    @media @tablet {
      display: none;
    }
  }
  .fourGrid,
  .fiveGrid {
    width: 4px;
    height: 24px;
    border-radius: 1px;
    background: var(--Dark-20, #ccc);

    &.active {
      border-radius: 1px;
      background: var(--Dark-60, #666);
    }
  }
}

.colIconBtn {
  padding: 4px;
  border: 1px solid transparent;
  border-radius: 2px;
  cursor: pointer;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  color: white;

  &:not(:last-child) {
    margin-right: 8px;
  }
  &.active {
    border-radius: 1px;
    border: 1px solid @Dark-60;
  }
  &.tablet {
    @media @mobile {
      display: none;
    }
  }
  &.mobile {
    @media @mobile-up {
      display: none;
    }
  }
}

.productContainer {
  --item-count: var(--mobile-col, 1);
  display: grid;
  grid-auto-rows: auto;
  grid-template-rows: 1fr;
  min-height: 127px;

  /* Dynamic gap based on grid layout */
  --column-gap: 10px;
  --row-gap: 10px;

  /* Mobile-specific gap tweaks */
  @media (max-width: 767px) {
    --item-count: var(--mobile-col, 1);
    /* 1 product per row */
    &[style*="--mobile-col: 1"] {
      --column-gap: 10px;
      --row-gap: 20px;
    }
    /* 2 products per row */
    &[style*="--mobile-col: 2"] {
      --column-gap: 10px;
      --row-gap: 20px;
    }
    /* 3 products per row */
    &[style*="--mobile-col: 3"] {
      --column-gap: 10px;
      --row-gap: 10px;
    }
  }

  /* Tablet-specific gap tweaks */
  @media (min-width: 769px) and (max-width: 1024px) {
    --item-count: var(--tablet-col, 3);
    /* 1 product per row on tablet */
    &[style*="--tablet-col: 1"] {
      --column-gap: 10px;
      --row-gap: 20px;
    }
    /* 2 products per row on tablet */
    &[style*="--tablet-col: 2"] {
      --column-gap: 10px;
      --row-gap: 20px;
    }
    /* 3 products per row on tablet */
    &[style*="--tablet-col: 3"] {
      --column-gap: 10px;
      --row-gap: 10px;
    }
  }

  /* Desktop overrides: only 4 or 5 columns */
  @media (min-width: 1025px) {
    --item-count: var(--desktop-col, 4);
    /* Default desktop gaps */
    --column-gap: 16px;
    --row-gap: 64px;
    /* Only allow 4 or 5 columns on desktop */
    &[style*="--desktop-col: 4"] {
      --column-gap: 16px;
      --row-gap: 64px;
    }
    &[style*="--desktop-col: 5"] {
      --column-gap: 16px;
      --row-gap: 64px;
    }
  }

  /* Apply the dynamic gap */
  gap: var(--row-gap) var(--column-gap);
  grid-template-columns: repeat(var(--item-count), 1fr);
}

:global {
  html {
    @media @tablet {
      overscroll-behavior: none;
    }
  }
}
