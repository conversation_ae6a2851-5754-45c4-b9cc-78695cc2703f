@import "../main.less";

.mobilecollectionContainer {
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding: 0 20px;
}

.mobilecollectionCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  &.active {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.mobilecollectionHeader {
  padding: 34px 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  background: transparent;
}

.mobileactiveHeader {
  background: white;
  padding: 30px;
}

.mobilecollectionInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobilecollectionName {
  font-size: 18px;
  font-family: "Helvetica Bold" !important;
  color: #1A1A1A;
  line-height: 130%;
}

.mobileproductCount {
  font-size: 18px;
  color: #CCC;
  font-family: "Helvetica Bold" !important;
  line-height: 130%;
}

.mobileviewAllBtn {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  font-family: "Helvetica Bold" !important;
  padding: 0;
  transition: color 0.2s ease;

  &:hover {
    color: #333;
  }
}

.mobileproductsContainer {
  overflow: hidden;
  background-color: white;
  transition: height 1s cubic-bezier(0.4, 0, 0.2, 1);

}

.mobileproductsContent {
  padding-bottom: 30px;
}

.mobileproductSlider {
  background-color: white;
  position: relative;
  padding: 0 0px 20px;
}

.mobilecleanCarousel {
  :global {
    .slick-list {
      margin: 0 !important;
      transform: none !important;
      width: 100% !important;
      padding: 0 !important;
    }

    .slick-track {
      display: flex !important;
      align-items: stretch;
      gap: 12px;
    }

    .slick-slide {
      height: auto !important;
      margin: 0;

      >div {
        height: 100%;
        padding: 0 !important;
      }
    }

    .slick-dots {
      display: none !important;
    }
  }
}

.mobilesliderItem {
  padding: 0 6px;
  height: 100%;

  >div {
    background: white;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }
  }
}

.mobilenoProductsMessage {
  background: white;
  padding: 40px 20px;
  text-align: center;
  color: #666;
  animation: fadeIn 0.3s ease-in-out;
}

.mobileloadingContainer {
  padding: 20px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobileslideWrap {
  :global {
    .slick-track {
      display: flex !important;
      justify-content: center;
      align-items: stretch !important;
      transform: none !important;
    }

    .slick-slide {
      height: auto !important;

      &>*,
      &>*>* {
        height: 100%;
      }
    }
  }
}

.mobileplaceholderScroll {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;

  >div {
    aspect-ratio: 1;
    border-radius: 16px;
    overflow: hidden;
    background: #f0f0f0;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

.mobileimageGallery {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  transition: transform 1s ease;
}

@media (max-width: 374px) {
  .mobilesectionHeading {
    font-size: 40px;
  }

  .mobileTitleSpan {
    font-size: 20px;
  }

  .mobilecollectionContainer {
    max-width: 100%;
    padding: 0 20px;
  }
}

.cleanCarousel {
  position: relative;
  background: white;
  border-radius: 12px;

  :global {
    .slick-list {
      margin: 0 !important;
      transform: none !important;
      width: 100% !important;
      padding: 0 !important;
    }

    .slick-track {
      display: flex !important;
      align-items: stretch;
      gap: 16px;
    }

    .slick-slide {
      height: auto !important;
      margin: 0;

      >div {
        height: 100%;
        padding: 0 !important;
      }

      @media @desktop {
        width: 100%;
      }

      @media @tablet {
        width: 100%;
      }
    }

    .slick-dots {
      display: none !important;
    }
  }
}

.carouselNavigation {
  position: relative;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.carouselNavBtn {
  position: absolute;
  top: 45%;
  transform: translateY(-106%);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.prevIcon {
  transform: rotate(180deg);
}

.prevBtn {

  left: -8px;
  transform: translateY(-600%);

}

.nextBtn {
  right: 22px;
  transform: translateY(-600%)
}

.sliderItem {
  padding: 40px 0;
  height: 100%;

  >div {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: transform 1s ease, box-shadow 1s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
  }
}

.cleanCarousel {

  .customNextBtn,
  .customPrevBtn {
    display: none;
  }
}

.cleanCarousel {
  :global {
    .slick-slide>div>div {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}

.addToCartContainer {
  @media @desktop {
    max-width: unset !important;

    .addToCartBody {
      overflow-y: hidden;
    }
  }
}

.sectionWrapper {
  padding-bottom: 158px;
  position: relative;

  @media @tablet-strict {
    padding: 28px 30px 0 30px;
  }

  @media @mobile {
    padding-bottom: 60px;
  }

  .visibleOnMobile {
    @media @desktop {
      display: none;
    }
  }
}

.titleBlock {
  padding-inline: @SectionInlinePaddingDesktop;
  padding-bottom: 60px;
  padding-top: 100px;
  margin-left: 84px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  @media @tablet {
    padding-inline: @SectionInlinePaddingTablet;
    padding-bottom: 8px;
  }

  .sectionHeading {
    font-size: 48px;
    font-family: "Helvetica Bold" !important;
    color: white;

    @media @tablet {
      font-size: 32px;
    }
  }

}

.imageGrid {
  padding-inline: @SectionInlinePaddingDesktop;
  display: grid;
  gap: 32px 24px;
  grid-template-columns: repeat(var(--per_row), 1fr);

  &.singleItem {
    display: flex;
    justify-content: center;

    &>* {
      flex: 0 1 calc(100% / var(--per_row));
    }
  }

  @media @tablet {
    padding-inline: @SectionInlinePaddingTablet;
    gap: 24px 16px;
    grid-template-columns: repeat(var(--brand-item), 1fr);
  }
}

.imageGalleryItem {
  @media (min-width: 1900px) {
    height: 500px;
    width: 400px;
  }

  @media (min-width: 1024px) and (max-width: 1900px) {
    height: 400px;
    width: 300px;
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    height: 300px;
  }

  @media (min-width: 481px) and (max-width: 767px) {
    height: 200px;
  }

}

.imageGallery {
  border-radius: @ImageRadius;

  -webkit-mask-image: -webkit-radial-gradient(white, black); //safari fix
}

.pos-relative {
  position: relative;
}

.gap-above-button {
  margin-top: 1rem;
  text-align: center;

  @media @tablet-strict {
    margin-top: 16px;
  }

  @media @desktop {
    margin-top: 32px;
  }

  &.visibleOnDesktop {
    @media @tablet {
      display: none;
    }

    @media @desktop {
      margin: 0 40px 82px 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.gap-above-button-horizontal {
  margin-top: 1rem;
  text-align: center;

  @media @tablet-strict {
    margin-top: 16px;
  }

  @media @desktop {
    margin-top: 64px;

    &.lessGap {
      margin-top: 24px;
    }
  }
}


.slideWrap {
  max-width: 100vw;
  padding: 0 10px;



  :global {
    .slick-track {
      display: flex !important;
      justify-content: center;
      align-items: stretch !important;
      transform: none !important;
    }

    .slick-slide {
      height: auto !important;

      &>*,
      &>*>* {
        height: 100%;
      }
    }

    .slick-list {
      padding-left: 5px !important;
    }
  }
}

.productSlider {
  position: relative;
  margin: 0 38px 0 36px;
  overflow: hidden;
  --gap: 24px;
  z-index: 30;

  @media (min-width: 768px) and (max-width: 1024px) {
    margin: 0 20px 0 20px;
  }

  .sliderItem {
    padding: 40px 0;
    height: 100%;
    width: 100%;
  }

  &.bannerSlider {
    position: relative;

    @media @desktop {
      padding-inline: 0;
    }
  }
}

.customNextBtn,
.customPrevBtn {
  width: 48px;
  height: 48px;
  position: absolute;
  top: 45%;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: @PageBackground;
  border-radius: 50%;

  @media @tablet {
    display: none;
  }

  svg>path {
    stroke: @TextHeading;
  }
}

.customNextBtn {
  right: 10px;
  transform: translateY(106%);
}

.customPrevBtn {
  left: 25px;
  transform: translate(-90px, 106%) rotate(180deg);
}

.placeholderScroll,
.placeholderGrid {
  padding: 0 24px;
}

.placeholderScroll {
  display: flex;

  &>* {
    flex: 0 0 calc(100% / var(--per_row));
  }
}

@media @desktop {
  .desktopVisible {
    display: block;
  }

  .desktopVisibleFlex {
    display: flex;
  }

  .desktopHidden {
    display: none;
  }

  .desktopHiddenFlex {
    display: none;
  }

  .hideOnDesktop {
    display: none;
  }
}

@media @tablet {
  .mobileVisible {
    display: block;
  }

  .mobileHidden {
    display: none;
  }

  .hideOnDesktop {
    display: none;
  }
}

@media @mobile {
  .hideOnMobile {
    display: none;
  }

  .hideOnDesktop {
    display: block;
  }
}

.collectionTabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  padding-left: 85px;
  padding-right: 70px;
  overflow: hidden;

  @media (min-width: 768px) and (max-width: 1024px) {
    padding-left: 69px;
    padding-right: 20px;
  }
}

.tabsContainer {
  display: flex;
  align-items: center;
  overflow-x: visible;
  overflow-y: visible;
  white-space: nowrap;
  position: relative;
  // transition: all 5s ease-in-out; 

  &::-webkit-scrollbar {
    display: none;
  }
}

.tabButton {
  position: relative;
  background: white;
  cursor: pointer;
  font-family: "Helvetica Medium" !important;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  padding: 32px 50px;
  font-size: 24px;
  white-space: nowrap;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  transition: height 1s ease-in-out;
}

.tabButton::after {
  width: 45px;
  height: 45px;
  content: "";
  position: absolute;
  bottom: 0;
  right: -44px;
  background-color: white;
  clip-path: path('M1 0C1 24.9 19.6875 45 45 45H0V0H1Z');
}

.tabButton::before {
  width: 45px;
  height: 45px;
  content: "";
  position: absolute;
  bottom: 0;
  transform: rotate(270deg);
  left: -44px;
  background-color: white;
  clip-path: path('M1 0C1 24.9 19.6875 45 45 45H0V0H1Z');
}

.activeTab {
  color: #000;
  font-family: "Helvetica Bold" !important;
  position: relative;
  z-index: 2;
  padding: 40px 50px 32px 50px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: white;
  margin-bottom: -2px;
  box-shadow: 0px 5px 40px 0px rgba(0, 0, 0, 0.30);
}

.leftOfActive {
  margin-bottom: -20px;
}

.rightOfActive {
  margin-bottom: -20px;
}

.productCount {
  opacity: 0.5;
  margin-left: 4px;
  font-size: 20px;
}

.viewAllTab {
  border: 1px solid #fff;
  color: #fff;
  border-radius: 20px;
  font-size: 24px;
  cursor: pointer;
  transition: background 0.3s ease;
  font-family: "Helvetica Bold" !important;
  display: inline-flex;
  height: 50px;
  padding: 12px 30px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.productCount {

  color: var(--Dark-20, #CCC);
  font-family: "Helvetica Bold" !important;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 115%;
}

.noProductsMessage {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-size: 16px;
}


@media (min-width: 768px) and (max-width: 1024px) {

  .sectionWrapper {
    padding-bottom: 100px;
  }

  .cleanCarousel {
    border-radius: 12px;

    :global {
      .slick-slide {
        width: 100%;
      }
    }

    .customNextBtn,
    .customPrevBtn {
      display: none;
    }
  }

  .carouselNavigation {
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    gap: 12px;
  }

  .carouselNavBtn {
    width: 48px;
    height: 48px;
    top: 45%;
    transform: translateY(-600%);
  }

  .prevBtn {
    left: 2px;
  }

  .nextBtn {
    right: 0;
  }

  .sliderItem {
    padding: 40px 0;

    >div {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
    }
  }

  .cleanCarousel :global {
    .slick-slide>div>div {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .collectionTabs {
    // margin-left: 69px;
    margin-right: 20px;
  }

  .tabButton {
    padding: 24px 32px;
    font-size: 20px;
  }

  .tabButton&::after {
    width: 40px;
    height: 40px;
    right: -39px;
    clip-path: path('M1 0C1 22 17.5 40 40 40H0V0H1Z');
  }

  .tabButton&::before {
    width: 40px;
    height: 40px;
    left: -39px;
    clip-path: path('M1 0C1 22 17.5 40 40 40H0V0H1Z');
  }

  .activeTab {
    padding: 32px 32px;
    font-size: 20px;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.2);
  }

  .productCount {
    font-size: 20px;
  }

  .viewAllTab {
    font-size: 20px;
    height: 45px;
    padding: 10px 24px;
  }

  .noProductsMessage {
    font-size: 14px;
    padding: 32px 16px;
  }
}


.mobilesectionWrapper {
  position: relative;
}

.mobiletitleBlock {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 44px 23px;
}

.mobilesectionHeading {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  color: white;
}

.mobileTitleSpan {
  font-size: 26px;
  font-family: "Helvetica Bold" !important;
  color: red;
}