@import "../main.less";
@viewAllCtaRight: 16px;

.addToCartContainer {
  @media @desktop {
    max-width: unset !important;
    .addToCartBody {
      overflow-y: hidden;
    }
  }
}

.sectionWrapper {
  padding-inline: @SectionInlinePaddingMobile;
  --gap: 16px;
  @media @mobile-up {
    padding-inline: @SectionInlinePaddingTablet;
  }
  @media @desktop {
    padding-inline: @SectionInlinePaddingDesktop;
    --gap: 24px;
  }

  .titleBlock {
    color: @TextHeading;
    display: flex;
    margin: 0 auto;
    position: relative;
    margin-bottom: 32px;
    h2 {
      margin-right: 12px;
    }
    &.isViewAllCta {
      padding-right: 55px + @viewAllCtaRight;
    }
    @media @tablet {
      margin-bottom: 24px;
    }
    @media @mobile {
      margin-bottom: 16px;
      &.isViewAllCta {
        padding-right: 48px + @viewAllCtaRight;
      }
    }

    .viewAllCta {
      position: absolute;
      right: @viewAllCtaRight;
      font-size: 14px;
      color: @ButtonPrimary;
      @media @mobile {
        font-size: 12px;
      }
    }
  }
  .moveCenter {
    text-align: center;
    justify-content: center;
    &.isViewAllCta {
      padding-right: 55px + @viewAllCtaRight;
      padding-left: 55px + @viewAllCtaRight;
    }
    @media @mobile {
      &.isViewAllCta {
        padding-right: 48px + @viewAllCtaRight;
        padding-left: 48px + @viewAllCtaRight;
      }
    }
  }

  .navigationBlockWrapper {
    width: 100%;
    overflow-x: auto;
    scrollbar-width: none;
    margin-bottom: 32px;
    @media @tablet {
      margin-bottom: 24px;
    }
    @media @mobile {
      margin-bottom: 16px;
    }
    &.moveCenter {
      text-align: center;
    }

    .navigationBlock {
      display: inline-flex;
      column-gap: 12px;
      .navigation {
        padding: 8px;
        border: 1px solid @DividerStokes;
        border-radius: @ButtonRadius;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        letter-spacing: -0.28px;
        color: @TextBody;

        .iconImage {
          border-radius: @ButtonRadius;
          width: 32px;
          height: 32px;
          min-width: 32px;
          display: flex;
          justify-content: center;
          @media @mobile {
            width: 32px;
            height: 32px;
            min-width: 32px;
          }
        }
      }

      .activeLink {
        background-color: @ButtonPrimary;
        color: @ButtonSecondary;
      }
    }
  }

  .productContainer {
    .slideWrap {
      max-width: 100vw;
      .gap-above-button {
        @media @desktop {
          margin-top: 64px;
        }
      }
      @media @mobile {
        padding: 0;
      }
      .sliderView {
        position: relative;
        padding: 0 calc(var(--gap) / 2);
        height: auto;
      }
      :global {
        .slick-list {
          margin: 0 calc(var(--gap) / 2 * -1) !important;
          @media @mobile {
            padding-left: 0 !important;
          }
        }
        .slick-track {
          display: flex !important;
        }
        .slick-slide {
          height: inherit !important;
          & > div,
          & > div > div {
            height: 100%;
          }
        }
      }
    }
  }
}
.alignViewAll {
  line-height: 42px;
  padding-left: 12px;
  @media @mobile {
    line-height: 36px;
  }
}
.hideOnMobile {
  @media @mobile {
    display: none;
  }
}
.showOnMobile {
  @media @mobile-up {
    display: none;
  }
}