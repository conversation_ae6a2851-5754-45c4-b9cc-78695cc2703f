@import "../main.less";

.media_text {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  font-family: "Helvetica Bold" !important;

  @media @mobile {
    width: 100%;
  }

  .media_text__info {
    display: flex;
    flex-direction: column;

    .media_text__cta {
      @media @mobile {
        max-width: 199px;
      }

      @media @tablet-strict {
        max-width: 240px;
      }
    }
  }

  @media @mobile-up {
    gap: 2rem;
  }

  @media @tablet-strict {
    gap: 3rem;
    flex-direction: row;
    padding: 60px 40px;
    background-color: #E6E6E6;
  }

  @media @desktop {
    gap: 223px;
    flex-direction: row;
    padding: 140px 198px;
    background-color: #E6E6E6;
  }

  @media screen and (min-width: 820px) and (max-width: 1440px) {
    flex-direction: row;
    padding: 140px 158px;
    gap: 150px;
    background-color: #E6E6E6;
  }

  &__image-wrapper {
    position: relative;
    overflow: hidden;

    @media @tablet-strict {
      flex-basis: 60%;
    }

    @media @desktop {
      flex-basis: 65.417%;

      .imageWrapper {
        border-radius: 0 @ImageRadius @ImageRadius 0;
      }
    }
  }

  &__info {
    flex: 1;
    padding: 0 @SectionInlinePaddingMobile;

    @media @tablet-strict {
      padding: 0 @SectionInlinePaddingTablet;
      flex-basis: 40%;
    }

    @media @desktop {
      padding: 0;
    }
  }

  &__heading {
    // @media @mobile {
    //   .h3(desktop);
    // }

    // @media @tablet-strict {
    //   .h3(desktop);
    // }
    font-family: "Helvetica Bold" !important;
  }

  &__description {
    // .b2(desktop);
    // color: @TextBody;
    // margin-top: 1rem;

    // @media @tablet-strict {
    //   margin-top: 0.75rem;
    //   .b2(desktop);
    // }
    padding-top: 64px;
    font-family: "Helvetica Medium" !important;

    @media screen and (min-width: 820px) and (max-width: 1440px) {
      padding-top: 50px;
      font-family: "Helvetica Medium" !important;
    }

    @media @mobile {
      padding-top: 20px;
    }
  }



  &__cta {
    // .btn-font(desktop);
    text-decoration: none;
    margin-top: 1rem;
    // margin-top: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    // width: 100%;
    padding: 12px 30px;
    font-size: 16px;
    // text-align: center;
    font-family: "Helvetica Bold" !important;
    border-radius: 999px;
    background-color: #1A1A1A;
    color: #E6E6E6;

    @media @desktop {
      // margin-top: 1.5rem;
      margin-top: 64px;
      // max-width: 264px;
      // width: 100%;
    }

    @media screen and (min-width: 820px) and (max-width: 1440px) {
      margin-top: 50px;
    }

    @media @tablet-strict {
      margin-top: 1.25rem;
      padding: 14px 32px;
      width: 64%;
    }

    @media @desktop {
      // margin-top: 2rem;
    }
  }
}

.carouselContainer {
  overflow: hidden;
  position: relative;

  @media @mobile {
    width: 100%;
    height: 100%;
  }
}

.carouselItems {
  display: flex;
  scroll-snap-type: x mandatory;
  overflow-x: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  height: 100%;
  width: 100%;

  // @media screen and(min-width: 1471px) and (max-width: 2540px){
  //   width: 702px ;
  //   height: 850px;
  // height: 850px;
  // height: 100%;
  // }
  @media (min-width: 1471px) {
    width: 702px;
  }

  @media screen and (max-width: 1470px) and (min-width: 1030px) {
    width: 520px;
  }

  // @media screen and (min-width: 900px) and (max-width: 1430px) {
  //   width: 100% ;
  // }

  @media @mobile {
    width: 50%;
  }

  @media @tablet {
    width: 100%;
    height: 100%;
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

.carouselItem {
  flex: 0 0 100%;
  scroll-snap-align: start;
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 30px;


}

/* New wrapper for carousel item content */
// .carouselItemContent {
//   position: relative;
//   width: 100%;
//   height: 100%;
//   border-radius: 30px;
//   overflow: hidden;
// }

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 30px;
}

.carouselProgressBarWrapper {
  position: absolute;
  top: 20px;
  // left: 50%;
  // transform: translateX(-50%);
  display: flex;
  justify-content: center;
  gap: 10px;
  z-index: 20;
  cursor: pointer;
  width: 100%;
  max-width: 670px;

  @media @mobile {
    top: 30px;
    width: 100%;
    padding: 0 20px;
  }
}

/* Progress bar positioned inside image at the top */

.progressTrack {
  height: 4px;
  width: 100px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.progressFill {
  height: 100%;
  background-color: #ff0000;
  width: 0%;
  transition: width 0.1s linear;
  border-radius: 2px;
}



@media @tablet-strict {
  .carouselContainer {
    width: 400px;
    height: 500px;
  }

  .carouselItems {
    // width: 60%;
    // max-width: 400px;
    width: 100%
  }

  .carouselItem {
    width: 100%;
    padding: 10px;
  }

  .carouselItemContent {
    border-radius: 25px;
    max-width: 380px;
    max-height: 480px;
    margin: 0 auto;
  }

  .image {
    width: 100%;
    height: 100%;
    max-height: 480px;
    object-fit: cover;
    border-radius: 25px;
  }

  .carouselProgressBarWrapper {
    top: 20px;
    gap: 8px;
  }

  .progressTrack {
    width: 50px;
    height: 3px;
  }
}

@media (max-width: 767px) {
  .heroVideoCarousel {
    height: auto;
    padding: 10px 20px 0 20px;
  }

  .carouselContainer {
    // height: 432px;
  }

  .carouselProgressBarWrapper {
    top: 30px;
    gap: 8px;
  }

  .progressTrack {
    height: 3px;
    width: 47px;
  }

  .carouselItems {
    width: 100%;
  }

  .carouselItem {
    width: 100%;
    padding: 20px 18px;
  }

  .carouselItemContent {
    border-radius: 20px;
  }

  .image {
    width: 100%;
    border-radius: 20px;
  }
}

@media screen and (min-width: 930px) and (max-width: 1470px) {
  .progressTrack {
    height: 4px;
    width: 65px;
  }
}

.mobile_button {
  @media @mobile {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
  }
}

.mobile_title {
  @media @mobile {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}