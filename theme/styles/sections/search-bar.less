@import "../main.less";
@import "../../components/header/styles/desktop-header.less";
@lg-min: 1024px;

.searchflex {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  gap: 8px;
  transition: all 0.3s ease;

  &.searchFocused {
    width: 100%;

    // Hide other icons when search is focused
    :global {
      .headerIcon:not(.search--closeIcon) {
        display: none;
      }
    }
  }
}

.searchIcon {
  display: flex;
}

.search {
  position: absolute;
  .inset(0);
  background-color: @HeaderBackground;
  z-index: 1;

  @media @tablet {
    display: flex;
    align-items: center;
  }

  @media @desktop {
    padding: 20px 0;
  }

  &__wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    padding: 8px 16px;

    @media @desktop {
      // width: 800px;
      padding: 0;
      margin-left: auto;
      margin-right: auto;
    }
  }

  &__input {
    position: relative;
    flex-grow: 1;

    &--text {
      background: @ThemeAccentL4;
      width: 100%;
      // padding: 7px 32px;
      font-size: 14px;
      line-height: 20px;
      border-radius: 4px;
      border: 1px solid @DividerStokes;
      font-family: "Helvetica Medium";

      @media @desktop {
        font-size: 14px;
        // padding: 9px 38px;
      }

      @media @mobile {
        font-size: 14px;
      }

      &::placeholder {
        color: @TextSecondary;
        font-weight: 700;
        font-size: 14px;
        line-height: 130%;
        letter-spacing: -0.28px;

        @media @tablet {
          font-size: 12px;
          line-height: 16px;
          letter-spacing: -0.24px;
        }
      }
    }

    &--removeSpace {
      // padding: 9px 38px 9px 12px;

      @media @mobile {
        // padding: 8px;
      }
    }

    &--search-icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
      position: absolute;
      top: 45%;
      transform: translateY(-50%);
      left: 8px;
      fill: #928b8b;

      @media @desktop {
        width: 24px;
        height: 24px;
        top: 50%;
      }
    }
  }

  &--closeIcon {
    margin-left: 12px;

    @media @mobile {
      margin-left: 8px;
    }
  }

  &__suggestions {
    position: absolute;
    top: calc(100% + 22px);
    left: 0;
    right: 0;
    z-index: 1001;

    @media @desktop {
      top: calc(100% + 22px);
    }

    // When search is focused, ensure suggestions are visible
    .searchFocused & {
      z-index: 1001;
    }

    &--products {
      padding: 8px;
      background-color: @DialogBackground;
      border: 1px solid @DividerStokes;
      border-radius: 4px;
      box-shadow:
        0px 4px 4px 0px rgba(78, 63, 9, 0.08),
        0px 8px 24px -4px rgba(78, 63, 9, 0.08);
      width: 100%;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      @media @desktop {
        margin-left: auto;
        margin-right: auto;
      }

      @media @tablet {
        border: none;
        box-shadow: none;
        padding: 16px;
      }

      >ul {
        overflow-y: auto;
      }
    }

    &--item {
      cursor: pointer;
      font-family: "Helvetica Medium";


      &:not(:last-child) {
        margin-bottom: 12px;

        @media @desktop {
          margin-bottom: 8px;
        }
      }

      &.no-result {
        height: 20px;
      }

      .productThumb {
        flex: 0 0 56px;
        border-radius: 4px;
        margin-right: 12px;
      }

      .productTitle {
        color: @TextHeading;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: -0.32px;

        @media @tablet {
          font-size: 14px;
          line-height: 18px;
          letter-spacing: -0.28px;
        }

        @media @desktop {
          .text-line-clamp();
        }
      }
    }

    &--button {
      text-align: center;
      margin-top: 12px;

      @media @desktop {
        margin-top: 8px;
      }
    }

    &--title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      font-family: "Helvetica Medium";
      font-weight: 600;
    }

    .clearAllButton {
      font-family: "Helvetica Medium";
      color: #666;
      padding: 4px 8px;
      background: none;
      border: none;
      cursor: pointer;
      min-width: 60px;
      text-align: right;

      &:hover {
        color: #000;
        text-decoration: underline;
      }

      &:focus {
        outline: none;
        color: #000;
      }
    }

    .searchItemContent {
      display: flex;
      align-items: center;
      flex: 1;
      cursor: pointer;
      padding: 8px 0;
      font-family: "Helvetica Medium";
    }

    .deleteSearchButton {
      background: none;
      border: none;
      padding: 8px;
      margin-left: 8px;
      cursor: pointer;
      opacity: 0.5;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      min-height: 32px;

      &:hover {
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 50%;
      }

      &:focus {
        outline: none;
        opacity: 1;
      }
    }

    .deleteIcon {
      width: 16px;
      height: 16px;
    }
  }
}

.double-row-search .search__suggestions {
  @media @desktop {
    top: calc(100% + 6px);
  }
}

.searchIcon g>path {
  fill: @HeaderIcon;
}

.single-row-search {
  .search {
    padding: 0;

    &--closeIcon {
      margin-left: 16px;

      @media @desktop {
        width: 32px !important;
        height: 32px !important;
        margin-left: 32px;
      }
    }

    &__wrapper {
      padding: 8px;

      @media @mobile-up {
        padding: 8px 24px;
      }

      @media @desktop {
        padding: 11px 0 0;
        width: 884px;
      }
    }

    &__input {
      padding: 4px 16px;
      height: 42px;
      border: 1px solid @TextLabel;
      border-radius: 4px;
      background: @ThemeAccentL5;
      font-family: "Helvetica Medium";

      @media @desktop {
        padding: 6px 16px;
        height: 48px;
      }

      &--text {
        background: transparent;
        border: none;
        padding: 0;
        // height: calc(100% - 19px);
        border-radius: unset;
        position: absolute;
        bottom: 4px;
        left: 16px;
        z-index: 2;
      }

      &--label {
        position: absolute;
        top: 50%;
        left: 16px;
        transform: translateY(-50%);
        color: @TextDisabled;
        transition: 300ms all cubic-bezier(0, 0, 0.2, 1);

        &.active {
          font-size: 10px;
          font-weight: 300;
          line-height: 12px;
          letter-spacing: 0em;
          top: 4px;
          transform: unset;
          color: @TextBody;
        }
      }

      &--search-icon {
        left: unset;
        right: 10px;
        fill: @TextHeading;
        z-index: 3;
      }
    }

    &__suggestions {
      &--products {
        padding: 20px 8px;
        margin: 0;

        @media @desktop {
          width: calc(100% - 64px);
        }
      }

      &--item {
        padding: 0 16px;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .product-title {
          color: @TextLabel;
        }
      }

      &--title {
        margin: 0 16px 24px;
        padding-bottom: 3px;
        border-bottom: 1px solid @DividerStokes;
      }

      &--button {
        margin: 24px 16px 0;
        padding: 12px 0 0;
        border-top: 1px solid @DividerStokes;

        .btnLink {
          display: flex;
          align-items: center;
          justify-content: space-between;
          text-decoration: none;
          color: @TextHeading;
          width: 100%;

          .showMoreIcon {
            width: 15px;
            height: 15px;
            transform: rotate(180deg);
            fill: @TextLabel;
          }
        }
      }
    }
  }
}

.noResult {
  color: black;
}


.searchflex {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50px;
  padding: 12px 16px;
  // width: 100%;
  width: 100%;
  gap: 8px;
  margin: 0 auto;

  @media @desktop-xl {
    min-width: 300px;
    width: 100%;
  }

  // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  // transition: all 0.3s ease;
}

.searchflex:focus-within {
  // box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.search__input--text {
  flex: 1;
  border: none;
  background: transparent;
  text-overflow: ellipsis;
  // padding: 12px 16px;
  font-size: 14px;
  outline: none;
  width: 100%;
  color: #333;
  font-family: 'Helvetica Medium' !important;
  transition: all 0.3s ease;

  &:focus {
    color: #000;
  }
}

.search__input--text::placeholder {
  color: #999;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.search__input--text:focus::placeholder {
  opacity: 0.6;
}

.search__input--removeSpace {
  padding-right: 0;
}

.headerIcon {
  cursor: pointer;
  color: #555;
  transition: color 0.2s ease;
}

.headerIcon:hover {
  color: #222;
}

.search--closeIcon {
  margin-left: 8px;
  width: 18px;
  height: 18px;
}

/* Icon styles */
.searchflex svg {
  min-width: 20px;
  height: 20px;
  color: #555;
}

/* Focus & active states */
.searchflex:focus-within svg {
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .searchflex {
    max-width: 100%;
    padding: 6px 12px;
  }

  .search__input--text {
    font-size: 14px;
  }
}

.searchText {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: "Helvetica Medium";
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  .totalCount {
    color: #666;
    font-size: 0.9em;
    font-weight: normal;
  }
}

.recentSearchesHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 8px;
}

.clearAllButton {
  font-family: "Helvetica Medium";
  background: none;
  border: none;
  color: #666;
  font-size: 0.9em;
  cursor: pointer;
  padding: 4px 8px;

  &:hover {
    color: #333;
    text-decoration: underline;
  }
}

.deleteSearchButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  margin-left: auto;

  &:hover {
    opacity: 1;
  }

  .deleteIcon {
    width: 16px;
    height: 16px;
  }
}

.search__suggestions--item {
  position: relative;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background-color: #f5f5f5;
  }
}

.text {
  font-family: "Helvetica Medium";
  font-size: 16px;
}

/* Add these styles to your existing search-bar.less file */

/* Mobile Search Modal Styles */
/* Add these styles to your existing search-bar.less file - Updated Mobile Search Modal Styles */

/* Mobile Search Modal with smooth bottom slide animation */

.mobileSearchModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  
  /* Fade in animation for overlay */
  animation: fadeInOverlay 0.3s ease-out;
  
  @media @desktop {
    display: none;
  }
}

.mobileSearchOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.mobileSearchContent {
  position: relative;
  width: 100%;
  background-color: white;
  border-radius: 20px 20px 0 0;
  height: 90vh;
  display: flex;
  flex-direction: column;
  
  /* Slide up animation from bottom */
  animation: slideUpFromBottom 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: bottom;
}

.mobileSearchHeader {
  padding: 20px 16px 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: white;
  border-radius: 20px 20px 0 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.mobileSearchInputWrapper {
  width: 100%;
}

.mobileSearchFlex {
  background-color: #f5f5f5;
  border-radius: 25px;
  padding: 12px 16px;
  width: 100%;
  gap: 12px;
  margin: 0;
  
}

.mobileSearchBody {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  
  .search__suggestions--products {
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    > ul {
      overflow-y: auto;
    }
  }
  
  /* Match desktop title styling exactly */
  .search__suggestions--title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    font-family: "Helvetica Medium";
    font-weight: 600;
    margin: 0;
    
    .text {
      font-family: "Helvetica Medium";
      font-size: 16px;
    }
  }
  
  /* Match desktop item styling exactly */
  .search__suggestions--item {
    cursor: pointer;
    font-family: "Helvetica Medium";
    margin-bottom: 12px;
    border-bottom: none;
    padding: 0;
    
    @media @desktop {
      margin-bottom: 8px;
    }

    &:not(:last-child) {
      margin-bottom: 12px;
    }

    &.no-result {
      height: 20px;
    }

    &:last-child {
      margin-bottom: 0;
    }
    
  }
  
  /* Match desktop searchItemContent styling */
  .searchItemContent {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
    padding: 8px 0;
    font-family: "Helvetica Medium";
    gap: 8px;
    
    /* Smooth hover transition */
    transition: background-color 0.2s ease;
    border-radius: 8px;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
  
  /* Match desktop searchText styling */
  .searchText {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: "Helvetica Medium";
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 16px;

    .totalCount {
      color: #666;
      font-size: 0.9em;
      font-weight: normal;
    }
  }
  
  /* Match desktop icon styling */
  .recentSearchIcon,
  .searchIcon {
    width: 20px;
    height: 20px;
    min-width: 20px;
    opacity: 1;
    color: #555;
    transition: color 0.2s ease;
  }
  
  /* Match desktop delete button styling */
  .deleteSearchButton {
    background: none;
    border: none;
    padding: 8px;
    margin-left: 8px;
    cursor: pointer;
    opacity: 0.5;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    min-height: 32px;
    border-radius: 50%;
    transition: all 0.2s ease;
    
    &:hover {
      opacity: 1;
      background-color: rgba(0, 0, 0, 0.05);
      transform: scale(1.1);
    }
    
    .deleteIcon {
      width: 16px;
      height: 16px;
    }
  }
  
  /* Match desktop clearAllButton styling */
  .clearAllButton {
    font-family: "Helvetica Medium";
    color: #666;
    padding: 4px 8px;
    background: none;
    border: none;
    cursor: pointer;
    min-width: 60px;
    text-align: right;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 4px;
    
    &:hover {
      color: #000;
      text-decoration: underline;
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    .text {
      font-family: "Helvetica Medium";
      font-size: 16px;
    }
  }
  
  /* Match desktop button section styling */
  .search__suggestions--button {
    text-align: center;
    margin-top: 12px;

    @media @desktop {
      margin-top: 8px;
    }
    
    .btnLink {
      font-family: "Helvetica Medium";
      transition: all 0.2s ease;
      border-radius: 8px;
      padding: 8px 16px;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
        transform: translateY(-1px);
      }
      
      span p {
        font-family: "Helvetica Medium";
        font-size: 16px;
      }
    }
  }
  
  /* Match desktop no result styling */
  .noResult {
    color: black;
    font-family: "Helvetica Medium";
  }
}

/* Animation Keyframes */
@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Mobile specific input styles - keep existing */
.search__input--mobile {
  cursor: pointer;
  caret-color: transparent;
  
  &:focus {
    outline: none;
  }
}

.search__input--mobile-active {
  cursor: text;
  caret-color: auto;
  
  &:focus {
    outline: none;
  }
}

/* Ensure proper mobile layout */
@media (max-width: 480px) {
  .mobileSearchContent {
    max-height: 95vh;
  
  }
  
  .mobileSearchHeader {
    padding: 16px 12px 8px;
  }
  
  .mobileSearchFlex {
    padding: 10px 14px;
  }
}

/* Hide mobile modal on desktop */
@media @desktop {
  .mobileSearchModal {
    display: none !important;
  }
}


@keyframes slideDownToBottom {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}