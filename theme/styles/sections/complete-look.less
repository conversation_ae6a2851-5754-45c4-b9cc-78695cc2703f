@import "../../styles/main.less";

.wrapper {
  padding: 32px;
  background-color: @PageBackground;

  @media @tablet {
    padding: 16px;
  }
}

.heading {
  color: @TextHeading;
  text-align: center;
  margin-bottom: 32px;
}

.container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;

  @media @tablet {
    flex-direction: column;
    gap: 16px;
  }
}

.leftPanel {
  flex: 1 1 35%;
  position: relative;

  @media @tablet {
    flex: 1 1 100%;
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.tagButton {
  position: absolute;
  transform: translate(-50%, -50%);
  padding: 6px 14px;
  border-radius: 20px;
  background: @PageBackground;
  color: @TextHeading;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s;

  &.activeTag,
  &:hover {
    background: @ButtonPrimary;
    color: @ButtonSecondary;
  }
}

.rightPanel {
  flex: 1 1 60%;
  @media @tablet {
    flex: 1 1 100%;
  }
}

.sliderItem {
  padding: 0 8px;
} 