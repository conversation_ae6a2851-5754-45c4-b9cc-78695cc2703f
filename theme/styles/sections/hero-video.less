@import "../main.less";

.video_heading {
  text-align: center;
  margin-top: 16px;
  margin-bottom: 16px;
  word-wrap: break-word;
}
.error_message {
  color: red;
  align-items: center;
  justify-content: center;
}

.video_container {
  background-color: @LightGray;
  position: relative;
  video {
    @media @tablet {
      &::-webkit-media-controls {
        display: none !important; /* Hides controls */
      }

      &::-moz-media-controls {
        display: none !important; /* For Firefox */
      }
    }
  }

  &:hover {
    @media @desktop {
      .pauseButton {
        display: block;
      }
    }
  }
}

.youtube_wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; // 16:9 aspect ratio
}

.yt_video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// ... (rest of the CSS remains the same)
.center-overlay {
  text-align: center;
  inset: 50% auto auto 50%;
  transform: translate(-50%, -50%);
}
.overlay-noimage {
  margin: 50px 0;
  &.youtube-noimage {
    display: none;
  }
}

.overlay {
  &__image,
  &__color {
    position: absolute;
    inset: 0;
  }
  &__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &__playButton {
    // #play {
    width: 80px;
    height: 80px;
    opacity: 0.5;
    cursor: pointer;
    //   }
  }
  .btn {
    padding: 15px;
    border-radius: 5px;
    border: none;
    font-weight: 800;
    text-transform: uppercase;
  }
}
.pauseButton {
  width: 80px;
  height: 80px;
  opacity: 0.5;
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  cursor: pointer;
  display: none;
}
.deafult-backround {
  background: @Gray;
}
