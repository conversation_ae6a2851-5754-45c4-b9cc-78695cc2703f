@import "../main.less";

.collections__template {
  max-width: 100vw;
  padding: 30px 30px 24px 30px;
  background-color: #00000000;

  @media @mobile {
    padding: 0;
  }

  .section-title-block {
    text-align: center;
    padding-inline: @SectionInlinePaddingMobile;

    @media @mobile-up {
      padding-inline: @SectionInlinePaddingTablet;
    }

    @media @desktop {
      padding-inline: @SectionInlinePaddingDesktop;
    }

    .section-title {
      color: @TextHeading;
      margin-bottom: 8px;

      @media @mobile-up {
        margin-bottom: 8px;
      }

      @media @desktop {
        margin-bottom: 16px;
      }
    }

    .section-description {
      color: @TextBody;
      margin: 0 auto 32px;

      // font-size: 14px;
      @media @desktop {
        max-width: 590px;
      }

      @media @tablet {
        font-size: 12px;
        margin: 0 auto 24px;
      }
    }
  }
}

// :global{
//   .slick-track{
//     transform: translate3d(0,0,0) !important;
//   }
// }

.collectionGrid {
  padding-inline: @SectionInlinePaddingMobile;
  display: grid;
  justify-content: center;
  gap: 16px;
  grid-template-columns: repeat(1, 1fr);

  @media @mobile-up {
    padding-inline: @SectionInlinePaddingTablet;
    gap: 16px;
    grid-template-columns: repeat(4, 1fr);
  }

  @media @desktop {
    padding-inline: @SectionInlinePaddingDesktop;
    gap: 74px;
    grid-template-columns: repeat(7, 1fr);

    // gap: 24px;
    // grid-template-columns: repeat(
    //   auto-fit,
    //   calc((100% - (24px * (var(--grid-columns) - 1))) / var(--grid-columns))
    // );
  }
}

.defaultGrid {
  @media @desktop {
    --grid-columns: 7;
  }
}

.collectionSlider {
  --gap: 74px;
  padding-inline: @SectionInlinePaddingDesktop;

  @media @tablet {
    --gap: 16px;
    padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    padding-inline: @SectionInlinePaddingMobile;
  }

  :global {
    .slick-list {
      margin: 0 calc(var(--gap) / 2 * -1);
    }
  }

  @media (min-width: 1920px) {
    .sliderItem {
      padding-inline: calc(var(--gap) / 2);
    }
  }

  &.singleItem {
    @media @mobile {
      padding-inline: @SectionInlinePaddingMobile;
    }
  }
}

.hideOnDesktop {
  @media @desktop {
    display: none;
  }
}

.hideOnTablet {
  @media @tablet {
    display: none;
  }
}

.hideOnMobile {
  @media @mobile {
    display: none;
  }
}

.showOnMobile {
  @media @mobile-up {
    display: none;
  }
}

@media (min-width: 769px) and (max-width: 1023px) {
  .collectionSlider {
    --gap: 16px;
    padding-inline: @SectionInlinePaddingTablet;

    .sliderItem {
      padding-inline: calc(var(--gap) / 2);
    }
  }
}

.scrollContainer {
  display: flex;
  overflow-x: auto;
  padding: 16px;
  gap: 74px;
  scroll-snap-type: x mandatory;

  @media @mobile {
    gap: 16px;
  }

  @media @tablet {
    gap: 20px;
  }
}

.collectionItem {
  flex: 0 0 auto;
  width: 200px;
  scroll-snap-align: start;
  text-align: center;

  @media @mobile {
    width: 71px !important;
  }

  @media @tablet {
    width: 91px;
  }
}

.collectionImage {
  width: 100%;
  // height: 200px;
  aspect-ratio: 13/20;
  object-fit: cover;
  border-radius: 30px;

  @media @mobile {
    border-radius: 20px;
    width: 71px;
    height: auto;
  }

  @media @tablet-strict {
    height: auto;
    border-radius: 20px;
    width: 91px;
  }
}

.collectionName {
  margin-top: 32px;
  font-size: 20px;
  font-family: "Helvetica Medium";
  color: #333;

  @media @mobile {
    margin-top: 12px;
    font-size: 12px;
    line-height: 130%;
  }

  @media @tablet {
    margin-top: 16px;
    font-size: 14px;
  }
}
