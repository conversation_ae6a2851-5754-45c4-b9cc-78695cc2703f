@import "../main.less";
@lg-min: 1024px;

.testimonialTitle {
  text-align: center;
  margin-bottom: 32px;
  padding-inline: @SectionInlinePaddingDesktop;

  @media @tablet {
    margin-bottom: 24px;
    padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    padding-inline: @SectionInlinePaddingMobile;
  }
}

.testimonialSlider {
  --gap: 24px;
  padding-left: @SectionInlinePaddingDesktop;
  @media @tablet {
    --gap: 12px;
    padding-left: @SectionInlinePaddingTablet;
  }
  @media @mobile {
    padding-left: @SectionInlinePaddingMobile;
  }

  :global {
    .slick-list {
      margin: 0 calc(var(--gap) / 2 * -1);
      padding-left: 0 !important;
    }
    .slick-track {
      display: flex;
    }
    .slick-slide {
      height: auto;
      & > * {
        height: 100%;
      }
    }
  }

  .sliderItem {
    padding-inline: calc(var(--gap) / 2);
    height: 100%;
  }

  &.twoItem {
    @media @desktop {
      padding-right: @SectionInlinePaddingDesktop;
    }
    @media @tablet-strict {
      padding-right: @SectionInlinePaddingTablet;
    }
  }

  &.oneItem {
    @media @mobile {
      padding-right: @SectionInlinePaddingMobile;
    }
  }
}

.testimonial {
  display: flex;
  border-radius: @ImageRadius;
  border: 1px solid @DividerStokes;
  height: 100%;

  @media @tablet {
    flex-direction: column;
    justify-content: flex-start;
  }

  @media @desktop {
    align-items: flex-start;
    padding: 16px;
    column-gap: 16px;
  }

  .testimonialImage {
    width: 100%;
    border-radius:~"calc(@{ImageRadius} / 2)";

    @media @desktop {
      flex: 0 0 232px;
    }
  }

  .testimonialInfo {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    @media @tablet {
      padding: 16px;
    }

    @media @desktop {
      flex: 1 0 0;
      min-width: 0;
    }

    @media @tablet {
      &.has-image {
        flex-grow: 1;
      }
    }

    .testimonialText {
      color: @TextBody;
      font-style: italic;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 16px;
      .overflow-ellipsis();

      @media @desktop {
        margin-bottom: 24px;
      }
    }

    .testimonialAuthorInfo {
      .authorName {
        margin-bottom: 4px;
        .overflow-ellipsis();
      }

      .authorDescription {
        color: @TextBody;
        .overflow-ellipsis();
      }
    }
  }
}

@media @desktop {
  .hideOnDesktop {
    display: none;
  }
}
@media @tablet {
  .hideOnDesktop {
    display: none;
  }
}
@media @mobile {
  .hideOnMobile {
    display: none;
  }
  .hideOnDesktop {
    display: block;
  }
}
