@import "../main.less";


.mainContainer {
  padding: 40px 0 40px 40px;
  overflow: hidden;
  background-color: white;

  @media @mobile {
    padding: 20px 0 20px 20px;
    overflow: hidden;
  }
}

.darkModeMainContainer {
  padding: 40px 0 40px 40px;
  overflow: hidden;
  background-color: #1A1A1A;

  @media @mobile {
    overflow: hidden;
    padding: 20px 0 20px 20px;
  }
}

// .dynamicStyles {
//   padding: 80px 32px;
//   max-width: 100vw;
//   border-radius: 20px;
//   background-color: #E2E2E2;
//   @media @mobile {
//     padding: 20px;
//   }
// }

.titleBlock {
  text-align: left;
  // padding-inline: @SectionInlinePaddingDesktop;
  display: flex;
  // flex-direction: column;
  justify-content: space-between;
  align-items: center;
  // gap: 16px;
  margin-bottom: 60px;
  padding-right: 40px;

  @media @tablet {
    // padding-inline: @SectionInlinePaddingTablet;
    gap: 8px;
    margin-bottom: 24px;
  }

  @media @mobile {
    // padding-inline: @SectionInlinePaddingMobile;
    padding: 0;
    margin-bottom: 20px;
  }
}

.sectionHeading {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  line-height: 115%;
  color: black;

  @media @mobile {
    font-size: 22px;
    font-family: "Helvetica Bold" !important;
  }
}

.darkModeSectionHeading {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  line-height: 115%;
  color: white;

  @media @mobile {
    font-size: 22px;
    font-family: "Helvetica Bold" !important;
  }
}

.description {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  line-height: 115%;
  color: black;

  @media @mobile {
    font-size: 22px;
  }
}

.darkModeDescription {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  line-height: 115%;
  color: white;

  @media @mobile {
    font-size: 22px;
  }
}

.imageGallery {
  border-radius: 20px;

  @media screen and (min-width: 1920px) {
    width: 418px;
    height: 538px;
  }

  @media screen and (min-width: 1430px) and (max-width: 1900px) {
    width: 300px;
    height: 380px;
  }


  @media @tablet {
    height: 200px;
    width: 150px;
  }

  @media @mobile {
    border-radius: 10px;
    width: 200px;
    height: 250px;
  }


  -webkit-mask-image: -webkit-radial-gradient(white, black); //safari fix
}

.imageGrid {
  --row-gap: 32px;
  --col-gap: 24px;
  --col-count: var(--item-count);
  // padding-inline: @SectionInlinePaddingDesktop;
  display: grid;
  gap: var(--row-gap) var(--col-gap);
  grid-template-columns: repeat(auto-fit,
      calc((100% / var(--col-count)) - (var(--col-gap) * (var(--col-count) - 1) / var(--col-count))));

  @media @tablet {
    --row-gap: 24px;
    --col-gap: 16px;
    // padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    --row-gap: 14px;
    --col-gap: 14px;
    --col-count: var(--item-count-mobile);
    // padding-inline: @SectionInlinePaddingMobile;
  }
}

.imageSlider {
  --gap: 24px;
  position: relative;

  //   padding-inline: @SectionInlinePaddingDesktop;
  @media @tablet {
    --gap: 12px;
    // padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    padding: 0;
  }

  :global {
    .slick-list {
      padding: 0 !important;
      margin: 0 calc(var(--gap) / 2 * -1);
    }
  }

  .sliderItem {
    padding: 0 calc(var(--gap) / 2);
  }

  // .firstItem {
  //   padding-left: 40px;
  // }


  &.mobileItemLess {
    @media @mobile {
      padding-inline: @SectionInlinePaddingMobile;
    }
  }

  .customNextBtn,
  .customPrevBtn {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 45%;
    // transform: translateY(-50%);
    // transform: translate(-90px,-50%);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: @PageBackground;
    // border: 1px solid @TextHeading;
    border-radius: 50%;
    transform: translateY(-10%);

    @media @tablet {
      display: none;
    }

    svg>path {
      stroke: @TextHeading;
    }
  }

  .customNextBtn {
    // right: min(5vw, 72px);
    right: 7px;
    // transform: translateY(106%);
  }

  .customPrevBtn {
    left: -25px;
    transform: rotate(180deg);
  }

}

.section-button {
  // margin-top: 1rem;
  display: flex;
  align-items: center;
  // justify-content: center;
  justify-content: flex-end;
  gap: 10px;
  // width: 100%;
  padding: 12px 30px;
  font-size: 16px;
  background-color: white;
  // text-align: center;
  font-family: "Helvetica Bold" !important;
  border-radius: 999px;
  border: 1.5px solid #1A1A1A;
  color: #1A1A1A;

  svg>path {
    stroke: #1A1A1A;
  }

  @media @mobile {
    padding: 8px 12px;
    margin: 0;
  }
}

.darkModeSectionButton {
  // margin-top: 1rem;
  display: flex;
  align-items: center;
  // justify-content: center;
  justify-content: flex-end;
  gap: 10px;
  // width: 100%;
  padding: 12px 30px;
  font-size: 16px;
  background-color: #1A1A1A;
  // text-align: center;
  font-family: "Helvetica Bold" !important;
  border-radius: 999px;
  border: 1.5px solid white;
  color: white;

  svg>path {
    stroke: white;
  }

  @media @mobile {
    padding: 8px 12px;
    margin: 0;
  }
}

.hideOnDesktop {
  @media @desktop {
    display: none;
  }
}

.hideOnTablet {
  @media @tablet {
    display: none;
  }
}

.hideOnMobile {
  @media @mobile {
    display: none;
  }
}

.showOnMobile {
  @media @mobile-up {
    display: none;
  }
}