@import "../main.less";

:global {
  .swiper-3d .swiper-slide {
    transform-origin: bottom center !important;
    bottom: 0;
  }
  .swiper {
    width: 510px;
    height: 686px;

    @media screen and (max-width: 1920px) and (min-width: 1024px) {
      width: 350px;
      height: 470px;
    }

    @media screen and (max-width: 1024px) and (min-width: 769px) {
      width: 250px;
      height: 350px;
    }

    @media screen and (max-width: 768px) and (min-width: 481px) {
      width: 200px;
      height: 260px;
    }

    @media @mobile {
      width: 150px;
      height: 200px;
    }
  }

  .swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 18px;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
  }

  // .swiper-slide:nth-child(1n) {
  //     background-color: rgb(206, 17, 17);
  // }

  // .swiper-slide:nth-child(2n) {
  //     background-color: rgb(0, 140, 255);
  // }

  // .swiper-slide:nth-child(3n) {
  //     background-color: rgb(10, 184, 111);
  // }

  // .swiper-slide:nth-child(4n) {
  //     background-color: rgb(211, 122, 7);
  // }

  // .swiper-slide:nth-child(5n) {
  //     background-color: rgb(118, 163, 12);
  // }

  // .swiper-slide:nth-child(6n) {
  //     background-color: rgb(180, 10, 47);
  // }

  // .swiper-slide:nth-child(7n) {
  //     background-color: rgb(35, 99, 19);
  // }

  // .swiper-slide:nth-child(8n) {
  //     background-color: rgb(0, 68, 255);
  // }

  // .swiper-slide:nth-child(9n) {
  //     background-color: rgb(218, 12, 218);
  // }

  // .swiper-slide:nth-child(10n) {
  //     background-color: rgb(54, 94, 77);
  // }
}

.heading {
  text-align: center;
  -webkit-text-stroke-width: 3.46px;
  -webkit-text-stroke-color: #ff1e00;
  font-family: "Neue Haas Grotesk Display Pro";
  font-size: 174.402px;
  line-height: 100%;
  /* 261.603px */
  text-transform: uppercase;
  letter-spacing: 5.232px;
  text-shadow: 5px 5px 10px #ff1e00;

  @media screen and (max-width: 1900px) and (min-width: 1024px) {
    font-size: 140px;
    line-height: 100%;
  }

  @media screen and (max-width: 1024px) and (min-width: 481px) {
    font-size: 80px;
    line-height: 100%;
  }

  @media @mobile {
    font-size: 83px;
    letter-spacing: 0;
    -webkit-text-stroke-width: 1.64px;
    line-height: 100%;
  }
}

.sub_heading {
  text-align: center;
  -webkit-text-stroke-width: 3.17px;
  -webkit-text-stroke-color: #ff1e00;
  font-family: "Neue Haas Grotesk Display Pro";
  font-size: 112.758px;
  line-height: 100%;
  text-transform: uppercase;
  /* 169.137px */
  letter-spacing: 3.383px;
  text-shadow: 5px 5px 10px #ff1e00;

  @media screen and (max-width: 1900px) and (min-width: 1024px) {
    font-size: 80px;
    line-height: 100%;
  }

  @media screen and (max-width: 1024px) and (min-width: 481px) {
    font-size: 60px;
    line-height: 100%;
  }

  @media @mobile {
    font-size: 54px;
    letter-spacing: 0;
    -webkit-text-stroke-width: 1.51px;
    line-height: 100%;
  }
}

// .swiper-slide {
//     transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
//     transform: scale(0.95);
//     opacity: 0.8;
//   }

//   .swiper-slide-active {
//     transform: scale(1);
//     opacity: 1;
//     z-index: 10;
//   }

:global {
  .swiper-button-prev,
  .swiper-button-next {
    width: 58px;
    height: 58px;
    background-color: white; // optional: background color
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); // optional
    z-index: 10;

    &::after {
      font-size: 36px;
      color: #000;
    }
  }

  .swiper-button-prev {
    left: -30px;
  }

  .swiper-button-next {
    right: -30px;
  }
}
