@import "../main.less";


.mainContainer {
  padding: 40px;
  background-color: white;

  @media @mobile {
    padding: 20px;
  }
}

.dynamicStyles {
  padding: 80px 32px;
  max-width: 100vw;
  border-radius: 20px;
  background-color: #E2E2E2;

  @media @mobile {
    padding: 20px;
  }
}

.titleBlock {
  text-align: left;
  // padding-inline: @SectionInlinePaddingDesktop;
  display: flex;
  // flex-direction: column;
  justify-content: space-between;
  align-items: center;
  // gap: 16px;
  margin-bottom: 32px;

  @media @tablet {
    // padding-inline: @SectionInlinePaddingTablet;
    gap: 8px;
    margin-bottom: 24px;
  }

  @media @mobile {
    // padding-inline: @SectionInlinePaddingMobile;
    padding: 0;
    margin-bottom: 60px;
  }
}

.sectionHeading {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  line-height: 115%;
  color: black;

  @media @mobile {
    font-size: 22px;
    font-family: "Helvetica Bold" !important;
  }
}

.description {
  font-size: 48px;
  font-family: "Helvetica Bold" !important;
  line-height: 115%;

  @media @mobile {
    font-size: 22px;
  }
}

.imageWrapper {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.bottomStrip {
  border-bottom: 5px solid #000;
  border-bottom-left-radius: 5px;
  width: calc(100% - 10px);
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: flex-end;

  @media @mobile {
    width: calc(100% - 2px);
    border-bottom-left-radius: 10px;
  }
}

.priceTag {
  position: relative;
  background-color: #000;
  color: #fff;
  padding: 0 25px;
  border-top-left-radius: 20px;
  height: 50px;
  width: max-content;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 2;
  overflow: hidden;

  @media @mobile {
    border-top-left-radius: 10px;
    height: 30px;
    padding: 0 15px;
  }
}

.bottomStrip::before {
  width: 35px;
  height: 35px;
  content: "";
  position: absolute;
  bottom: 0;
  transform: rotate(270deg);
  right: 124px;
  background-color: black;

  clip-path: path("M1 0C1 19.3 15.3 35 35 35H0V0H1Z");

  @media @mobile {
    width: 20px;
    height: 20px;
    content: "";
    position: absolute;
    bottom: 0;
    transform: rotate(270deg);
    right: 65px;
    background-color: black;
    clip-path: path("M1 0C1 11 8.75 20 20 20H0V0H1Z");

  }
}

.priceTextTop {
  font-size: 12px;
  font-family: "Helvetica Bold" !important;
  letter-spacing: 5.64px;
  text-transform: uppercase;
  line-height: 130%;
  color: #fff;
  text-align: center;

  @media @mobile {
    font-size: 5px;
    letter-spacing: 2.376px;
  }
}

.priceTextBottom {
  font-size: 24px;
  font-family: "Helvetica Bold" !important;
  line-height: 130%;
  color: #fff;

  @media @mobile {
    font-size: 14px;
  }
}


.imageGallery {
  position: relative;
  border-radius: 20px;

  @media screen and (min-width: 1920px) {
    width: 410px;
    height: 578px;
  }

  @media screen and (min-width: 1430px) and (max-width: 1900px) {
    width: 300px;
    height: 400px;
  }

  @media @tablet {
    width: 150px;
  }

  @media @mobile {
    border-radius: 10px;
    width: 142px;
    height: 200px;
  }

  -webkit-mask-image: -webkit-radial-gradient(white, black); //safari fix
}

.imageGrid {
  --row-gap: 32px;
  --col-gap: 24px;
  --col-count: var(--item-count);
  // padding-inline: @SectionInlinePaddingDesktop;
  display: grid;
  gap: var(--row-gap) var(--col-gap);
  grid-template-columns: repeat(auto-fit,
      calc((100% / var(--col-count)) - (var(--col-gap) * (var(--col-count) - 1) / var(--col-count))));

  @media @tablet {
    --row-gap: 24px;
    --col-gap: 16px;
    // padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    --row-gap: 14px;
    --col-gap: 14px;
    --col-count: var(--item-count-mobile);
    // padding-inline: @SectionInlinePaddingMobile;
  }
}

.imageSlider {
  --gap: 24px;
  // padding-inline: @SectionInlinePaddingDesktop;

  @media @tablet {
    --gap: 12px;
    // padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    padding: 0;
  }

  :global {
    .slick-list {
      margin: 0 calc(var(--gap) / 2 * -1);
    }
  }

  .sliderItem {
    padding: 0;
  }

  &.mobileItemLess {
    @media @mobile {
      padding-inline: @SectionInlinePaddingMobile;
    }
  }

}

.section-button {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  // justify-content: center;
  justify-content: flex-end;
  gap: 10px;
  // width: 100%;
  padding: 12px 30px;
  font-size: 16px;
  background-color: #E6E6E6;
  // text-align: center;
  font-family: "Helvetica Bold" !important;
  border-radius: 999px;
  border: 1.5px solid #1A1A1A;

  svg>path {
    stroke: #1A1A1A;
  }

  @media @mobile {
    padding: 8px 12px;
  }
}

.hideOnDesktop {
  @media @desktop {
    display: none;
  }
}

.hideOnTablet {
  @media @tablet {
    display: none;
  }
}

.hideOnMobile {
  @media @mobile {
    display: none;
  }
}

.showOnMobile {
  @media @mobile-up {
    display: none;
  }
}