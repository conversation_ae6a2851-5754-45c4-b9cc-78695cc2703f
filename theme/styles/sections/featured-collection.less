@import "../main.less";

.addToCartContainer {
  @media @desktop {
    max-width: unset !important;

    .addToCartBody {
      overflow-y: hidden;
    }
  }
}

.sectionWrapper {
  min-height: 400px;
  padding: 58px 0 32px 58px;
  background: linear-gradient(0deg, #593630 -45.88%, #C5A175 98.11%);

  @media @tablet {
    min-height: 370px;
  }

  @media @tablet-strict {
    padding: 28px 30px 0 30px;
  }

  @media @mobile {
    min-height: 420px;
    padding: 28px 6px 0 6px;
  }

  .visibleOnMobile {
    @media @desktop {
      display: none;
    }
  }
}

.titleBlock {
  padding-inline: @SectionInlinePaddingDesktop;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // flex-direction: column;

  @media @tablet {
    padding-inline: @SectionInlinePaddingTablet;
    padding-bottom: 8px;
  }

  @media @mobile {
    padding-inline: @SectionInlinePaddingMobile;
  }

  // &.bannerTitleBlock {
  //   // display: flex;
  //   // flex-direction: column;
  //   padding-inline-start: 0;

  //   p {
  //     margin: 0 0 22px 0;
  //   }
  // }






  // .sectionHeading {
  //   margin-bottom: 16px;

  //   @media @tablet {
  //     margin-bottom: 8px;
  //   }
  // }

  // .description {
  //   max-width: 590px;
  //   margin-bottom: 16px;

  //   @media @tablet {
  //     margin-bottom: 16px;
  //   }
  // }
}

.imageGrid {
  padding-inline: @SectionInlinePaddingDesktop;
  display: grid;
  gap: 32px 24px;
  grid-template-columns: repeat(var(--per_row), 1fr);

  &.singleItem {
    display: flex;
    justify-content: center;

    &>* {
      flex: 0 1 calc(100% / var(--per_row));

      @media @mobile {
        flex: 1;
      }
    }
  }

  @media @tablet {
    padding-inline: @SectionInlinePaddingTablet;
    gap: 24px 16px;
    grid-template-columns: repeat(var(--brand-item), 1fr);
  }

  @media @mobile {
    padding-inline: @SectionInlinePaddingMobile;
    gap: 12px;
    grid-template-columns: repeat(2, 1fr);
  }
}

.imageGallery {
  border-radius: @ImageRadius;
  
  -webkit-mask-image: -webkit-radial-gradient(white, black); //safari fix
}

.pos-relative {
  position: relative;
}

.gap-above-button {
  margin-top: 1rem;
  text-align: center;

  @media @tablet-strict {
    margin-top: 16px;
  }

  @media @desktop {
    margin-top: 32px;
  }

  &.visibleOnDesktop {
    @media @tablet {
      display: none;
    }

    @media @desktop {
      margin: 0 40px 82px 0;
      display: flex;
      justify-content: flex-end;
      // button {
      //   border: none !important;
      //   text-decoration: underline;
      //   padding: 0;
      //   background-color: @PageBackground;
      //   &:hover {
      //     background-color: @PageBackground;
      //     color: @ButtonPrimary;
      //   }
      // }
    }
  }
}

.gap-above-button-horizontal {
  margin-top: 1rem;
  text-align: center;

  @media @tablet-strict {
    margin-top: 16px;
  }

  @media @desktop {
    margin-top: 64px;

    &.lessGap {
      margin-top: 24px;
    }
  }
}

.featuredCollectionSlider {
  :global {
    .slick-track {
      
      @media @mobile {
        transform: none !important;
        width: 100% !important;
        // margin-right: 100px !important;
      }
    }

    .slick-list {
      @media @mobile {
        width: 103% !important;
        overflow-x: scroll !important;
      }
    }
  }
}


.slideWrap {
  max-width: 100vw;
  // min-height: 400px;
  padding: 0 10px;



  :global {
    .slick-track {
      display: flex !important;
      justify-content: center;
      align-items: stretch !important;
      transform: none !important;
    }

    .slick-slide {
      height: auto !important;

      &>*,
      &>*>* {
        height: 100%;
      }
    }

    .slick-list {
      padding-left: 5px !important;

      @media @mobile {
        padding-left: 0 !important;
      }
    }

    .slick-slider {
      @media @mobile {
        padding-left: 10px;
      }
    }

    .slick-slider:not(.no-nav) {
      @media @mobile {
        padding-bottom: 8px;
        margin-bottom: 0px;
      }
    }
  }
}

.productSlider {
  --gap: 24px;
  padding-inline: @SectionInlinePaddingDesktop;

  @media @tablet {
    --gap: 16px;
    padding-inline: @SectionInlinePaddingTablet;
  }

  @media @mobile {
    --gap: 12px;
    padding: 0;
  }

  :global {
    .slick-list {


      margin: 0 calc(var(--gap) / 2 * -1);

      transform: translateX(-70px);
      width: 106.4%;

      @media screen and (max-width: 1950px) {
        transform: translateX(-70px);
        width: 108.5%;
      }

      @media screen and (max-width: 1500px) {
        transform: translateX(-70px);
        width: 111.7%;
      }

      @media screen and (min-width: 820px) and (max-width: 1400px) {
        transform: translateX(-70px);
        width: 117%;
      }

      @media @tablet-strict {
        transform: translateY(-70px);
        width: 109.4%;
      }

      @media @mobile {
        transform: translateY(-70px);
        padding-left: 40px;

      }
    }

    .slick-track {
      display: flex;
    }

    .slick-slide {
      height: auto;

      &>* {
        height: 100%;
      }

      @media (min-width: 1920px) {
        flex-shrink: 0;
        height: 550px;
        width: 440px !important;
      }

      @media @mobile {
        flex-shrink: 0;
        height: 178px;
        width: 146px !important;
      }
    }
  }

  .sliderItem {
    padding: 0 calc(var(--gap) / 2);
    height: 100%;
    width: 100%;
  }

  &.lessItem {
    @media @mobile {
      padding-inline: @SectionInlinePaddingMobile;
    }
  }

  &.bannerSlider {
    position: relative;

    @media @desktop {
      padding-inline: 0;
    }
  }
}

.bannerImageSliderWrap {
  display: flex;
  align-items: center;
  gap: 32px;

  @media @tablet {
    display: block;

    .titleBlock {
      display: none;
    }
  }

  .bannerImage {
    flex: 0 0 47.5%;
    border-radius: 24px;
    overflow: hidden;

    @media (max-width: 1920px) and (min-width: 1024px) {
      height: 50%;
    }
  }

  .slideWrapBanner {
    flex: 1;
    min-width: 0;

    @media @tablet {
      margin-top: 24px;
    }
  }
}

.customNextBtn,
.customPrevBtn {
  width: 48px;
  height: 48px;
  position: absolute;
  top: 45%;
  transform: translateY(-50%);
  // transform: translate(-90px,-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: @PageBackground;
  // border: 1px solid @TextHeading;
  border-radius: 50%;

  @media @tablet {
    display: none;
  }

  svg>path {
    stroke: @TextHeading;
  }
}

.customNextBtn {
  // right: min(5vw, 72px);
  right: 10px;
  transform: translateY(106%);
}

.customPrevBtn {
  left: 25px;
  transform: translate(-90px, 106%) rotate(180deg);
}

.placeholderScroll,
.placeholderGrid {
  padding: 0 24px;

  @media @mobile {
    padding: 0 16px;
  }
}

.placeholderScroll {
  display: flex;

  &>* {
    flex: 0 0 calc(100% / var(--per_row));

    @media @mobile {
      flex: 0 0 calc(100% / var(--per_row_mobile));
    }
  }
}

@media @desktop {
  .desktopVisible {
    display: block;
  }

  .desktopVisibleFlex {
    display: flex;
  }

  .desktopHidden {
    display: none;
  }

  .desktopHiddenFlex {
    display: none;
  }

  .hideOnDesktop {
    display: none;
  }
}

@media @tablet {
  .mobileVisible {
    display: block;
  }

  .mobileHidden {
    display: none;
  }

  .hideOnDesktop {
    display: none;
  }
}

@media @mobile {
  .hideOnMobile {
    display: none;
  }

  .hideOnDesktop {
    display: block;
  }
}

.sectionbutton {
  // margin-top: 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  // width: 100%;
  padding: 12px 20px;
  margin-bottom: 40px;
  font-size: 14px;
  // text-align: center;
  font-family: "Helvetica Bold" !important;
  border-radius: 999px;
  background-color: #1A1A1A;
  color: #E6E6E6;

}

.section-button {

  // margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  // width: 100%;
  padding: 12px 30px;
  font-size: 16px;
  // text-align: center;
  font-family: "Helvetica Bold" !important;
  border-radius: 999px;
  background-color: #1A1A1A;
  color: #E6E6E6;



}


// .imageset{
//   height: 550px;
//   width: 440px;
// }