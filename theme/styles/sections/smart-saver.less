@import "../main.less";

.titleBlock {
    text-align: center;
    padding-inline: @SectionInlinePaddingDesktop;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;

    @media @tablet {
        padding-inline: @SectionInlinePaddingTablet;
        gap: 8px;
        margin-bottom: 24px;
    }

    @media @mobile {
        padding-inline: @SectionInlinePaddingMobile;
    }
}

.sectionHeading {
    font-size: 48px;
    font-family: "Helvetica Bold" !important;
    line-height: 115%;
    color: black;

    @media @mobile {
        font-size: 22px;
    }
}

.description {
    font-size: 18px;
    font-family: "Helvetica Medium" !important;
    line-height: 130%;

    @media @mobile {
        font-size: 12px;
    }
}

.imageGallery {
    //   border-radius: var(--bd-radius, 0);
    border-radius: 20px;

    @media @desktop {
        width: 300px;
        height: 300px;
    }

    @media screen and (min-width: 920px) and (max-width: 1430px) {
        width: 200px;
        height: 200px;
    }

    @media screen and (min-width: 1920px) {
        width: 422px;
        height: 422px;
    }

    @media @mobile {
        width: 163px;
        height: 152px;
    }

    -webkit-mask-image: -webkit-radial-gradient(white, black); //safari fix
}

.imageGrid {
    --row-gap: 32px;
    --col-gap: 24px;
    --col-count: var(--item-count);
    padding-inline: @SectionInlinePaddingDesktop;
    display: grid;
    gap: var(--row-gap) var(--col-gap);
    grid-template-columns: repeat(auto-fit,
            calc((100% / var(--col-count)) - (var(--col-gap) * (var(--col-count) - 1) / var(--col-count))));

    @media @tablet {
        --row-gap: 24px;
        --col-gap: 16px;
        padding-inline: @SectionInlinePaddingTablet;
    }

    @media @mobile {
        --row-gap: 14px;
        --col-gap: 14px;
        --col-count: var(--item-count-mobile);
        padding-inline: @SectionInlinePaddingMobile;
        // height: 100px;
        // width: 100px;
    }
}

.imageGridItem {
    //     @media @mobile {
    //     width: 163px;
    //     height: 152px;
    //     }
    //     @media @mobile {
    //     width: 163px;
    //     height: 152px;
    //     border-radius: 20px;
    // }
    display: flex;
    justify-content: center;

}

.imageSlider {
    --gap: 24px;
    padding-inline: @SectionInlinePaddingDesktop;

    @media @tablet {
        --gap: 12px;
        padding-inline: @SectionInlinePaddingTablet;
    }

    @media @mobile {
        padding: 0;
    }

    :global {
        .slick-list {
            margin: 0 calc(var(--gap) / 2 * -1);
        }
    }

    .sliderItem {
        padding: 0 calc(var(--gap) / 2);
    }

    &.mobileItemLess {
        @media @mobile {
            padding-inline: @SectionInlinePaddingMobile;
        }
    }
}

.hideOnDesktop {
    @media @desktop {
        display: none;
    }
}

.hideOnTablet {
    @media @tablet {
        display: none;
    }
}

.hideOnMobile {
    @media @mobile {
        display: none;
    }
}

.showOnMobile {
    @media @mobile-up {
        display: none;
    }
}

.imageWrapper {
    position: relative;
    display: inline-block;
    overflow: hidden;
}


.priceOff {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: red;
    height: 50px;
    border-top-left-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;

    @media @mobile {
        height: 30px;
        padding: 0 10px;
    }
}


.priceOff::before {
    width: 35px;
    height: 35px;
    content: "";
    position: absolute;
    bottom: 0;
    transform: rotate(270deg);
    right: 146px;
    background-color: red;
    clip-path: path("M1 0C1 19.3 15.3 35 35 35H0V0H1Z");

    @media @mobile {
        width: 20px;
        height: 20px;
        content: "";
        position: absolute;
        bottom: 0;
        transform: rotate(270deg);
        left: -19px;
        background-color: red;
        clip-path: path("M1 0C1 11 8.75 20 20 20H0V0H1Z");

    }
}

.offerText {
    font-size: 26px;
    font-family: "Helvetica Bold" !important;
    line-height: 130%;
    color: #fff;
    letter-spacing: 0.52px;
    text-transform: uppercase;

    @media @mobile {
        font-size: 14px;
        letter-spacing: 0.28px;
    }
}