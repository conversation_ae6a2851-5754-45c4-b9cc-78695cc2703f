.imageWrapper {
  img {
    transition: 300ms all cubic-bezier(0, 0, 0.2, 1);
  }
}

.imageHoverEnabled {
  img {
    &:hover {
      transform: scale(1.1);
    }
  }
}

.applicationBannerContainer {
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  .box-wrapper {
    width: var(--box_width);
    height: var(--box_height);
    opacity: 0;
    position: absolute;
    left: var(--x_position);
    top: var(--y_position);
    cursor: pointer;
    transform: translate(var(--x_offset), var(--y_offset));
  }

  .box-wrapper-visible {
    background: red;
    opacity: 0.5;
  }
}

.contentWrapper {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  background: transparent;
  border-radius: 0;
  backdrop-filter: none;
  font-family: "Helvetica Medium";
}

.heading {
  font-size: 48px;
  font-weight: 900;
  color: #212121;
  margin-bottom: 8px;
  // text-transform: uppercase;
}

.redText {
  color: #ff0000;
}

.description {
  font-size: 18px;
  font-weight: 400;
  color: #666;
  margin-bottom: 24px;
}

.button {
  background-color: white;
  display: flex;
  align-items: center;
  gap: 10px;
  color: black;
  padding: 16px 24px;
  border-radius: 999px;
  text-decoration: none;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Helvetica Bold" !important;
  // text-transform: uppercase;
}
