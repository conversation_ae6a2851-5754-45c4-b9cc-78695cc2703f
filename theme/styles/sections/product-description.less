@import "../../styles/main.less";

.productDescWrapper {
  @media @mobile {
    min-height: 90vh;
  }
}
.breadcrumbWrapper {
  margin-left: clamp(120px, 9.9vw, 190px);
  margin-top: clamp(12px, 1.04vw, 20px);
  margin-bottom: clamp(12px, 1.04vw, 20px);
  @media @tablet {
    display: none;
  }
}
.loader {
  position: relative;
  height: 90vh;
  width: 100%;
}

.productDescMobileAcc {
  @media @mobile-up {
    display: none;
  }
}
.productDescDesktop {
  height: fit-content;
  width: 100%;
}
// .line {
//   width: 100%;
//   height: 1px;
//   background-color: @DividerStokes;
//   margin-top: 50px;
//   margin-bottom: 50px;

//   @media @tablet {
//     margin-bottom: 1.875rem;
//   }
// }
.svgWrapper {
  position: relative;
  width: 24px;
  height: 24px;
}

.activeWishlist {
  color: @ButtonPrimary;
}

.imgWrap {
  position: relative;
}

/deep/.productDetails {
  margin-top: 20px;
  padding: 0 20px 20px 20px;
  h2 {
    text-align: center;
    padding: 20px 0;
    font-size: 1.5625rem;
    font-weight: 600;
  }
  .productLongDescription {
    line-height: 20px;
    font-size: 14px;
    overflow-wrap: break-word;
    b {
      font-weight: 700;
      margin-top: 25px;
      display: block;
    }
    br {
      content: "";
      display: block;
      margin-bottom: 10px;
    }
    p {
      margin-bottom: 10px;
      line-height: 20px;
      img {
        margin: 10px 0;
      }
    }
    video {
      max-width: 100% !important;
    }
  }
}

.productDescriptionContainer {
  background-color: #f2f2f2;
  @media (min-width: 769px) {
    display: flex;
    flex-direction: column;
    gap: 60px;
  }

  .mainContainer {
    // padding: 1rem 2.5rem 0;
    color: @TextBody;
    background-color: @BackgroundPdp;
    display: flex;
    flex-direction: column;
    gap: 60px;
    @media @tablet {
      gap: 8px;
    }

    .isDesktop {
      display: block;

      @media @tablet {
        display: none;
      }
    }
    .isMobile {
      display: block;

      @media @desktop {
        display: none;
      }
    }

    .productDescContainer {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      @media (min-width: 769px) {
        padding-bottom: 21px;
      }

      @media @tablet {
        flex-direction: column;
        max-height: max-content;
        margin-left: 0;
        margin-right: 0;
        gap: 0;
      }
      @media @desktop {
        margin-left: clamp(20px, 6.25vw, 120px);
        margin-right: clamp(20px, 6.4vw, 123px);
        gap: clamp(40px, 8.54vw, 164px);
        max-height: clamp(600px, 48.85vw, 938px);
      }

      .left {
        width: 54%;
        // margin-right: 40px;

        @media @tablet {
          width: 100%;
          box-sizing: border-box;
        }
      }
      .right {
        width: 41.82%;
        box-sizing: border-box;
        position: relative;
        overflow-y: scroll;
        @media (max-width: 1440px) {
          overflow-x: hidden;
        }

        @media @tablet {
          overflow: unset;
          width: 100%;
          padding: 0 10px;
        }
        @media @desktop {
          padding-top: 52px;
        }

        .priceContainer {
          display: flex;
          align-items: center;
          gap: 20px;
          @media @tablet {
            gap: 10px;
          }
        }
        .productTitleContainer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          align-items: flex-start;
          gap: 16px;
          flex-direction: column;
        }
        .bestSellerContainer {
          min-width: 80px;
          height: auto;
          display: flex;
          padding: 8px 12px;
          justify-content: center;
          align-items: center;
          gap: 8px;
          border-radius: 18.519px;
          background: var(--Dark, #1a1a1a);
          @media @tablet {
            display: none;
          }
        }

        .bestSellerText {
          color: var(--White, #fff);
          leading-trim: both;
          text-edge: cap;
          font-family: "Helvetica Medium";
          font-size: 1rem;
          font-style: normal;
          font-weight: 500;
          line-height: 130%; /* 20.8px */
          letter-spacing: 0.32px;
        }

        .preview {
          display: none;
          position: absolute;
          z-index: 1;
          margin-top: 15px;
          width: 100%;
        }
        .product {
          display: flex;
          flex-direction: column;

          @media @tablet {
            padding: 1.25rem 0.625rem;
          }
          @media @desktop {
            gap: 50px;
          }
          .titleBlock {
            .productBrand {
              color: @Dark;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Bold";
              font-size: 1.5rem;
              font-style: normal;
              font-weight: 700;
              line-height: 150%; /* 36px */
            }
            .productTitleWrapper {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              gap: 8px;

              .productTitle {
                color: @Dark;
                leading-trim: both;
                text-edge: cap;
                font-family: "Helvetica Bold" !important;
                font-size: 1.5rem;
                font-style: normal;
                font-weight: 700;
                line-height: 150%; /* 36px */
                flex: 1;
                line-height: 43px;
                position: relative;
                .text-line-clamp(2);

                @media @tablet {
                  line-height: 37px;
                  font-size: 1.125rem;
                  color: #000;
                  margin-bottom: 20px;
                }
              }
              .heartIcon {
                cursor: pointer;
                display: flex;
                min-width: clamp(40px, 2.71vw, 52px);
                min-height: clamp(40px, 2.71vw, 52px);
                justify-content: center;
                align-items: center;
                gap: 0.5rem;
                border-radius: 999px;
                border: 1px solid #e6e6e6;
                background: #fff;
                @media @tablet {
                  display: none;
                }
              }
              // .shareIcon {
              //   cursor: pointer;
              //   svg {
              //     g > path {
              //       fill: @TextBody;
              //     }
              //     rect {
              //       fill: @PageBackground;
              //     }
              //   }
              // }
            }
          }

          .taxLabel {
            color: @Dark;
            font-family: "Helvetica Medium";
            font-size: 0.875rem;
            font-style: normal;
            font-weight: 500;
            line-height: 130%; /* 18.2px */
            letter-spacing: 0.28px;

            @media @tablet {
              font-size: 0.75rem;
              color: #000;
              opacity: 0.5;
            }
          }
          .fitTypeWrapper {
            display: flex;
            flex-direction: column;
            gap: 30px;
            // @media @tablet {
            // margin-bottom: 1.875rem;
            // }
            .line {
              width: 100%;
              height: 1px;
              background-color: @Dark-10;
              margin-bottom: 1.875rem;
              @media (min-width: 769px) {
                display: none;
              }
            }
          }

          .fitTypeContainer {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 8px;
          }
          .fitTypeTextContainer {
            display: flex;
            padding: 12px 24px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            border-radius: 60px;
            border: 0.8px solid @Dark-20;
            background: @White;
            width: fit-content;
            border-radius: 60px;
            border: 0.8px solid var(--Dark-20, #ccc);
            background: #fff;
            @media @tablet {
              padding: 0.5rem 0.75rem;
            }

            .fitTypeText {
              color: @Dark-80;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Bold";
              font-size: 1rem;
              font-style: normal;
              font-weight: 700;
              line-height: 130%; /* 20.8px */
              @media @tablet {
                font-size: 13px;
                color: @Dark-80;
              }
            }
          }

          .shortDescription {
            margin: 24px 0;
          }

          .reviewRatingContainer {
            background-color: @ThemeAccentL4;
            border-radius: 4px;
            display: flex;
            width: fit-content;
            align-items: center;
            padding: 4.5px 10px;
            margin: 16px 0 24px;

            @media @tablet {
              margin: 24px 0;
            }

            .ratingWrapper {
              display: flex;
              align-items: center;

              .ratingIcon {
                width: 14px;
                height: 14px;
                margin-left: 4px;

                svg > path {
                  fill: @ThemeAccentD2;
                }
              }
            }

            .separator {
              border-left: 1px solid @DividerStokes;
              height: 100%;
              margin: 0 6px;
              width: 1px;
            }

            .reviewWrapper {
              color: @TextLabel;
            }
          }
          &__price {
            display: flex;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 12px;
            flex-direction: column;

            @media @tablet {
              font-size: 1rem;
              line-height: 130%;
              margin-bottom: 20px;
            }

            .mrpStrike {
              display: flex;
              margin-right: 8px;
              justify-content: center;
              align-items: center;
              gap: 6px;

              &::after {
                content: "";
                position: absolute;
                bottom: 50%;
                left: 0;
                width: 100%;
                height: 0.5px;
                transform: translateY(50%);
                background: @TextLabel;
              }

              &:empty {
                display: none;
              }
            }

            &--effective {
              color: @Dark;
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Bold";
              font-size: 1.375rem;
              font-style: normal;
              font-weight: 700;
              line-height: 130%; /* 28.6px */

              @media @tablet {
                line-height: 19px;
                min-height: 19px;
                font-size: 1rem;
              }
            }

            &--marked {
              color: var(--Dark-40, #999);
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Medium";
              font-size: 1.125rem;
              font-style: normal;
              font-weight: 400;
              line-height: 120%; /* 21.6px */
              letter-spacing: -0.18px;
              text-decoration-line: line-through;
              @media @tablet {
                font-size: 16px;
              }
            }

            &--discount {
              color: var(--Brand, #ff1e00);
              leading-trim: both;
              text-edge: cap;
              font-family: "Helvetica Bold";
              font-size: 1.125rem;
              font-style: normal;
              font-weight: 700;
              line-height: 130%; /* 23.4px */
              @media @tablet {
                font-size: 16px;
              }
            }
            .mrpLabel {
              color: @Dark-40;
              font-weight: 400;
              font-family: "Helvetica Medium";
              font-size: 1.125rem;
              line-height: 14px;
              letter-spacing: -0.02em;
              @media @tablet {
                font-size: 16px;
              }

              &--effective {
                margin-right: 4px;
                @media @tablet {
                  font-size: 16px;
                }
              }
              &--marked {
                margin-right: 0.25rem;
                color: @Dark-40;
                font-weight: 400;
                font-size: 1.125rem;
                line-height: 14px;
                letter-spacing: -0.02em;
                text-decoration-line: line-through;
                @media @tablet {
                  font-size: 16px;
                }
              }
            }
          }

          &__size {
            &--guide {
              cursor: pointer;
              margin-top: 1rem;
              display: flex;
              align-items: center;
              border: none;
              color: @ButtonPrimary;
              background-color: transparent;

              span {
                margin-right: 2px;
              }

              .scaleIcon {
                width: 25px;
                height: 12px;
              }
            }
          }
        }

        .tagsBlock {
          display: flex;
          flex-wrap: wrap;
          border-radius: 60px;
          border: 0.8px solid var(--Dark-20, #ccc);
          background: #fff;
          padding: 0.75rem 1.5rem;
          justify-content: center;
          align-items: center;
          gap: 0.5rem;

          .tagItem {
            color: var(--Dark-80, #333);
            leading-trim: both;
            text-edge: cap;
            font-family: "Helvetica Now Display";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 130%; /* 20.8px */
          }

          .colorBlock {
          }
        }
      }

      .sizeCartContainer {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 3.125rem;

        @media @tablet {
          display: none;
        }

        .actionBuyNow {
          flex: 1;

          button {
            margin-top: 0;
          }

          &--ml-12 {
            margin-left: 12px;

            @media @tablet {
              margin-left: 8px;
            }
          }
        }

        .sizeWrapper {
          position: relative;
          min-width: 0;
          flex-grow: 0;
          transition: all 0.5s;
          flex-basis: 33.33%;
          margin-right: 12px;
          // @media @tablet {
          //   padding-top: 24px;
          // }
          &.sizeWrapper--collapse {
            overflow: hidden;
            flex-basis: 0;
            margin-right: 0;
          }

          @media @tablet {
            margin-right: 8px;
          }

          .sizeButton {
            padding: 11px 16px;
            border: 0.8px solid @DividerStokes;
            border-radius: @ButtonRadius;
            background-color: @ThemeAccentL4;
            width: 100%;
            height: 100%;
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            letter-spacing: -0.02em;
            text-transform: uppercase;
            color: @TextHeading;

            @media @tablet {
              font-size: 12px;
              line-height: 14px;
            }

            @media @desktop {
              padding: 16px 16px;
            }

            .selectedSize {
              text-align: left;
              color: @TextHeading;
              border-radius: unset;
              .text-line-clamp();
            }

            .dropdownArrow {
              height: 24px;
              width: 24px;
              flex: 0 0 24px;
            }

            .rotateArrow {
              transform: rotate(180deg);
            }
          }
          .disabledButton {
            color: @TextDisabled;
          }
          .sizeDropdown {
            position: absolute;
            background-color: @DialogBackground;
            top: calc(100% + 4px);
            min-width: 100%;
            white-space: nowrap;
            border: 1px solid #d4d1d1;
            box-shadow:
              0px 4px 4px rgba(0, 0, 0, 0.15),
              0px 12px 16px rgba(0, 0, 0, 0.16);
            border-radius: min(@ButtonRadius, 8px);
            padding: 0.5rem;
            z-index: 1;

            li {
              padding: 8px 12px;
              border-radius: calc(min(@ButtonRadius, 8px) / 2);
            }

            .selected_size {
              background-color: @ThemeAccentL3;
            }

            .disabled_size {
              text-decoration-line: line-through;
              color: @TextDisabled;
              text-decoration: line-through;
              cursor: not-allowed;
            }

            .selectable_size {
              cursor: pointer;

              &:hover {
                background-color: @HighlightColor;
              }
            }
          }
        }

        .cartWrapper {
          flex: 2;
          display: flex;
          flex-direction: column;
          gap: 1.25rem;
          justify-content: flex-start;
          align-items: flex-start;

          &--half-width {
            flex: 1 !important;
          }

          .addToCart {
            @media @desktop {
              &:hover {
                .cartIcon {
                  fill: @Dark-5;
                }
              }
            }

            @media @tablet {
              display: none;
            }

            // .cartIcon {
            //   height: 18px;
            //   width: 18px;
            //   margin-right: 5px;
            //   fill: @Dark-5;
            //   transition: all 0.4s;
            // }
          }

          .notAvailable {
            background-color: @ButtonPrimaryL3;
            cursor: unset;
          }

          .qtyContainer {
            height: 100%;
            justify-content: space-between;
            border-radius: @ButtonRadius;
            @media @tablet {
              padding: 12px;
            }
            padding: 18px 12px;
          }

          .inputContainer {
            width: 100%;
            border: none;
          }
        }
      }

      .button {
        color: @Dark-5;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold" !important;
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 23.4px */
        width: 100%;
        padding: 12px 30px;
        cursor: pointer;
        transition: all 0.4s;
        border-radius: 999px;
        background: @Dark;

        @media @desktop {
          height: 50px !important;
        }

        @media @tablet {
          padding: 16px;
          font-size: 12px;
        }
      }

      .buyNow {
        margin-top: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 0.8px solid transparent;
        svg > path {
          fill: @ButtonSecondary;
        }
        &__icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        @media @tablet {
          margin-top: 8px !important;
          display: none !important;
        }

        .customIcon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }

      .customBtn {
        margin: 24px 0;
        .customBtnStyle {
          margin-top: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 0.8px solid transparent;

          svg > path {
            fill: @ButtonSecondary;
          }

          &__icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          @media @tablet {
            margin-top: 8px !important;
          }

          .customIcon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }
      }

      .deliveryInfoBlock {
        // margin-top: 48px;

        // @media @mobile {
        //   margin-top: 24px;
        // }

        // @media @tablet-strict {
        //   margin-top: 32px;
        // }
      }

      .compareBtn {
        margin: 24px 0px;
      }
      .compare-container {
        display: flex;
        padding: 12px 32px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        width: fit-content;
        border-radius: 4px;
        border: 0.8px solid @DividerStokes;
        color: @ButtonPrimary;
        background: @ButtonSecondary;
        margin: 24px 0;
        font-weight: 500;
        line-height: normal;
        letter-spacing: -0.28px;
        text-transform: uppercase;
        cursor: pointer;
        @media @tablet {
          margin: 16px 0 32px 0;
          font-size: 12px;
          letter-spacing: -0.24px;
        }

        .compare-icon ::v-deep .inline-svg svg {
          width: 18px !important;
          height: 18px !important;
        }
      }
      .mt-2 {
        margin-top: 2rem;
      }

      .top-spacing {
        margin-top: 24px;
      }

      .productDetail {
        list-style: outside;
        padding-left: 1rem;
        margin-top: 24px;
        font-size: 14px;
        line-height: 18px;
        letter-spacing: -0.28px;

        li {
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        @media @tablet {
          font-size: 12px;
        }
      }

      .sellerInfo {
        line-height: 20px;
        margin-top: 24px;

        .storeSeller {
          display: flex;
          align-items: center;
          min-height: 15px;
          @media @tablet {
            margin-bottom: 12px;
          }

          .soldByLabel {
            white-space: nowrap;
          }

          .selectable {
            border-bottom: 1px solid @TextBody;
            cursor: pointer;
            height: 40px;
            width: 40px;
          }

          .nameWrapper {
            display: flex;
            align-items: center;
            margin-left: 5px;
            overflow: hidden;

            .storeSellerName {
              display: block;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }

            .otherSellers {
              white-space: nowrap;
            }

            .dropdownArrow {
              height: 12px;
              width: 12px;
              margin-left: 2px;

              /deep/ svg {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }

      .bottom-spacing {
        margin-bottom: 24px;

        @media @tablet {
          margin-bottom: 32px;
        }
      }

      .short-description {
        margin: 24px 0;
      }

      /deep/ .toast {
        text-transform: unset;
      }

      .sizeWrapperContainer {
        display: flex;
        flex-direction: column;
        gap: 3.125rem;
        @media @tablet {
          // margin-bottom: 1.875rem;
          gap: 1.875rem;
        }
      }

      .sizeSelection {
        display: grid;
        grid-template-rows: 1fr;
        transition: grid-template-rows 0.5s;

        &--collapse {
          //grid-template-rows: 0fr;
          display: none;
        }
        & > div {
          overflow: hidden;
        }
        .sizeLabel {
          color: @Dark;
          leading-trim: both;
          text-edge: cap;
          font-family: "Helvetica Bold";
          font-size: 1.125rem;
          font-style: normal;
          font-weight: 700;
          line-height: 130%; /* 23.4px */
          @media @tablet {
            font-size: 1rem;
            color: #000;
          }
        }
        .sizeSelection__container {
          display: flex;
          align-items: flex-start;
          justify-content: center;
          flex-direction: column;
          gap: 1.25rem;
        }
        &__findSize {
          color: @Dark;
          leading-trim: both;
          text-edge: cap;
          font-family: "Helvetica Medium";
          font-size: 1rem;
          font-style: normal;
          font-weight: 500;
          cursor: pointer;
          line-height: 130%; /* 20.8px */
          letter-spacing: 0.16px;
          text-decoration-line: underline;
          text-decoration-style: solid;
          text-decoration-skip-ink: none;
          text-decoration-thickness: auto;
          text-underline-offset: auto;
          text-underline-position: from-font;
          @media @tablet {
            font-size: 14px;
            color: @Dark;
          }
        }

        &__label {
          // margin: 12px 0;
          // margin-top: 24px;

          span {
            font-weight: bold;
          }
        }

        &__wrapper {
          display: flex;
          flex-wrap: wrap;
          .grid-gap(12px);
        }

        &__header {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        &__block {
          border-radius: 8.032px;
          border: 0.803px solid @Dark-20;
          // min-width: 40px;
          width: 56px;
          height: 44px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          position: relative;
          display: flex;
          padding: 0.5rem 0.875rem;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: @Dark-40;
          @media @tablet {
            padding: 0.5rem 0.75rem;
            height: 40px;
            width: 40px;
          }

          .sizeText {
            leading-trim: both;
            text-edge: cap;
            font-family: "Helvetica Medium";
            font-size: 1rem;
            font-style: normal;
            font-weight: 500;
            line-height: 120%; /* 19.2px */
            letter-spacing: -0.16px;
            @media @tablet {
              font-size: 14px !important;
            }
          }

          &--selected {
            color: #fff;
            background: @Brand;
          }

          &--selectable {
            cursor: pointer;
          }

          &--disable {
            cursor: default;
            color: @TextDisabled;
          }

          svg {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;

            line {
              stroke: @DividerStokes;
              stroke-width: 1;
            }
          }
        }

        &__footer {
          width: 100%;
        }
      }
      .sizeSelection__footer {
        position: relative;
        @media @tablet {
          display: none;
        }
      }

      .sizeSelection__footerText {
        color: @Dark-85;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold";
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 23.4px */
      }

      .sizeSelection__footerTextLink {
        color: @Brand;
        leading-trim: both;
        text-edge: cap;

        /* H5 */
        font-family: "Helvetica Bold";
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 700;
        line-height: 130%;
      }

      .notifyAccordion {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        padding: clamp(16px, 1.56vw, 30px);
        margin-bottom: 8px;
        animation: fadeInAccordion 0.3s;
        width: 100%;
        box-sizing: border-box;
      }

      @keyframes fadeInAccordion {
        from {
          opacity: 0;
          transform: translateY(16px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .notifyTitle {
        color: @Dark-85;
        leading-trim: both;
        text-edge: cap;

        /* H5 */
        font-family: "Helvetica Bold";
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 23.4px */
        margin-bottom: 1.875rem;
      }

      .notifyFieldContainer {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 1rem;
      }

      .notifyField {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        label {
          display: block;
          color: @Dark;
          leading-trim: both;
          text-edge: cap;
          font-family: "Helvetica Bold";
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          line-height: 130%; /* 18.2px */
          letter-spacing: 0.28px;
        }
      }

      .notifyDropdown,
      .notifyInput {
        width: 100%;
        padding: 0.625rem;
        border: none;
        border-bottom: 1px solid @Dark-20;
        background: transparent;
        outline: none;
        color: @Dark-60;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold" !important;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 18.2px */
        letter-spacing: 0.28px;
      }

      .notifyButton {
        width: 100%;
        padding: 0.75rem 0;
        border: 1.5px solid @Dark;
        border-radius: 24px;
        cursor: pointer;
        color: @Dark;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold" !important;
        font-size: 1rem;
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 20.8px */
        letter-spacing: 0.32px;

        transition: background 0.2s;
        &:hover {
          background: #f5f5f5;
        }
      }

      .notifyPrivacy {
        font-size: 0.85rem;
        color: #888;
        text-align: center;
        color: @Dark-60;
        leading-trim: both;
        text-edge: cap;
        font-family: "Helvetica Bold";
        font-style: normal;
        font-weight: 700;
        line-height: 130%; /* 18.2px */
        letter-spacing: 0.28px;
      }

      .loading-height {
        &.sizeSelection__wrapper {
          min-height: 32.5px;
        }
      }
      .fade-enter-active,
      .fade-leave-active {
        transition: all 0.3s;
      }

      .stickyAddToCart {
        position: fixed;
        bottom: 0;
        display: flex;
        width: 100%;
        background: @PageBackground;

        @media @desktop {
          display: none;
        }
      }

      /* Custom PDP redesign blocks */
      .headerBlock {
        margin-top: 24px;
        .headerTop {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .badgeBestSeller {
            background: var(--Dark, #1a1a1a);
            padding: 6px 10px;
            border-radius: 18px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
          }
          .badgeText {
            color: #fff;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.05em;
            text-transform: uppercase;
          }
        }
      }

      .tagsBlock {
        margin: 12px 0;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .tagItem {
          padding: 6px 12px;
          border-radius: 24px;
          background: #222;
          color: #fff;
          font-size: 12px;
          text-transform: capitalize;
        }
      }

      .colorBlock {
        display: flex;
        gap: 12px;
        margin: 12px 0;

        img {
          width: 56px;
          height: 72px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid #444;
          cursor: pointer;
        }
      }

      .sizeBlock {
        margin-top: 16px;
        display: flex;
        gap: 8px;

        .sizeItem {
          padding: 10px 16px;
          border-radius: 4px;
          border: 1px solid @DividerStokes;
          background: @PageBackground;
          font-size: 12px;
          text-transform: uppercase;
          cursor: pointer;
          &.selected {
            background: @ButtonPrimary;
            color: @ButtonSecondary;
          }
        }
      }

      .cartBlock {
        margin-top: 24px;
      }

      .notifyBlock {
        margin-top: 12px;
        font-size: 12px;
        a {
          color: @ButtonPrimary;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .occassionsToWearContainerMobile {
        padding: 0 10px;

        @media (min-width: 769px) {
          display: none;
        }
      }
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: @Dark-10;

    @media (max-width: 768px) {
      margin-bottom: 1.875rem;
      margin-top: 0;
    }
  }
  .subContainer {
    display: flex;
    flex-direction: column;
    gap: 8px;
    @media (min-width: 769px) {
      gap: 140px;
    }
  }
}

// Mobile breadcrumb styling
.product {
  .isMobile {
    margin: 16px 0 8px 0;
    padding: 0 10px;

    @media @desktop {
      display: none;
    }
  }
}
