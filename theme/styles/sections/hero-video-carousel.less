@import "../main.less";

.heroVideoCarousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 30px 30px 24px 30px; 
  height: 904px;
}

.carouselContainer {
  width: 100%;
  height: 850px;
  overflow: hidden;
  border-radius: 20px;
  
}


.carouselItems {
  display: flex;
  scroll-snap-type: x mandatory;
  overflow-x: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  height: 100%;
  gap: 10px;

  &::-webkit-scrollbar {
    display: none;
  }
}

.carouselItem {
  flex: 0 0 100%;
  scroll-snap-align: start;
  width: 100%;
  height: 100%;
  position: relative;
}

.videoItem {
  width: 100%;
  height: 100%;
  position: relative;
}

.video,
.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Progress bar wrapper */
.carouselProgressBarWrapper {
  position: sticky;
  bottom: 10px;
  left: 10px;
  right: 10px;
  display: flex;
  justify-content: center;
  gap: 10px;
  z-index: 20;
  cursor: pointer;
}

/* Each progress bar track */
.progressTrack {
  height: 4px;
  width: 30px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

/* Filler for animated progress */
.progressFill {
  height: 100%;
  background-color: #ff0000;
  width: 0%;
  transition: width 0.1s linear;
  border-radius: 2px;
}

@media (max-width: 767px) {
  .heroVideoCarousel {
    height: auto;
    padding: 10px 20px 0 20px;
  }

  .carouselContainer {
    height: 432px;
  }

  .carouselProgressBarWrapper {
    bottom: 8px;
    margin: 20px 10px;
  }

  .progressTrack {
    height: 3px;
    width: 47px;
  }
}