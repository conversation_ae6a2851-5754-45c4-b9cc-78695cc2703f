.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-between() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-select-none() {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.text-line-clamp(@line-count: 1) {
  display: -webkit-box;
  -webkit-line-clamp: @line-count;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.overflow-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
}
// Mixins for ios 14 and below
.column-gap(@gap) {
  & > *:not(:last-child) {
    margin-right: @gap;
  }
}

.row-gap(@gap) {
  & > *:not(:last-child) {
    margin-bottom: @gap;
  }
}

.inset(@position) {
  top: @position;
  bottom: @position;
  left: @position;
  right: @position;
}
.aspect-ratio(@ratio) {
  position: relative;
  & > * {
    position: absolute;
    .inset(0);
  }
  &:before {
    content: "";
    display: block;
    padding-bottom: ~"calc(100% * (1 /(@{ratio})))";
  }
}

.grid-gap(@row-gap, @column-gap: @row-gap) {
  @supports not (gap: @row-gap @column-gap) {
    margin-left: -(@column-gap / 2);
    margin-right: -(@column-gap / 2);
    margin-bottom: -@row-gap;
    & > * {
      padding-left: @column-gap / 2;
      padding-right: @column-gap / 2;
      margin-bottom: @row-gap;
    }
  }

  @supports (gap: @row-gap @column-gap) {
    gap: @row-gap @column-gap;
  }
}

// Topography Mixin
.h3(@screen) {
  font-weight: 700;
  letter-spacing: -0.02em;
  color: @TextHeading;

  & when (@screen = desktop) {
    font-size: 28px;
    line-height: 36px;
  }

  & when (@screen = mobile) {
    font-size: 24px;
    line-height: 32px;
  }
}

.h4(@screen) {
  font-weight: 600;
  letter-spacing: -0.02em;
  color: @TextHeading;

  & when (@screen = desktop) {
    font-size: 20px;
    line-height: 24px;
  }

  & when (@screen = mobile) {
    font-size: 16px;
    line-height: 18px;
  }
}

.b2(@screen) {
  font-weight: 400;
  letter-spacing: -0.02em;
  color: @TextBody;

  & when (@screen = desktop) {
    font-size: 14px;
    line-height: 18px;
  }

  & when (@screen = mobile) {
    font-size: 12px;
    line-height: 16px;
  }
}

.b2(@screen) {
  font-weight: 400;
  letter-spacing: -0.02em;
  color: @TextBody;

  & when (@screen = desktop) {
    font-size: 14px;
    line-height: 18px;
  }

  & when (@screen = mobile) {
    font-size: 12px;
    line-height: 16px;
  }
}

.b5(@screen) {
  font-weight: 400;
  letter-spacing: -0.02em;
  color: @TextBody;

  & when (@screen = desktop) {
    font-size: 12px;
    line-height: 14px;
  }

  & when (@screen = mobile) {
  }
}

.btn-font(@screen) {
  font-weight: 500;
  letter-spacing: -0.02em;
  & when (@screen = desktop) {
    font-size: 14px;
    line-height: 16px;
  }

  & when (@screen = mobile) {
    font-size: 12px;
    line-height: 14px;
  }
}

.button-font() {
  .btn-font(mobile);
  border-radius: @ButtonRadius;
  cursor: pointer;
  @media @desktop {
    .btn-font(desktop);
  }
}

.fontBody() {
  font-family: var(--font-body);
}

.fontHeader() {
  font-family: var(--font-header);
}

.padding(@padding) {
  padding: @padding;
}

.padding(@vertical,@horizontal) {
  padding-top: @vertical;
  padding-bottom: @vertical;
  padding-left: @horizontal;
  padding-right: @horizontal;
}

.padding-horizontal(@padding) {
  padding-left: @padding;
  padding-right: @padding;
}

.padding-vertical(@padding) {
  padding-top: @padding;
  padding-bottom: @padding;
}

.padding-top(@padding) {
  padding-top: @padding;
}

.padding-bottom(@padding) {
  padding-bottom: @padding;
}

.padding-left(@padding) {
  padding-left: @padding;
}

.padding-right(@padding) {
  padding-right: @padding;
}

.margin(@margin) {
  margin: @margin;
}

.margin(@vertical,@horizontal) {
  margin-top: @vertical;
  margin-bottom: @vertical;
  margin-left: @horizontal;
  margin-right: @horizontal;
}

.margin-horizontal(@margin) {
  margin-left: @margin;
  margin-right: @margin;
}

.margin-vertical(@margin) {
  margin-top: @margin;
  margin-bottom: @margin;
}

.margin-top(@margin) {
  margin-top: @margin;
}

.margin-bottom(@margin) {
  margin-bottom: @margin;
}

.margin-left(@margin) {
  margin-left: @margin;
}

h-1 {
  height: 1rem;
}

h-2 {
  height: 2rem;
}

h-3 {
  height: 3rem;
}

h-4 {
  height: 4rem;
}

h-5 {
  height: 5rem;
}

h-6 {
  height: 6rem;
}

h-7 {
  height: 7rem;
}

h-8 {
  height: 8rem;
}

h-9 {
  height: 9rem;
}
