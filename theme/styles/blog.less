@import "../styles/main.less";

.loader {
  position: relative;
  height: 90vh;
  width: 100%;

  .loader<PERSON>ontainer {
    position: absolute;
    height: 100%;
    background: transparent;

    .customLoader {
      margin-left: 0;
    }
  }
}

.paginationWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
}

.imageWrapper {
  &:before {
    content: unset;
  }
  ::v-deep {
    .fy__img {
      object-fit: cover;
    }
    & > * {
      position: static;
    }
    picture {
      width: 100%;
      height: 100%;
    }
  }
}
.tagBtn {
  border: none;
  white-space: nowrap;
  background-color: @ButtonSecondary;
  color: @ButtonPrimary;
  border-radius: @ButtonRadius;
  margin-right: 6px;
  padding: 9px 16px;
  text-transform: capitalize;
  box-shadow: 1px 3px 9px 1px @ThemeAccentL4;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  &Selected {
    background-color: @ButtonPrimary;
    color: @ButtonSecondary;
  }
}
.blogContainer {
  font-size: 14px;
  line-height: 20px;
  max-width: @page-width;
  margin-left: auto;
  margin-right: auto;
  padding: 40px 16px 0;
  @media @mobile-up {
    padding: 55px 24px 0;
  }
  @media @desktop {
    padding: 72px 40px 0;
  }
  &__carousel {
  }
  .blog__content {
    display: grid;
    grid-column-gap: 30px;
    grid-row-gap: 16px;
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    grid-auto-columns: 1fr;
    padding-bottom: 40px;
    @media @mobile-up {
      padding-bottom: 60px;
    }
    @media @desktop {
      grid-template-columns: 0.85fr 0.4fr;
    }
    &Full {
      grid-template-columns: 1fr;
      .blogContainer__grid {
        grid-template-columns: repeat(4, 1fr);
        @media @tablet {
          grid-template-columns: repeat(3, 1fr);
        }
        @media @mobile {
          grid-template-columns: 1fr;
        }
      }
    }
  }
  .blog__contentRight {
    margin-top: 86px;
    // &.mt-30 {
    //   margin-top: 30px;
    // }
    @media @mobile {
      // &,&.mt-30 {
      margin-top: 2rem;
      // }
    }
  }
  &__grid {
    display: grid;
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: auto;
    grid-auto-columns: 1fr;
    margin-top: 30px;
    @media @mobile {
      grid-template-columns: 1fr;
    }
  }
  &__pagination {
    margin: 24px 0 20px;
  }
}
.sliderWrapper {
  .blogItem {
    font-size: 14px;
    line-height: 20px;
    row-gap: 32px;
    flex-direction: column-reverse;
    align-items: flex-start;
    background: @ThemeAccentL5;
    border-radius: @ImageRadius;
    overflow: hidden;
    cursor: auto;
    user-select: auto;
    display: flex;
    --gap: 24px;
    margin-right: 0 calc(var(--gap) / 2);
    @media @tablet {
      --gap: 16px;
    }
    @media @desktop {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      column-gap: 48px;
      background: transparent;
      border-radius: 0;
    }
    @media @mobile {
      align-items: stretch;
      --gap: 12px;
      margin-right: var(--p-right-mob, 12px);
    }
    &__info {
      padding: 0 1rem 1rem;
      @media @desktop {
        padding: 0;
        max-width: 600px;
      }
    }
    &__meta {
      margin-bottom: 8px;
      @media @mobile {
        display: flex;
        justify-content: space-between;
      }
      .divider {
        padding-left: 4px;
        padding-right: 4px;
        @media @mobile {
          display: none;
        }
      }
    }
    &__title {
      font-size: 32px;
      line-height: 36px;
      @media @mobile {
        font-size: 20px;
        line-height: 133%;
      }
    }
    &__image {
      width: 100%;
      height: 200px;

      @media @mobile-up {
        width: 100%;
        height: 300px;
      }
      @media @desktop {
        border-radius: @ImageRadius;
        flex: 0 0 500px;
        width: 500px;
      }
      @media @desktop-xl {
        flex: 0 0 650px;
        width: 650px;
        height: 400px;
      }
    }
    &__content {
      margin-top: 16px;
      margin-bottom: 8px;
      font-size: 16px;
      line-height: 22px;
      @media screen and (max-width: 767px) {
        font-size: 14px;
        line-height: 24px;
      }
      @media @mobile {
        display: none;
      }
      @media @desktop-xl {
        font-size: 18px;
        line-height: 24px;
      }
    }
    &__button {
      display: inline-block;
      border-radius: @ButtonRadius;
      margin-top: 24px;
      font-size: 16px;
      font-weight: 600;
      @media @mobile-up {
        height: 48px;
        padding: 16px 48px;
      }
      @media @desktop {
        height: auto;
        margin-top: 32px;
      }
      @media @mobile {
        background: transparent;
        border: none;
      }
    }
  }
}
.filterWrapper {
  padding-top: 40px;
  @media @mobile-up {
    padding-top: 60px;
  }
}
.filterWrapper {
  line-height: 20px;
  &__header {
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    .resetBtn {
      padding-bottom: 0.2rem;
      cursor: pointer;
    }
  }
  &__content {
    display: grid;
    grid-column-gap: 32px;
    grid-row-gap: 16px;
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    grid-auto-columns: 1fr;
    align-items: flex-start;
    @media @desktop {
      grid-template-columns: 0.85fr 0.4fr;
    }
  }
}
.tagList {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  &::-webkit-scrollbar {
    display: none !important;
  }
  @media @desktop {
    flex-wrap: wrap;
    padding: 6px 0;
  }
}
.blogSearch {
  display: flex;
  justify-content: flex-end;
  @media @tablet {
    order: -1;
  }
  &__input {
    border: 1px solid @DividerStokes;
    background-color: @ThemeAccentL5;
    border-radius: @ButtonRadius;
    width: 96%;
    height: 50px;
    padding: 8px 16px;
    box-shadow: 0 4px 9px 4px rgba(179, 179, 180, 0.15);
    @media @tablet {
      width: 100%;
      margin-top: 0;
      margin-bottom: 10px;
    }
  }
}
.filterList {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem 1rem;
  min-height: 3.5rem;
  margin-bottom: 10px;
  padding-top: 20px;
  .filterItem {
    font-size: 12px;
    font-weight: 600;
    border-radius: @ButtonRadius;
    background-color: @ButtonPrimary;
    color: @ButtonSecondary;
    margin-right: 8px;
    padding: 8px 40px 8px 16px;
    position: relative;
    text-transform: capitalize;
    &__icon {
      width: 20px;
      height: 20px;
      padding: 0.25rem;
      position: absolute;
      top: 50%;
      right: 8px;
      transform: translateY(-50%);
      fill: @ButtonSecondary;
    }
  }
}
.blog {
  background: @ThemeAccentL5;
  border-radius: @ImageRadius;
  overflow: hidden;
  border: 1px solid @ThemeAccentL4;
  height: 100%;
  &:hover {
    .blog__image {
      ::v-deep img {
        transform: scale(1.1);
      }
    }
  }
  &__info {
    padding: 1rem;
  }
  &__meta {
    line-height: 20px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
  }
  &__title {
    font-size: 16px;
    line-height: 24px;
    ::v-deep .highlight {
      background-color: @ButtonPrimary;
      color: @ButtonSecondary;
    }
  }
  &__highlight {
    background-color: @ButtonPrimary;
    color: @ButtonSecondary;
  }
  &__image {
    height: 180px;
    transition: 300ms transform ease;
    overflow: hidden;

    &:hover {
      transform: scale(1.1);
    }
  }
}
