{"name": "Turbo-payment", "version": "1.0.152", "description": "", "main": "index.js", "license": "ISC", "scripts": {"build": "npm run clean && webpack --mode production", "dev": "npx concurrently \" npm run build:dev \" \" npm run serve \" ", "build:dev": "npm run clean && npm run build:local -- --watch", "build:local": "npm run clean && webpack --env local", "clean": "rm -rf dist && rm -rf dist_sections", "serve": "npx nodemon scripts/server.js \"--ignore src/*\" ", "publish:theme": "npm run build && node scripts/publish.js", "publish:local": "npm run build:local && node scripts/publish.js --local", "test": "echo \"Error: no test specified\" && exit 1", "upload-sections": "node scripts/extract-section-props.js", "lint": "eslint . --ext .js,.jsx", "lint:fix": "eslint . --ext .js,.jsx --fix", "format": "prettier --write \"theme/**/*.{js,jsx,json,css,md}\"", "refresh": "npm uninstall fdk-store && npm install gitlab:fynd/regrowth/fynd-platform/themes/shadowfire.git#develop", "husky": "npm install husky --save-dev && husky install && npx husky add .husky/pre-commit 'npm run lint && npm run format'", "tailwind": "npx tailwindcss -i ./theme/styles/tailwind-base.global.css -o ./theme/styles/tailwind.global.css --watch"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run format"}}, "fdk_feature": {"enable_section_chunking": true}, "devDependencies": {"@loadable/babel-plugin": "^5.16.1", "@loadable/component": "^5.16.4", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "file-loader": "^6.2.0", "husky": "^8.0.3", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.2", "prettier": "^3.3.3", "react-hydration-overlay": "^0.1.2", "tailwindcss": "^3.4.17", "three": "^0.176.0", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@emotion/is-prop-valid": "^1.3.1", "@gofynd/fdk-client-javascript": "^1.4.5", "@google/model-viewer": "^3.5.0", "@pixelbin/core": "^6.1.0", "@react-google-maps/api": "^2.19.3", "@svgr/webpack": "^8.0.1", "awesome-snackbar": "^2.0.7", "babel-loader": "^9.1.0", "buffer": "^6.0.3", "card-validator": "8.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "dompurify": "^3.1.6", "fdk-react-templates": "github:gofynd/fdk-react-templates#V.0.0.339", "fdk-store": "github:gofynd/fdk-store-gql#v3.0.47", "framer-motion": "^11.3.28", "google-libphonenumber": "^3.2.38", "html-react-parser": "^5.1.8", "imask": "^6.4.3", "lucide-react": "^0.511.0", "marked": "^13.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.3", "react-hook-form": "^7.52.0", "react-icons": "^5.5.0", "react-international-phone": "^4.3.0", "react-outside-click-handler": "^1.3.0", "react-range-slider-input": "^3.0.7", "react-refresh": "^0.14.2", "react-slick": "^0.30.2", "react-snap-carousel": "^0.3.2", "slick-carousel": "^1.8.1", "swiper": "^11.2.6", "url": "^0.11.0", "values.js": "^2.1.1"}, "theme_metadata": {"theme_type": "react"}}