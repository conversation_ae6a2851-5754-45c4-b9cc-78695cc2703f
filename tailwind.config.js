/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./theme/**/*.{js,jsx,ts,tsx}"],
  theme: {
    screens: {
      tablet: "768px",
      // => @media (min-width: 768px) { ... }

      desktopSmall: "1024px",
      // => @media (min-width: 1024px) { ... }

      desktop: "1280px",
      // => @media (min-width: 1280px) { ... }

      desktopLarge: "1440px",
      // => @media (min-width: 1440px) { ... }
      desktopExtraLarge: "1920px",
      // => @media (min-width: 1920px) { ... }
      desktopDoubleExtraLarge: "2560px",
      // => @media (min-width: 2560px) { ... }
    },
    extend: {
      fontFamily: {
        'helvetica-now-display': ['"Helvetica Now Display"', 'sans-serif'],
      },
      colors: {
        "boss-black": "#000",
        "boss-camel": "#987147",
        "boss-orange": "#eb6608",
        "boss-green": "#80ba27",
        hugo: "#b51f29",
        "dark-gray": "#707070",
        "middle-gray": "#b7b7b7",
        "light-gray": "#ededed",
        "primary-gray": "#f6f6f6",
        "delivered-tag": "#eafbed"
      },
      backgroundColor: {
        skin: {
          brand: `var(--brand-color)`,
          navbarBg: `var(--navbar-bg-color)`,
          navbarFlyout: `var(--navbar-flyout-color)`,
          cartCount: `var(--navbar-text-color)`,
          plpFilterBar: `var(--plp-filter-bar)`,
          plpFilterButtonBg: `var(--plp-filter-button-bg)`,
          plpRangeSlider: `var(--plp-range-slider-color)`,
        },
      },
      textColor: {
        skin: {
          navbarText: `var(--navbar-text-color)`,
          cartCountText: `var(--navbar-bg-color)`,
          navbarDesktopText: `var(--navbar-desktop-text-color)`,
          plpFilterText: `var(--plp-filter-text)`,
          plpFilterButtonText: `var(--plp-filter-button-text)`,
          plpFilterBarActiveCount: `var(--plp-filter-bar-active-icon)`,
        },
      },
      fill: {
        skin: {
          headerSvg: `var(--navbar-text-color)`,
          sortBySvg: `var(--plp-filter-text)`,
          plpFilterBarActiveIcon: `var(--plp-filter-bar-active-icon)`,
          plpFilterButtonIcon: `var(--plp-filter-button-text)`,
        },
      },
      stroke: {
        skin: {
          brandToggleArrowSvg: `var(--plp-filter-text)`,
        },
      },
      boxShadow: {
        custom: "-80px 0px 5px 0px rgba(0, 0, 0, 0.1)",
      },
      borderColor: {
        skin: {
          plpSortByBorder: `var(--plp-filter-text)`,
        },
      },
      outlineColor: {
        skin: {
          navbarOutline: `var(--navbar-text-color)`,
        },
      },
      animation: {
        "slide-right": "arrow-move 250ms ease-out normal",
      },
    },
  },
  plugins: [],
};