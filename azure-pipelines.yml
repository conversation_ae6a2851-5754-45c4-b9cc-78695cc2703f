trigger:
  branches:
    include:
      - "main"

resources:
  containers:
    - container: node18
      image: node:18-buster

pool:
  docker

variables:
- group: pipeline

jobs:
- job: GitHubSync
  container: node18
  steps:
  - checkout: self

  - script: |

      echo "This is a main branch merge. Sync starting..."
      BRANCH_NAME=main

      git config --global user.email "<EMAIL>"
      git config --global user.name "<PERSON><PERSON> Dafda"

      git clone https://$(GITHUB_USERNAME):$(GITHUB_PERSONAL_TOKEN)@github.com/gofynd/Turbo.git
      cd Turbo
      git checkout $BRANCH_NAME || git checkout -b $BRANCH_NAME
      rm -rf ./*
      cd ..

      cp -R `ls | grep -v "Turbo"` ./Turbo
      cp .gitignore.ci ./Turbo/.gitignore

      cd Turbo

      git add .
      git commit -m "[Auto Generated]"
      git push origin $BRANCH_NAME

    displayName: 'Sync with GitHub'
