# FDK React Theme

## Overview

FDK React Theme is a React Theme designed specifically for Fynd Commerce. It includes a collection of reusable components, tools, and utilities to streamline the development process. Its is built with Webpack to handle JavaScript, TypeScript, CSS, and other assets, making it efficient and easy to maintain.

## Features

- **Dynamic Entry Points:** Automatically includes all `.jsx` files from `pages` and `sections` directory.
- **Support for CSS and Less:** Handles both CSS modules and global CSS, as well as Less files.
- **Comprehensive Asset Management:** Supports various asset types including fonts and SVGs.
- **Powerful Plugins:** Utilizes `MiniCssExtractPlugin` for efficient build processes.
- **Advanced Optimization:** Configured with `CssMinimizerPlugin` for CSS minification and faster load times in production.

## Development

### Prerequisites

Before you begin creating a theme, you need:

1. To [create](https://partners.fynd.com/help/docs/guide/become-fynd-partner) or have access to a Partner account on Fynd Partners. To have at least 1 development account or live account available in your partner organization.

2. [Click here](https://partners.fynd.com/help/docs/guide/partner-panel/accounts) to know how to create development account or get access to a live account through your partner panel. It is recommended to use development account for this tutorial.

3. [FDK-CLI](https://github.com/gofynd/fdk-cli) latest version.

4. [Node.js](https://nodejs.org/en) v16.19 or above.

5. [Git](https://git-scm.com/) installed.

6. [ReactJS](https://react.dev/).

### Initialize new theme using base theme template

1. **Install FDK CLI**

   ```bash
   npm install -g @gofynd/fdk-cli
   fdk --version
   ```

2. **Login your partner account into FDK/CLI**

   ```bash
      fdk login
   ```

   And after running the login command you will be redirected to the homepage of the partner’s panel of that specific env.

3. **Select Partner Organization**

   Select the Partner Organization through which you want to create your theme.

4. **Login Successful**

   As soon as you select the partner organization you will be redirected to the Login Successful page in browser. You will get User logged in successfully message in terminal

5. **Initialize a new theme**

   ```bash
      fdk theme new --name name-of-the-theme
   ```

6. **Select account type**

   Select account type as `development` or `live` depending on the account type you want to use for creating your theme.

7. **Select Account**

   FDK-CLI will list the development accounts available in your partner organization. Select the account in which you want to create the theme.

8. **Select Sales Channel**

   FDK-CLI will list the sales channels available in the account chosen. Select the sales channel in which you want to create your theme.

   A folder with the theme name will be created into your machine with theme code cloned into it.

9. **Navigate to your project folder**

   ```bash
      cd theme-name
   ```

10. **Preview your theme**

    Once you are into your theme’s directory, run the following command to serve your theme locally and preview your theme.

    ```bash
       fdk theme serve
    ```

**NOTE:** `fdk theme serve` wouldn't work without the `.fdk` folder setup, which needs the `fdk theme init` command.

For more information [click here](https://partners.fynd.com/help/docs/partners/themes/vuejs/theme-creation)

## Project Structure

- **theme/**: Source files including JavaScript/TypeScript, CSS, Less, and assets.
- **webpack.config.js**: Webpack configuration file.

## Webpack Configuration

### Entry Points

The entry points are dynamically generated by including all `.jsx` files from the `src` directory:

```javascript
entry: () => {
    const entryFiles = glob.sync('./src/**/*.jsx');
    const entry = {};
    entryFiles.forEach(file => {
        entry[file.replace('src', '')] = file;
    });
    return entry;
},
```

### Output

This specifies that the entry point for the Webpack build process is the `index.jsx` file located in the theme directory.:

```javascript
entry: {
  themeBundle: [path.resolve(context, "theme/index.jsx")],
}
```

### Plugins

The configuration includes several plugins to enhance the build process:

- [MiniCssExtractPlugin](https://www.npmjs.com/package/mini-css-extract-plugin):Extracts CSS into separate files, allowing for parallel loading of CSS and JavaScript.
- [NodeJSPolyfill](https://www.npmjs.com/package/node-polyfill-webpack-plugin): Adds polyfills for Node.js features to ensure compatibility in the browser environment.
- [Overlay](https://www.npmjs.com/package/react-hydration-overlay): Provides a development overlay for React hydration, useful for debugging during development.

### Loaders

Various loaders are configured to handle different types of files:

- [babel-loader](https://www.npmjs.com/package/babel-loader): Transpiles JavaScript and TypeScript files using Babel presets.
- [css-loader](https://www.npmjs.com/package/css-loader): Handles CSS files with support for CSS modules.
- [less-loader](https://www.npmjs.com/package/less-loader): Compiles Less files, with support for both modules and global styles.
- [svgr/webpack](https://www.npmjs.com/package/@svgr/webpack): Processes SVG files to be used as React components.
- `asset/resource`: Manages font files and other static assets.

### Optimization

The project uses `CssMinimizerPlugin to minimize CSS files, reducing their size for faster load times in production:

```javascript
  optimization: {
      minimizer: [`...`, new CssMinimizerPlugin()],
    },
```

## Global Configuration

The following table provides a detailed overview of all **Global Configurations** available for this component. These configurations allow for customization of typography, header, footer, product cards, and other storefront design elements. The settings are grouped into categories for easy reference and can be modified via the Fynd Platform.

| **Configuration**       | **Type**       | **Default Value**    | **Category**                | **Description**                                                                                                                         |
| ----------------------- | -------------- | -------------------- | --------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- |
| `font_header`           | `font`         | `false`              | Typography                  | Defines the font styling for header elements across the site.                                                                           |
| `font_body`             | `font`         | `false`              | Typography                  | Defines the font styling for body text content.                                                                                         |
| `header_layout`         | `select`       | `"single"`           | Header                      | Configures the layout of the header. Options include: **Single Row Navigation** and **Double Row Navigation**.                          |
| `logo_menu_alignment`   | `select`       | `"layout_1"`         | Header                      | Determines the alignment of the logo and menu on desktop. Options include: logo left/menu center, menu left, or menu right.             |
| `header_mega_menu`      | `checkbox`     | `false`              | Header                      | Enables the mega menu layout for navigation. This option is applicable only when the header layout is set to **Double Row Navigation**. |
| `extension`             | `extension`    | `{}`                 | Header                      | Allows you to add and manage extensions in specific positions within the header, such as before or after header icons.                  |
| `is_hyperlocal`         | `checkbox`     | `false`              | Header                      | Activates hyperlocal functionality to personalize content based on the user's location.                                                 |
| `is_delivery_minutes`   | `checkbox`     | `false`              | Header                      | Displays the delivery promise in terms of minutes on applicable pages.                                                                  |
| `max_delivery_min`      | `text`         | `"60"`               | Header                      | Specifies the threshold value (in minutes) for displaying the delivery promise.                                                         |
| `is_delivery_hours`     | `checkbox`     | `false`              | Header                      | Displays the delivery promise in terms of hours on applicable pages.                                                                    |
| `max_delivery_hours`    | `text`         | `"2"`                | Header                      | Specifies the threshold value (in hours) for displaying the delivery promise.                                                           |
| `is_delivery_day`       | `checkbox`     | `false`              | Header                      | Shows delivery promise as a simple **Today/Tomorrow** format.                                                                           |
| `is_delivery_date`      | `checkbox`     | `false`              | Header                      | Displays delivery promise as a date range. Useful for deliveries expected over multiple days.                                           |
| `logo`                  | `image_picker` | `""`                 | Footer                      | Allows uploading a custom logo for display in the footer.                                                                               |
| `footer_description`    | `text`         | `""`                 | Footer                      | Adds a text description in the footer area, typically used for branding or additional information.                                      |
| `payments_logo`         | `image_picker` | `""`                 | Footer                      | Allows adding an image in the footer to showcase payment options or any relevant footer image.                                          |
| `footer_image`          | `checkbox`     | `false`              | Footer                      | Enables an image to be displayed within the footer section.                                                                             |
| `footer_image_desktop`  | `image_picker` | `""`                 | Footer                      | Specifies an image to display in the footer for desktop devices.                                                                        |
| `footer_image_mobile`   | `image_picker` | `""`                 | Footer                      | Specifies an image to display in the footer for mobile and tablet devices.                                                              |
| `disable_cart`          | `checkbox`     | `false`              | Cart & Button Configuration | Disables the shopping cart and checkout functionality across the site.                                                                  |
| `show_price`            | `checkbox`     | `true`               | Cart & Button Configuration | Toggles the visibility of product prices on Product Cards, Product Details Pages (PDP), and Featured Product sections.                  |
| `button_options`        | `select`       | `"addtocart_buynow"` | Cart & Button Configuration | Configures the available options for product action buttons. Options include combinations of Add to Cart, Buy Now, or custom buttons.   |
| `custom_button_text`    | `text`         | `"Enquire now"`      | Cart & Button Configuration | Specifies the text to display on a custom button for specific product actions.                                                          |
| `custom_button_link`    | `url`          | `""`                 | Cart & Button Configuration | Adds a URL that the custom button will redirect to when clicked.                                                                        |
| `custom_button_icon`    | `image_picker` | `""`                 | Cart & Button Configuration | Allows uploading an icon for the custom button, applicable to Product Details Pages (PDP) and Featured Product sections.                |
| `product_img_width`     | `text`         | `""`                 | Product Card Configuration  | Configures the width of product card images. Applicable to product listing, detail, and featured sections.                              |
| `product_img_height`    | `text`         | `""`                 | Product Card Configuration  | Configures the height of product card images, maintaining their aspect ratio.                                                           |
| `show_sale_badge`       | `checkbox`     | `true`               | Product Card Configuration  | Displays a **Sale** badge on discounted products.                                                                                       |
| `image_border_radius`   | `range`        | `24`                 | Product Card Configuration  | Sets the corner radius for product images, enhancing the visual style.                                                                  |
| `img_fill`              | `checkbox`     | `false`              | Product Card Configuration  | Ensures the image fully fits its container by clipping parts of the image if necessary.                                                 |
| `img_container_bg`      | `color`        | `""`                 | Product Card Configuration  | Specifies the background color of image containers for products, collections, and categories.                                           |
| `show_image_on_hover`   | `checkbox`     | `false`              | Product Card Configuration  | Displays an additional image of the product when hovering over the product card.                                                        |
| `img_hd`                | `checkbox`     | `false`              | Other Page Configuration    | Enhances image quality by upscaling, which may affect page performance. Applicable on the homepage.                                     |
| `section_margin_bottom` | `range`        | `16`                 | Other Page Configuration    | Sets the bottom margin for page sections, useful for spacing adjustments.                                                               |
| `button_border_radius`  | `range`        | `4`                  | Other Page Configuration    | Defines the corner radius for buttons, improving their visual style and user experience.                                                |

### Steps to Modify Global Configuration via Fynd Platform

1. **Log in to the Fynd Platform:**

   - Go to [Fynd Platform](https://platform.fynd.com) and log in with your credentials.

2. **Navigate to Your Company:**

   - Once logged in, select your compay from the list.

3. **Select the Theme**

   - In the sidebar, under **Sales Channel**, select your sales channel.
   - Then, under **Appearance**, click on **Themes**.
   - In the current theme, click on **Edit**. Here, you can preview and configure the theme.  
     Here's a sample [theme](https://platform.fynd.com/company/5178/application/668765e1c984016d78222a21/themes/668768e7e21c099a562b5d56/edit).

4. **Locate Global Configuration Section:**

   - Within the Theme, find the **Settings** section under **Configuration**. This is where the configurations described are accessible.

5. **Modify Configurations:**

   - Select and update the configurations based on your requirements, such as:
     - Fonts under **Typography**.
     - Header layouts under **Header**.
     - Button styles under **Cart & Button Configuration**.

6. **Preview Changes:**

   - Preview the updates made to the page in real time to ensure they look and function as expected.

7. **Save and Publish:**

   - After confirming your changes, click on **Save**. This will publish the updated configurations.

8. **Test Your Storefront:**
   - Visit your store's live URL to confirm the updates are functioning as expected.

---

This README provides a detailed overview of the FDK React Theme, including installation, usage, and configuration details. Ensure to update any placeholders with actual information specific to your project.
