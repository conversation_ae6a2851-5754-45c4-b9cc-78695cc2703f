module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: ["airbnb", "airbnb/hooks", "prettier"],
  plugins: ["react", "react-hooks", "jsx-a11y", "prettier"],
  overrides: [],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: "module",
  },
  rules: {
    "prettier/prettier": "error",
    "react/jsx-filename-extension": [1, { extensions: [".js", ".jsx"] }],
    "react/react-in-jsx-scope": "off",
    "linebreak-style": 0,
    "import/prefer-default-export": "off",
    "react/jsx-props-no-spreading": "off",
    "import/no-unresolved": "off",
    "react/prop-types": "off",
    camelcase: "off",
    "no-unused-vars": "off",
    "react/no-danger": "off",
    "jsx-a11y/click-events-have-key-events": "off",
    "jsx-a11y/media-has-caption": "off",
    "react/no-array-index-key": "off",
    "react/require-default-props": "off",
    "import/no-extraneous-dependencies": "off",
    "consistent-return": "off",
    "no-useless-escape": "off",
    "react-hooks/exhaustive-deps": "off",
    "jsx-a11y/no-noninteractive-element-interactions": "off",
    "jsx-a11y/no-static-element-interactions": "off",
    "react/jsx-no-useless-fragment": "off",
    "react/jsx-boolean-value": "off",
    "no-else-return": "off",
    "no-param-reassign": "off",
    "react/function-component-definition": "off",
    "no-plusplus": "off",
    "react/self-closing-comp": "off",
    "no-shadow": "off",
    "arrow-body-style": "off",
    "no-use-before-define": "off",
    "global-require": "off",
    "no-restricted-syntax": "off",
    "guard-for-in": "off",
    "no-useless-constructor": "off",
    "no-empty-function": "off",
    "import/order": "off",
    "no-unsafe-optional-chaining": "off",
    "react/no-unknown-property": "off",
    "react/no-unstable-nested-components": "off",
    "react/button-has-type": "off",
  },
};
